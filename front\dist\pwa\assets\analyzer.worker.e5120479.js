(function(){"use strict";self.onmessage=t=>{const{type:e,data:n}=t.data;if(e==="init"){const o=JSON.parse(n.results),i=JSON.parse(n.config);C(o,i)}};function C(t,e){const n=new Map,o=new Map,i=t.length-1+e.lookAheadCount,p=O(t,e);let g=0;function y(f){for(let s=f;s<Math.min(t.length-e.lookAheadCount,f+100);s++){const m=G(t[s].numbers,e.firstGroupSize),a=Array.from(m);for(let r=s+1;r<t.length&&r-s<=e.maxRange;r++){const u=G(t[r].numbers,e.secondGroupSize),j=Array.from(u),k=r-s;for(let c=r+1;c-r<=e.maxRange&&!(c>i);c++){const P=c-r;for(const N of a)for(const h of j){const b=`${N.join(",")}-${h.join(",")}-${k}-${P}`,l=o.get(b)||{count:0,periods:[],isPredict:!1};c<t.length?(l.count++,l.periods.push({firstPeriod:t[s].period,secondPeriod:t[r].period,targetPeriod:t[c].period})):c===i&&(l.periods.push({firstPeriod:t[s].period,secondPeriod:t[r].period}),l.isPredict=!0),o.set(b,l)}}}}for(let s=f;s<Math.min(t.length-e.lookAheadCount,f+100);s++){const m=G(t[s].numbers,e.firstGroupSize),a=Array.from(m);for(let r=s+1;r<t.length&&r-s<=e.maxRange;r++){const u=G(t[r].numbers,e.secondGroupSize),j=Array.from(u),k=r-s;for(let c=r+1;c-r<=e.maxRange&&!(c>i);c++){const P=c-r;let N=[];if(c<t.length){const h=G(t[c].numbers,e.targetGroupSize);N=Array.from(h)}for(const h of a)for(const b of j){const l=`${h.join(",")}-${b.join(",")}-${k}-${P}`;if(!!(o.get(l)||{count:0,periods:[],isPredict:!1}).isPredict&&c<t.length)for(const S of N){const z=`${h.join(",")}-${b.join(",")}-${k}-${P}-${S.join(",")}`,A=n.get(z);if(A)A.targetMatches++;else{const x={firstNumbers:h,secondNumbers:b,targetNumbers:S,gap:k,targetGap:P,targetMatches:1,targetProbability:0,rank:0};n.set(z,x)}}}g++,R(g,p)}}}R(g,p),f+100<t.length-e.lookAheadCount?setTimeout(()=>y(f+100),0):$()}function $(){var m;const f=[],d=new Map;for(const a of n.values()){const r=`${a.firstNumbers.join(",")}-${a.secondNumbers.join(",")}-${a.gap}-${a.targetGap}`,u=o.get(r);!(u!=null&&u.isPredict)||!u||u.count<=0||(a.targetProbability=a.targetMatches/u.count,!(a.targetProbability<.5||u.count<2)&&(d.has(a.targetMatches)||d.set(a.targetMatches,[]),(m=d.get(a.targetMatches))==null||m.push(a),f.push(a)))}const s=w(f,o);postMessage({type:"complete",data:s,occurrences:o,matchData:d})}y(0)}function*M(t,e,n=0,o=[]){if(o.length===e){yield[...o];return}for(let i=n;i<t.length;i++)o.push(t[i]),yield*M(t,e,i+1,o),o.pop()}function G(t,e){return M(t,e)}function O(t,e){const n=t.length-1+e.lookAheadCount;let o=0;for(let i=0;i<t.length-e.lookAheadCount;i++)for(let p=i+1;p<t.length&&p-i<=e.maxRange;p++)for(let g=p+1;g<t.length&&g-p<=e.maxRange&&!(g>n);g++)o++;return o}function w(t,e){return t.sort((n,o)=>{var d,s;const i=o.targetProbability-n.targetProbability;if(i!==0)return i;const p=((d=e.get(n.firstNumbers.join(",")+"-"+n.secondNumbers.join(",")+"-"+n.gap))==null?void 0:d.count)||0,y=(((s=e.get(o.firstNumbers.join(",")+"-"+o.secondNumbers.join(",")+"-"+o.gap))==null?void 0:s.count)||0)-p;if(y!==0)return y;const $=n.firstNumbers&&n.firstNumbers.length>0?n.firstNumbers[0]:1/0,f=o.firstNumbers&&o.firstNumbers.length>0?o.firstNumbers[0]:1/0;return $-f}),t.map((n,o)=>(n.rank=o+1,n))}function R(t,e){postMessage({type:"progress",data:{stage:"processing",progress:t,total:e}})}})();
