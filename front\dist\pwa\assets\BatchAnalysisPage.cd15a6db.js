import{Q as t}from"./QSelect.a87fec46.js";import{k as ne,r as i,c as W,b as de,I as c,J as D,L as b,M as a,a3 as R,P as l,a4 as ie,az as A,aA as k,aB as V,a6 as me,a8 as S,O as L,a2 as j,af as B}from"./index.5cb20bf8.js";import{u as ve,Q as pe}from"./useLotteryAnalysis.b196cc0f.js";import{Q as re}from"./QLinearProgress.7b973567.js";import{Q as ce}from"./QPage.070ae342.js";import{L as E}from"./lotto.3734c012.js";import{h as be}from"./error-handler.cb520886.js";import{_ as ge}from"./plugin-vue_export-helper.21dcd24c.js";import"./QItem.ecbdb62a.js";const _e={class:"row q-mb-lg"},Ve={class:"col-12 col-sm-6"},Ce={class:"q-pa-sm"},xe={class:"row q-mb-lg"},ye={class:"col-12 col-sm-6"},Fe={class:"q-pa-sm"},we={class:"col-12 col-sm-6"},fe={class:"q-pa-sm"},he={class:"row q-mb-lg"},qe={class:"col-12 col-sm-6"},De={class:"q-pa-sm"},Ae={class:"col-12 col-sm-6"},Be={class:"q-pa-sm"},Ee={key:0,class:"row q-mb-lg"},Ne={class:"col-12"},Ue={class:"row q-mb-md"},Re={class:"col-12 col-sm-4 q-pa-sm"},ke={class:"col-12 col-sm-4 q-pa-sm"},Se={class:"col-12 col-sm-4 q-pa-sm"},Le={class:"row q-mb-md"},ze={class:"col-12 col-sm-4"},Oe={class:"q-pa-sm"},Pe={class:"col-12 col-sm-4"},Qe={class:"q-pa-sm"},Me={class:"col-12 col-sm-4"},Te={class:"q-pa-sm"},$e={class:"row q-mb-md"},Ge={class:"col-12 col-sm-4 q-pa-sm"},Ie={class:"col-12 col-sm-4 q-pa-sm"},We={class:"col-12 col-sm-4 q-pa-sm"},je={class:"row q-mb-md"},Je={class:"col-12 col-sm-4"},He={class:"q-pa-sm"},Ke={class:"col-12 col-sm-4"},Xe={class:"q-pa-sm"},Ye={class:"col-12 col-sm-4"},Ze={class:"q-pa-sm"},el={class:"row q-mb-md"},ll={class:"col-12 col-sm-4 q-pa-sm"},ol={class:"col-12 col-sm-4 q-pa-sm"},al={class:"col-12 col-sm-4 q-pa-sm"},ul={class:"row q-mb-md"},tl={class:"col-12 col-sm-4 q-pa-sm"},sl={class:"col-12 col-sm-4 q-pa-sm"},nl={class:"col-12 col-sm-4 q-pa-sm"},dl={class:"row q-mb-md"},il={class:"col-12 col-sm-4"},ml={class:"q-pa-sm"},vl={class:"col-12 col-sm-4"},pl={class:"q-pa-sm"},rl={class:"text-center q-mb-sm"},cl={class:"row q-mb-md"},bl={class:"col-12 col-sm-6"},gl={class:"text-subtitle1"},_l={class:"text-subtitle1"},Vl={class:"col-12 col-sm-6 text-right"},Cl=ne({__name:"BatchAnalysisPage",setup(xl){const w=ve(),J=[{label:"\u7248\u8DEF\u5206\u6790",value:"ball-follow"},{label:"\u5C3E\u6578\u5206\u6790",value:"tail"},{label:"\u7D9C\u5408\u5206\u6790",value:"pattern"}],H=[{label:"\u5A01\u529B\u5F69",value:"super_lotto638"},{label:"\u5927\u6A02\u900F",value:"lotto649"},{label:"\u4ECA\u5F69539",value:"daily539"},{label:"\u516D\u5408\u5F69",value:"lotto_hk"}],K=[{label:"Excel (.xlsx)",value:"excel"},{label:"CSV (.csv)",value:"csv"}],p=i("ball-follow"),r=i("super_lotto638"),F=i(""),g=i(50),f=i("excel"),_=i(!1),h=i(0),C=i(""),z=i([]),X=W(()=>new Date().toISOString().split("T")[0]),O=W(()=>p.value&&r.value&&F.value&&g.value>0&&!_.value),n=i({num1:1,num2:1,num3:1,periodNum:50,maxRange:20,aheadNum:1}),d=i({num1:1,num2:1,num3:1,periodNum:50,maxRange:20,aheadNum:1}),u=i({comb1:1,comb2:1,comb3:1,tailComb1:1,tailComb2:1,tailComb3:1,period:50,ahead:1}),x=[{label:"\u4E00\u661F\u7D44\u5408",value:1},{label:"\u4E8C\u661F\u7D44\u5408",value:2},{label:"\u4E09\u661F\u7D44\u5408",value:3}],y=[{label:"\u4E00\u661F\u7D44\u5408",value:1},{label:"\u4E8C\u661F\u7D44\u5408",value:2},{label:"\u4E09\u661F\u7D44\u5408",value:3},{label:"\u56DB\u661F\u7D44\u5408",value:4},{label:"\u4E94\u661F\u7D44\u5408",value:5}],P=i(Array.from({length:491},(s,e)=>({label:`${e+10}\u671F`,value:e+10}))),Q=i(Array.from({length:21},(s,e)=>({label:`${e+10}\u671F`,value:e+10}))),N=i(Array.from({length:15},(s,e)=>({label:`\u4E0B${e+1}\u671F`,value:e+1}))),M=i(Array.from({length:991},(s,e)=>({label:`${e+10}\u671F`,value:e+10}))),Y=s=>({"ball-follow":"\u7248\u8DEF\u5206\u6790",tail:"\u5C3E\u6578\u5206\u6790",pattern:"\u7D9C\u5408\u5206\u6790"})[s]||s,Z=s=>({super_lotto638:"\u5A01\u529B\u5F69",lotto649:"\u5927\u6A02\u900F",daily539:"\u4ECA\u5F69539",lotto_hk:"\u516D\u5408\u5F69"})[s]||s,ee=()=>{},T=()=>{switch(p.value){case"ball-follow":return{comb1:n.value.num1,comb2:n.value.num2,comb3:n.value.num3,periodNum:n.value.periodNum,maxRange:n.value.maxRange,aheadNum:n.value.aheadNum};case"tail":return{tailComb1:d.value.num1,tailComb2:d.value.num2,tailComb3:d.value.num3,periodNum:d.value.periodNum,maxRange:d.value.maxRange,aheadNum:d.value.aheadNum};case"pattern":return{comb1:u.value.comb1,comb2:u.value.comb2,comb3:u.value.comb3,tailComb1:u.value.tailComb1,tailComb2:u.value.tailComb2,tailComb3:u.value.tailComb3,period:u.value.period,ahead:u.value.ahead};default:return{comb1:1,comb2:1,comb3:1,tailComb1:1,tailComb2:1,tailComb3:1,periodNum:50,maxRange:20,aheadNum:1}}},U=i(!1),le=async()=>{if(!O.value){B.create({type:"warning",message:"\u8ACB\u5B8C\u6574\u586B\u5BEB\u6240\u6709\u5FC5\u8981\u53C3\u6578"});return}try{_.value=!0,U.value=r.value==="super_lotto638",h.value=0,C.value="\u6E96\u5099\u958B\u59CB...";const e=(await E.getLottoList({draw_type:r.value,date_end:F.value,limit:g.value})).data;C.value="\u6B63\u5728\u9032\u884C\u5206\u6790...";let o=0;for(const v of e){h.value+=1/e.length,C.value=`\u5206\u6790\u4E2D... ${++o}/${e.length}`;let m;switch(p.value){case"ball-follow":m=await E.getLottoList({draw_type:r.value,date_end:v.draw_date,limit:g.value}),await $(m.data);break;case"tail":m=await E.getLottoList({draw_type:r.value,date_end:v.draw_date,limit:g.value}),await G(m.data);break;case"pattern":m=await E.getLottoList({draw_type:r.value,date_end:v.draw_date,limit:g.value}),await $(m.data),await G(m.data);break}}C.value="\u5206\u6790\u5B8C\u6210\uFF01",h.value=1,B.create({type:"positive",position:"top",message:"\u5206\u6790\u5B8C\u6210\uFF01"})}catch(s){be(s)}finally{_.value=!1}},oe=()=>{_.value=!1,C.value="\u5206\u6790\u5DF2\u4E2D\u65B7",B.create({type:"warning",message:"\u5206\u6790\u5DF2\u4E2D\u65B7"})},$=s=>{const e=T(),o=s.map(v=>{const m=[...v.draw_number_size];return!U.value&&v.special_number&&m.push(v.special_number),{numbers:[...m],period:String(v.period)}});return w.init({firstGroupSize:e.comb1,secondGroupSize:e.comb2,targetGroupSize:e.comb3,maxRange:e.maxRange,lookAheadCount:e.aheadNum},o),w.analyzeWithProgress()},G=s=>{const e=T(),o=s.map(v=>{const m=new Set;for(let q of v.draw_number_size)m.add(q%10);!U.value&&v.special_number&&m.add(v.special_number%10);const se=Array.from(m).sort((q,I)=>q===0?1:I===0?-1:q-I);return{period:String(v.period),numbers:[...se]}});return w.init({firstGroupSize:e.tailComb1,secondGroupSize:e.tailComb2,targetGroupSize:e.tailComb3,maxRange:e.maxRange,lookAheadCount:e.aheadNum},o),w.analyzeWithProgress()},ae=()=>{try{f.value==="csv"?ue():te()}catch(s){console.error("\u4E0B\u8F09\u5931\u6557:",s),B.create({type:"negative",message:"\u6A94\u6848\u4E0B\u8F09\u5931\u6557"})}},ue=()=>{},te=()=>{};return de(()=>{F.value=new Date().toISOString().split("T")[0]}),(s,e)=>(c(),D(ce,{class:"justify-center"},{default:b(()=>[a(j,null,{default:b(()=>[a(R,null,{default:b(()=>[e[43]||(e[43]=l("div",{class:"text-h4 text-center q-mb-lg"},"\u5206\u6790\u5831\u8868",-1)),l("div",_e,[l("div",Ve,[e[25]||(e[25]=l("div",{class:"text-h6"},"\u5206\u6790\u65B9\u6CD5",-1)),l("div",Ce,[a(t,{outlined:"",dense:"",modelValue:p.value,"onUpdate:modelValue":e[0]||(e[0]=o=>p.value=o),options:J,"map-options":"","emit-value":"",class:"text-h6"},null,8,["modelValue"])])])]),l("div",xe,[l("div",ye,[e[26]||(e[26]=l("div",{class:"text-h6 text-weight-bold"},"\u5F69\u7A2E\u9078\u64C7",-1)),l("div",Fe,[a(t,{outlined:"",dense:"",modelValue:r.value,"onUpdate:modelValue":[e[1]||(e[1]=o=>r.value=o),ee],options:H,"emit-value":"","map-options":""},null,8,["modelValue"])])]),l("div",we,[e[27]||(e[27]=l("div",{class:"text-h6 text-weight-bold"},"\u53C3\u8003\u671F\u865F",-1)),l("div",fe,[a(ie,{outlined:"",dense:"",modelValue:F.value,"onUpdate:modelValue":e[2]||(e[2]=o=>F.value=o),type:"date",max:X.value,class:"text-h6"},null,8,["modelValue","max"])])])]),l("div",he,[l("div",qe,[e[28]||(e[28]=l("div",{class:"text-h6 text-weight-bold"},"\u63A8\u7B97\u671F\u6578",-1)),l("div",De,[a(t,{outlined:"",dense:"",modelValue:g.value,"onUpdate:modelValue":e[3]||(e[3]=o=>g.value=o),options:M.value,"emit-value":"","map-options":""},null,8,["modelValue","options"])])]),l("div",Ae,[e[29]||(e[29]=l("div",{class:"text-h6 text-weight-bold"},"\u8F38\u51FA\u683C\u5F0F",-1)),l("div",Be,[a(t,{outlined:"",dense:"",modelValue:f.value,"onUpdate:modelValue":e[4]||(e[4]=o=>f.value=o),options:K,"emit-value":"","map-options":""},null,8,["modelValue"])])])]),p.value?(c(),A("div",Ee,[l("div",Ne,[e[42]||(e[42]=l("div",{class:"text-h6 text-weight-bold q-mb-md"},"\u53C3\u6578\u8A2D\u5B9A",-1)),p.value==="ball-follow"?(c(),A(k,{key:0},[l("div",Ue,[e[30]||(e[30]=l("div",{class:"col-12 text-h6"},"\u62D6\u724C\u7D44\u5408",-1)),l("div",Re,[a(t,{outlined:"",dense:"",modelValue:n.value.num1,"onUpdate:modelValue":e[5]||(e[5]=o=>n.value.num1=o),options:x,"emit-value":"","map-options":""},null,8,["modelValue"])]),l("div",ke,[a(t,{outlined:"",dense:"",modelValue:n.value.num2,"onUpdate:modelValue":e[6]||(e[6]=o=>n.value.num2=o),options:x,"emit-value":"","map-options":""},null,8,["modelValue"])]),l("div",Se,[a(t,{outlined:"",dense:"",modelValue:n.value.num3,"onUpdate:modelValue":e[7]||(e[7]=o=>n.value.num3=o),options:x,"emit-value":"","map-options":""},null,8,["modelValue"])])]),l("div",Le,[l("div",ze,[e[31]||(e[31]=l("div",{class:"text-h6"},"\u63A8\u7B97\u671F\u6578",-1)),l("div",Oe,[a(t,{outlined:"",dense:"",modelValue:n.value.periodNum,"onUpdate:modelValue":e[8]||(e[8]=o=>n.value.periodNum=o),options:P.value,"emit-value":"","map-options":""},null,8,["modelValue","options"])])]),l("div",Pe,[e[32]||(e[32]=l("div",{class:"text-h6"},"\u6700\u5927\u5340\u9593",-1)),l("div",Qe,[a(t,{outlined:"",dense:"",modelValue:n.value.maxRange,"onUpdate:modelValue":e[9]||(e[9]=o=>n.value.maxRange=o),options:Q.value,"emit-value":"","map-options":""},null,8,["modelValue","options"])])]),l("div",Me,[e[33]||(e[33]=l("div",{class:"text-h6"},"\u9810\u6E2C\u671F\u6578",-1)),l("div",Te,[a(t,{outlined:"",dense:"",modelValue:n.value.aheadNum,"onUpdate:modelValue":e[10]||(e[10]=o=>n.value.aheadNum=o),options:N.value,"emit-value":"","map-options":""},null,8,["modelValue","options"])])])])],64)):V("",!0),p.value==="tail"?(c(),A(k,{key:1},[l("div",$e,[e[34]||(e[34]=l("div",{class:"col-12 text-h6"},"\u5C3E\u6578\u62D6\u724C\u7D44\u5408",-1)),l("div",Ge,[a(t,{outlined:"",dense:"",modelValue:d.value.num1,"onUpdate:modelValue":e[11]||(e[11]=o=>d.value.num1=o),options:y,"emit-value":"","map-options":""},null,8,["modelValue"])]),l("div",Ie,[a(t,{outlined:"",dense:"",modelValue:d.value.num2,"onUpdate:modelValue":e[12]||(e[12]=o=>d.value.num2=o),options:y,"emit-value":"","map-options":""},null,8,["modelValue"])]),l("div",We,[a(t,{outlined:"",dense:"",modelValue:d.value.num3,"onUpdate:modelValue":e[13]||(e[13]=o=>d.value.num3=o),options:y,"emit-value":"","map-options":""},null,8,["modelValue"])])]),l("div",je,[l("div",Je,[e[35]||(e[35]=l("div",{class:"text-h6"},"\u63A8\u7B97\u671F\u6578",-1)),l("div",He,[a(t,{outlined:"",dense:"",modelValue:d.value.periodNum,"onUpdate:modelValue":e[14]||(e[14]=o=>d.value.periodNum=o),options:P.value,"emit-value":"","map-options":""},null,8,["modelValue","options"])])]),l("div",Ke,[e[36]||(e[36]=l("div",{class:"text-h6"},"\u6700\u5927\u5340\u9593",-1)),l("div",Xe,[a(t,{outlined:"",dense:"",modelValue:d.value.maxRange,"onUpdate:modelValue":e[15]||(e[15]=o=>d.value.maxRange=o),options:Q.value,"emit-value":"","map-options":""},null,8,["modelValue","options"])])]),l("div",Ye,[e[37]||(e[37]=l("div",{class:"text-h6"},"\u9810\u6E2C\u671F\u6578",-1)),l("div",Ze,[a(t,{outlined:"",dense:"",modelValue:d.value.aheadNum,"onUpdate:modelValue":e[16]||(e[16]=o=>d.value.aheadNum=o),options:N.value,"emit-value":"","map-options":""},null,8,["modelValue","options"])])])])],64)):V("",!0),p.value==="pattern"?(c(),A(k,{key:2},[l("div",el,[e[38]||(e[38]=l("div",{class:"col-12 text-h6"},"\u62D6\u724C\u7D44\u5408",-1)),l("div",ll,[a(t,{outlined:"",dense:"",modelValue:u.value.comb1,"onUpdate:modelValue":e[17]||(e[17]=o=>u.value.comb1=o),options:x,"emit-value":"","map-options":""},null,8,["modelValue"])]),l("div",ol,[a(t,{outlined:"",dense:"",modelValue:u.value.comb2,"onUpdate:modelValue":e[18]||(e[18]=o=>u.value.comb2=o),options:x,"emit-value":"","map-options":""},null,8,["modelValue"])]),l("div",al,[a(t,{outlined:"",dense:"",modelValue:u.value.comb3,"onUpdate:modelValue":e[19]||(e[19]=o=>u.value.comb3=o),options:x,"emit-value":"","map-options":""},null,8,["modelValue"])])]),l("div",ul,[e[39]||(e[39]=l("div",{class:"col-12 text-h6"},"\u5C3E\u6578\u62D6\u724C\u7D44\u5408",-1)),l("div",tl,[a(t,{outlined:"",dense:"",modelValue:u.value.tailComb1,"onUpdate:modelValue":e[20]||(e[20]=o=>u.value.tailComb1=o),options:y,"emit-value":"","map-options":""},null,8,["modelValue"])]),l("div",sl,[a(t,{outlined:"",dense:"",modelValue:u.value.tailComb2,"onUpdate:modelValue":e[21]||(e[21]=o=>u.value.tailComb2=o),options:y,"emit-value":"","map-options":""},null,8,["modelValue"])]),l("div",nl,[a(t,{outlined:"",dense:"",modelValue:u.value.tailComb3,"onUpdate:modelValue":e[22]||(e[22]=o=>u.value.tailComb3=o),options:y,"emit-value":"","map-options":""},null,8,["modelValue"])])]),l("div",dl,[l("div",il,[e[40]||(e[40]=l("div",{class:"text-h6"},"\u63A8\u7B97\u671F\u6578",-1)),l("div",ml,[a(t,{outlined:"",dense:"",modelValue:u.value.period,"onUpdate:modelValue":e[23]||(e[23]=o=>u.value.period=o),options:M.value,"emit-value":"","map-options":""},null,8,["modelValue","options"])])]),l("div",vl,[e[41]||(e[41]=l("div",{class:"text-h6"},"\u9810\u6E2C\u671F\u6578",-1)),l("div",pl,[a(t,{outlined:"",dense:"",modelValue:u.value.ahead,"onUpdate:modelValue":e[24]||(e[24]=o=>u.value.ahead=o),options:N.value,"emit-value":"","map-options":""},null,8,["modelValue","options"])])])])],64)):V("",!0)])])):V("",!0),a(me,{align:"right",class:"q-my-lg q-py-none q-px-md"},{default:b(()=>[_.value?(c(),D(S,{key:0,type:"button",label:"\u4E2D\u65B7\u8A08\u7B97",color:"negative",onClick:oe,class:"text-h6 q-mr-md"})):V("",!0),a(S,{type:"button",label:"\u7522\u751F\u5831\u8868",color:"positive",class:"text-h6 q-px-lg q-py-sm",onClick:le,loading:_.value,disable:!O.value},{loading:b(()=>[a(pe)]),_:1},8,["loading","disable"])]),_:1})]),_:1}),_.value?(c(),D(R,{key:0},{default:b(()=>[l("div",rl,L(C.value),1),a(re,{rounded:"",size:"md",value:h.value,"animation-speed":50,color:"primary",class:"q-mb-xs"},null,8,["value"])]),_:1})):V("",!0)]),_:1}),z.value.length>0?(c(),D(j,{key:0,class:"q-mt-lg"},{default:b(()=>[a(R,null,{default:b(()=>[e[44]||(e[44]=l("div",{class:"text-h6 text-weight-bold q-mb-md"},"\u5206\u6790\u7D50\u679C",-1)),l("div",cl,[l("div",bl,[l("div",gl," \u5206\u6790\u65B9\u6CD5\uFF1A"+L(Y(p.value)),1),l("div",_l," \u5F69\u7A2E\uFF1A"+L(Z(r.value)),1)]),l("div",Vl,[a(S,{color:"primary",icon:"download",label:`\u4E0B\u8F09 ${f.value.toUpperCase()} \u6A94\u6848`,onClick:ae,disable:z.value.length===0},null,8,["label","disable"])])])]),_:1})]),_:1})):V("",!0)]),_:1}))}});var El=ge(Cl,[["__scopeId","data-v-08038d0b"]]);export{El as default};
