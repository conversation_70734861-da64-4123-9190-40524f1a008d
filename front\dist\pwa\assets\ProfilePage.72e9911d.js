import{C as h,ar as A,at as S,c as V,h as D,Q as U,t as E,k as z,a1 as I,r,b as T,I as y,J as q,L as i,a9 as C,M as t,a3 as _,P as d,az as $,a4 as n,a8 as w,aD as N,a2 as j,af as Q}from"./index.5cb20bf8.js";import{Q as L}from"./QForm.b4bc5881.js";import{Q as M}from"./QPage.070ae342.js";import{h as b}from"./error-handler.cb520886.js";import{_ as R}from"./plugin-vue_export-helper.21dcd24c.js";const H=["text","rect","circle","QBtn","QBadge","QChip","QToolbar","QCheckbox","QRadio","QToggle","QSlider","QRange","QInput","QAvatar"],J=["wave","pulse","pulse-x","pulse-y","fade","blink","none"];var W=h({name:"QSkeleton",props:{...A,tag:{type:String,default:"div"},type:{type:String,validator:l=>H.includes(l),default:"rect"},animation:{type:String,validator:l=>J.includes(l),default:"wave"},animationSpeed:{type:[String,Number],default:1500},square:Boolean,bordered:Boolean,size:String,width:String,height:String},setup(l,{slots:p}){const m=E(),g=S(l,m.proxy.$q),v=V(()=>{const s=l.size!==void 0?[l.size,l.size]:[l.width,l.height];return{"--q-skeleton-speed":`${l.animationSpeed}ms`,width:s[0],height:s[1]}}),o=V(()=>`q-skeleton q-skeleton--${g.value===!0?"dark":"light"} q-skeleton--type-${l.type}`+(l.animation!=="none"?` q-skeleton--anim q-skeleton--anim-${l.animation}`:"")+(l.square===!0?" q-skeleton--square":"")+(l.bordered===!0?" q-skeleton--bordered":""));return()=>D(l.tag,{class:o.value,style:v.value},U(p.default))}});const G={key:1,class:"q-gutter-md"},K={class:"row"},O={class:"row justify-end q-gutter-sm"},X={class:"row justify-end q-gutter-sm"},Y=z({name:"ProfilePage",__name:"ProfilePage",setup(l){const p=I(),m=r(!1),g=r(!1),v=r(!1),o=r({id:0,uid:"",name:"",email:"",is_active:!1,expires_at:null,last_login_at:null,created_at:""}),s=r({name:"",email:""}),c=r({new_password:""}),f=r(""),B=u=>u?new Date(u).toLocaleString("zh-TW"):"\u7121",k=async()=>{try{m.value=!0;const u=await C.getUserProfile();o.value=u.data,o.value.created_at=B(u.data.created_at),o.value.last_login_at=B(u.data.last_login_at),o.value.expires_at=B(u.data.expires_at),s.value={name:u.data.name,email:u.data.email}}catch(u){b(u)}finally{m.value=!1}},F=async()=>{try{g.value=!0,await C.updateUserProfile(s.value),p.user&&(p.user.name=s.value.name),Q.create({message:"\u500B\u4EBA\u8CC7\u6599\u66F4\u65B0\u6210\u529F",color:"positive",timeout:2e3,position:"top"}),await k()}catch(u){b(u)}finally{g.value=!1}},P=async()=>{try{v.value=!0,await C.changePassword(c.value),Q.create({message:"\u5BC6\u78BC\u66F4\u65B0\u6210\u529F",color:"positive",timeout:3e3,position:"top"}),x()}catch(u){b(u)}finally{v.value=!1}},x=()=>{c.value={new_password:""},f.value=""};return T(()=>{k()}),(u,a)=>(y(),q(M,{class:"justify-center"},{default:i(()=>[t(j,{class:"q-mx-auto q-py-lg q-px-md",style:{"max-width":"600px"}},{default:i(()=>[t(_,null,{default:i(()=>a[8]||(a[8]=[d("div",{class:"text-h5 text-center text-weight-bold q-mb-lg"}," \u500B\u4EBA\u8CC7\u6599 ",-1)])),_:1}),t(_,{class:"q-gutter-md"},{default:i(()=>[a[9]||(a[9]=d("div",{class:"text-h6 text-weight-bold"},"\u57FA\u672C\u8CC7\u6599",-1)),m.value?(y(),q(W,{key:0,type:"rect",height:"200px"})):(y(),$("div",G,[t(n,{modelValue:o.value.uid,"onUpdate:modelValue":a[0]||(a[0]=e=>o.value.uid=e),label:"\u5E33\u865F",outlined:"",readonly:"",class:"bg-grey-1"},null,8,["modelValue"]),t(n,{modelValue:s.value.name,"onUpdate:modelValue":a[1]||(a[1]=e=>s.value.name=e),label:"\u59D3\u540D",outlined:"",rules:[e=>!!e||"\u8ACB\u8F38\u5165\u59D3\u540D"]},null,8,["modelValue","rules"]),t(n,{modelValue:s.value.email,"onUpdate:modelValue":a[2]||(a[2]=e=>s.value.email=e),label:"\u96FB\u5B50\u90F5\u4EF6",outlined:"",type:"email"},null,8,["modelValue"]),d("div",K,[t(n,{modelValue:o.value.created_at,"onUpdate:modelValue":a[3]||(a[3]=e=>o.value.created_at=e),label:"\u8A3B\u518A\u6642\u9593",outlined:"",readonly:"",class:"bg-grey-1 col q-mr-sm"},null,8,["modelValue"]),t(n,{modelValue:o.value.last_login_at,"onUpdate:modelValue":a[4]||(a[4]=e=>o.value.last_login_at=e),label:"\u6700\u5F8C\u767B\u5165\u6642\u9593",outlined:"",readonly:"",class:"bg-grey-1 col"},null,8,["modelValue"])]),t(n,{modelValue:o.value.expires_at,"onUpdate:modelValue":a[5]||(a[5]=e=>o.value.expires_at=e),label:"\u5E33\u865F\u5230\u671F\u6642\u9593",outlined:"",readonly:"",class:"bg-grey-1"},null,8,["modelValue"]),d("div",O,[t(w,{color:"primary",label:"\u66F4\u65B0\u8CC7\u6599",loading:g.value,onClick:F},null,8,["loading"])])]))]),_:1}),t(N,{class:"q-my-lg"}),t(_,{class:"q-gutter-md"},{default:i(()=>[a[10]||(a[10]=d("div",{class:"text-h6 text-weight-bold"},"\u66F4\u6539\u5BC6\u78BC",-1)),t(L,{onSubmit:P,class:"q-gutter-md"},{default:i(()=>[t(n,{modelValue:c.value.new_password,"onUpdate:modelValue":a[6]||(a[6]=e=>c.value.new_password=e),label:"\u65B0\u5BC6\u78BC",type:"password",outlined:"",rules:[e=>!!e||"\u8ACB\u8F38\u5165\u65B0\u5BC6\u78BC",e=>e.length>=4||"\u5BC6\u78BC\u9577\u5EA6\u9700\u5927\u65BC 4"]},null,8,["modelValue","rules"]),t(n,{modelValue:f.value,"onUpdate:modelValue":a[7]||(a[7]=e=>f.value=e),label:"\u78BA\u8A8D\u65B0\u5BC6\u78BC",type:"password",outlined:"",rules:[e=>!!e||"\u8ACB\u78BA\u8A8D\u65B0\u5BC6\u78BC",e=>e===c.value.new_password||"\u5BC6\u78BC\u4E0D\u4E00\u81F4"]},null,8,["modelValue","rules"]),d("div",X,[t(w,{color:"negative",label:"\u91CD\u8A2D",flat:"",onClick:x}),t(w,{color:"primary",label:"\u66F4\u6539\u5BC6\u78BC",type:"submit",loading:v.value},null,8,["loading"])])]),_:1})]),_:1})]),_:1})]),_:1}))}});var ue=R(Y,[["__scopeId","data-v-c10efa96"]]);export{ue as default};
