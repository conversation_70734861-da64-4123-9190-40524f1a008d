const fs = require('fs');
const path = require('path');
const packageJson = require('../package.json');

// 生成版本信息
const versionInfo = {
  version: `v${packageJson.version}_${new Date().getTime().toString().slice(-6)}`,
  baseVersion: `v${packageJson.version}`,
  buildTime: new Date().toISOString(),
  timestamp: Date.now()
};

// 确保 dist/pwa 目录存在
const distDir = path.join(__dirname, '../dist/pwa');
if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, { recursive: true });
}

// 写入版本文件
const versionFilePath = path.join(distDir, 'version.json');
fs.writeFileSync(versionFilePath, JSON.stringify(versionInfo, null, 2));

console.log('版本文件已生成:', versionFilePath);
console.log('版本信息:', versionInfo);
