import{C as Ee,c as he,h as Ae,Q as Ce,k as be,r as b,b as ke,w as re,I as t,J as x,L as c,a2 as M,M as r,a3 as J,P as e,O as u,az as l,aC as q,aA as y,A as p,aB as P,a8 as te,ba as Q,N,b7 as qe,bb as De,a5 as Ne,aD as xe,a6 as $e}from"./index.5cb20bf8.js";import{Q as ee,c as ne}from"./QSelect.a87fec46.js";import{Q as Re,a as Ve}from"./QItem.ecbdb62a.js";import{u as Pe,Q as Se}from"./useLotteryAnalysis.b196cc0f.js";import{Q as Qe}from"./QLinearProgress.7b973567.js";import{Q as ze}from"./QPage.070ae342.js";import{Q as Le,a as ve,b as Me,c as _e,u as Ue}from"./QTabPanels.ec4a05ed.js";import{p as B,u as Be,_ as Ie}from"./IndexPage.82a3c419.js";import{L as ye}from"./lotto.3734c012.js";import{Q as we}from"./QPagination.c60c89d4.js";import{Q as Te}from"./QSpace.7a70be6f.js";import{_ as Fe}from"./plugin-vue_export-helper.21dcd24c.js";import"./QResizeObserver.55c7aa89.js";import"./touch.9135741d.js";import"./use-render-cache.3aae9b27.js";import"./QPopupProxy.73021305.js";var je=Ee({name:"QBtnGroup",props:{unelevated:Boolean,outline:Boolean,flat:Boolean,rounded:Boolean,square:Boolean,push:Boolean,stretch:Boolean,glossy:Boolean,spread:Boolean},setup(se,{slots:_}){const f=he(()=>{const A=["unelevated","outline","flat","rounded","square","push","stretch","glossy"].filter(D=>se[D]===!0).map(D=>`q-btn-group--${D}`).join(" ");return`q-btn-group row no-wrap${A.length!==0?" "+A:""}`+(se.spread===!0?" q-btn-group--spread":" inline")});return()=>Ae("div",{class:f.value},Ce(_.default))}});const Oe={class:"row q-gutter-y-md"},Ge={class:"col-12 col-sm-5 draw-title text-center"},We={class:"text-period"},Je={class:"text-draw-date"},Ze={class:"col-12 col-sm-7 self-center"},He={class:"row justify-center"},Ke={key:0,class:"col-auto"},Xe={class:"row q-my-md q-gutter-sm items-center"},Ye={class:"col-sm-auto"},et={class:"col-sm-auto"},tt={class:"col-sm-auto"},st={class:"row"},lt={class:"col-auto text-h6"},ut={key:0,class:"row q-my-md justify-center"},at={class:"row q-col-gutter-md"},ot={key:0,class:"col-12 q-my-sm"},rt={class:"row q-col-gutter-md items-center text-h6"},nt={class:"col-12 col-sm-4"},it={class:"col-12 col-sm-6"},dt={class:"row q-col-gutter-md"},ct={class:"col-auto"},pt={class:"col-auto"},mt={class:"col-12 col-sm-2 text-center"},vt={key:1,class:"row justify-center"},_t={class:"row q-mb-sm"},bt={class:"col text-h6 text-bold"},gt={class:"row q-col-gutter-sm"},ft={class:"row items-center"},yt={class:"ball"},wt={class:"row q-mb-sm"},ht={class:"col text-h6 text-bold"},Bt={class:"row q-col-gutter-sm"},Ft={class:"row items-center"},Et={class:"ball"},At={class:"row q-mb-sm"},Ct={class:"col text-h6 text-bold"},kt={class:"row q-col-gutter-sm"},qt={class:"ball"},Dt={class:"row q-col-gutter-xs"},Nt={class:"row items-center"},xt={class:"ball tail-number"},$t={class:"row q-mt-md"},Rt={class:"row q-mt-xl"},Vt={class:"row q-gutter-y-md"},Pt={class:"col-12 col-sm-4 draw-title text-center"},St={class:"text-period"},Qt={class:"text-draw-date"},zt={class:"col-12 col-sm-6 self-center"},Lt={class:"row justify-center"},Mt={key:0,class:"col-auto"},Ut=be({__name:"BallFollowResult",props:{maxNumber:{},isSuperLotto:{type:Boolean},drawResults:{},predictResult:{},results:{},occurrenceResults:{},pageSize:{}},emits:["view-details"],setup(se){const _=se,f=b("1"),A=b([{label:"\u6E96\u904E 1 \u6B21\u4EE5\u4E0A",value:1},{label:"\u6E96\u904E 2 \u6B21\u4EE5\u4E0A",value:2},{label:"\u6E96\u904E 3 \u6B21\u4EE5\u4E0A",value:3},{label:"\u6E96\u904E 4 \u6B21\u4EE5\u4E0A",value:4},{label:"\u6E96\u904E 5 \u6B21\u4EE5\u4E0A",value:5}]),D=b(1),Z=b(.5),le=b([{label:"\u6E96\u78BA\u7387 100%",value:1},{label:"\u6E96\u78BA\u7387 90% \u4EE5\u4E0A",value:.9},{label:"\u6E96\u78BA\u7387 80% \u4EE5\u4E0A",value:.8},{label:"\u6E96\u78BA\u7387 70% \u4EE5\u4E0A",value:.7},{label:"\u6E96\u78BA\u7387 60% \u4EE5\u4E0A",value:.6},{label:"\u6E96\u78BA\u7387 50% \u4EE5\u4E0A",value:.5}]),C=b("count"),ie=b([{label:"\u6E96\u78BA\u6B21\u6578\u7D71\u8A08",value:"count"},{label:"\u9810\u6E2C\u7D44\u6578\u7D71\u8A08",value:"group"}]),L=b(1),F=b({pageItems:[],targetNumAppearances:new Map,tailNumAppearances:new Map,totalCount:0,totalPages:0}),T=b(!1);ke(async()=>{T.value=!1,a(),U(),T.value=!0}),re(()=>D.value,()=>{!T.value||U()}),re(()=>Z.value,()=>{!T.value||U()}),re(()=>C.value,()=>{!T.value||U()}),re(()=>L.value,()=>{!T.value||H()});const a=()=>{D.value=1,L.value=1},g=m=>{var s;const o=m.firstNumbers.join(",")+"-"+m.secondNumbers.join(",")+"-"+m.gap+"-"+m.targetGap;return((s=_.occurrenceResults.get(o))==null?void 0:s.count)||0},w=b([]),z=m=>{const o=[],s=Array.from(m);for(let n=1;n<=_.maxNumber;n++)s.includes(n)||o.push(n);return o},j=b([]),$=b(new Map),U=()=>{j.value=[],L.value=1,$.value.clear();const m=new Map,o=new Map;for(const i of _.results){let h=i.targetMatches>=D.value&&i.targetProbability>=Z.value;if(h&&j.value.push(i),C.value==="count")for(const k of i.targetNumbers){let E=i.targetMatches;if(h){m.set(k,(m.get(k)||0)+E);const ae=k%10;o.set(ae,(o.get(ae)||0)+E)}else for(const ae of i.targetNumbers)$.value.set(ae,($.value.get(ae)||0)+E)}else if(C.value==="group")for(const k of i.targetNumbers)if(h){m.set(k,(m.get(k)||0)+1);const E=k%10;o.set(E,(o.get(E)||0)+1)}else $.value.set(k,($.value.get(k)||0)+1)}const s=Array.from(m.entries()).sort((i,h)=>h[1]-i[1]);F.value.targetNumAppearances=new Map(s),w.value=z(F.value.targetNumAppearances.keys()),$.value=new Map(Array.from($.value.entries()).filter(i=>w.value.includes(i[0])).sort((i,h)=>h[1]-i[1]));for(const i of w.value)$.value.has(i)||$.value.set(i,0);const n=Array.from(o.entries()).sort((i,h)=>h[1]-i[1]);F.value.tailNumAppearances=new Map(n),H()},H=()=>{const m=(L.value-1)*_.pageSize,o=m+_.pageSize;F.value.pageItems=j.value.slice(m,o),F.value.totalCount=j.value.length,F.value.totalPages=Math.ceil(j.value.length/_.pageSize)},O=b([..._.drawResults].reverse()),G=b("asc"),K=m=>{m==="desc"?(G.value="desc",O.value=[..._.drawResults]):(G.value="asc",O.value=[..._.drawResults].reverse())},v=m=>{var s,n;if(!((n=(s=_.predictResult)==null?void 0:s.draw_number_size)!=null&&n.length))return!1;const o=[..._.predictResult.draw_number_size];return!_.isSuperLotto&&_.predictResult.special_number&&o.push(_.predictResult.special_number),o.includes(m)},ue=m=>{var o;return!((o=_.predictResult)!=null&&o.special_number)||_.isSuperLotto?!1:_.predictResult.special_number===m};return(m,o)=>(t(),x(M,{class:"q-mt-md"},{default:c(()=>[r(J,null,{default:c(()=>[r(Le,{modelValue:f.value,"onUpdate:modelValue":o[0]||(o[0]=s=>f.value=s),dense:"",align:"justify",class:"text-h6","active-color":"primary","indicator-color":"primary"},{default:c(()=>[r(ve,{name:"1",label:"\u5206\u6790\u7D50\u679C"}),r(ve,{name:"2",label:"\u9810\u6E2C\u7D50\u679C"}),r(ve,{name:"3",label:"\u958B\u734E\u7D50\u679C"})]),_:1},8,["modelValue"]),m.predictResult.period?(t(),x(M,{key:0,bordered:"",class:"ball-card full-width q-my-lg"},{default:c(()=>[r(J,null,{default:c(()=>[e("div",Oe,[e("div",Ge,[o[9]||(o[9]=e("div",{class:"text-h6"},"\u9810\u6E2C\u958B\u734E\u7D50\u679C",-1)),e("div",We,"\u7B2C "+u(m.predictResult.period)+" \u671F",1),e("div",Je," \u958B\u734E\u65E5\u671F\uFF1A"+u(m.predictResult.draw_date),1)]),e("div",Ze,[e("div",He,[(t(!0),l(y,null,q(m.predictResult.draw_number_size,s=>(t(),l("div",{key:s,class:"col-auto"},[(t(),l("div",{class:"ball",key:s},u(p(B)(s)),1))]))),128)),m.predictResult.special_number?(t(),l("div",Ke,[(t(),l("div",{class:"ball special-number",key:m.predictResult.special_number},u(p(B)(m.predictResult.special_number)),1))])):P("",!0)])])])]),_:1})]),_:1})):P("",!0),f.value==="1"||f.value==="2"?(t(),l(y,{key:1},[e("div",Xe,[o[10]||(o[10]=e("div",{class:"col-sm-auto text-h6"},"\u7BE9\u9078",-1)),e("div",Ye,[r(ee,{outlined:"",dense:"",modelValue:D.value,"onUpdate:modelValue":o[1]||(o[1]=s=>D.value=s),options:A.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])]),e("div",et,[r(ee,{outlined:"",dense:"",modelValue:Z.value,"onUpdate:modelValue":o[2]||(o[2]=s=>Z.value=s),options:le.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])]),e("div",tt,[r(ee,{outlined:"",dense:"",modelValue:C.value,"onUpdate:modelValue":o[3]||(o[3]=s=>C.value=s),options:ie.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])])]),e("div",st,[e("div",lt," \u5171 "+u(F.value.totalCount)+" \u7B46\u8CC7\u6599 ",1)])],64)):P("",!0),r(Me,{modelValue:f.value,"onUpdate:modelValue":o[8]||(o[8]=s=>f.value=s)},{default:c(()=>[r(_e,{name:"1"},{default:c(()=>[F.value.totalPages>1?(t(),l("div",ut,[r(we,{modelValue:L.value,"onUpdate:modelValue":o[4]||(o[4]=s=>L.value=s),max:F.value.totalPages,input:!0},null,8,["modelValue","max"])])):P("",!0),e("div",at,[F.value.pageItems.length===0?(t(),l("div",ot,[r(M,{flat:"",bordered:""},{default:c(()=>[r(J,null,{default:c(()=>o[11]||(o[11]=[e("div",{class:"text-h6 text-center"},"\u6C92\u6709\u7B26\u5408\u689D\u4EF6\u7684\u7D50\u679C",-1)])),_:1})]),_:1})])):P("",!0),(t(!0),l(y,null,q(F.value.pageItems,(s,n)=>(t(),l("div",{key:n,class:"col-12 q-my-sm"},[r(M,{flat:"",bordered:""},{default:c(()=>[r(J,null,{default:c(()=>[e("div",rt,[e("div",nt,[e("div",null,[o[12]||(o[12]=N(" \u958B\u51FA ")),(t(!0),l(y,null,q(s.firstNumbers,i=>(t(),x(ne,{key:i,color:"primary","text-color":"white",size:"lg",dense:""},{default:c(()=>[N(u(p(B)(i)),1)]),_:2},1024))),128)),N(" \u4E0B"+u(s.gap)+"\u671F\u958B ",1),(t(!0),l(y,null,q(s.secondNumbers,i=>(t(),x(ne,{key:i,color:"secondary","text-color":"white",size:"lg",dense:""},{default:c(()=>[N(u(p(B)(i)),1)]),_:2},1024))),128)),N(" \u518D\u4E0B"+u(s.targetGap)+"\u671F\u9810\u6E2C\u62D6\u51FA ",1),(t(!0),l(y,null,q(s.targetNumbers,i=>(t(),x(ne,{key:i,color:"accent","text-color":"white",size:"lg",dense:""},{default:c(()=>[N(u(p(B)(i)),1)]),_:2},1024))),128))])]),e("div",it,[e("div",dt,[e("div",ct," \u958B\u51FA"+u(g(s))+"\u6B21\uFF0C\u5171\u6E96"+u(s.targetMatches)+"\u6B21 ",1),e("div",pt," \u6E96\u78BA\u7387: "+u((s.targetProbability*100).toFixed(2))+"% ",1)])]),e("div",mt,[r(te,{dense:"",color:"primary",icon:"visibility",label:"\u6AA2\u8996\u8A73\u60C5",class:"text-subtitle1 text-bold",onClick:i=>m.$emit("view-details",s)},null,8,["onClick"])])])]),_:2},1024)]),_:2},1024)]))),128))]),F.value.totalPages>1?(t(),l("div",vt,[r(we,{modelValue:L.value,"onUpdate:modelValue":o[5]||(o[5]=s=>L.value=s),max:F.value.totalPages,input:!0},null,8,["modelValue","max"])])):P("",!0)]),_:1}),r(_e,{name:"2"},{default:c(()=>{var s;return[e("div",_t,[e("label",bt," \u9810\u6E2C\u865F\u78BC\uFF08"+u(((s=Array.from(F.value.targetNumAppearances.keys()))==null?void 0:s.length)||0)+"\u7B46\uFF09 ",1)]),r(M,{flat:"",bordered:"",class:"q-pa-sm q-mb-md text-subtitle1 appearance"},{default:c(()=>[e("div",gt,[(t(!0),l(y,null,q(F.value.targetNumAppearances.keys(),n=>(t(),l("div",{class:Q(["col-4 col-md-2",{predict:v(n),"predict-special-number":ue(n)}]),key:n},[e("div",ft,[e("span",yt,u(p(B)(n)),1),N(" ("+u(F.value.targetNumAppearances.get(n))+"\u6B21) ",1)])],2))),128))])]),_:1}),e("div",wt,[e("label",ht," \u672A\u51FA\u73FE\u865F\u78BC - \u4F9D\u9810\u6E2C\u6B21\u6578\u6392\u5E8F\uFF08"+u(w.value.length)+"\u7B46) ",1)]),r(M,{flat:"",bordered:"",class:"q-pa-sm q-mb-md text-subtitle1 appearance"},{default:c(()=>[e("div",Bt,[(t(!0),l(y,null,q($.value.entries(),([n,i])=>(t(),l("div",{class:Q(["col-4 col-md-2",{predict:v(n),"predict-special-number":ue(n)}]),key:n},[e("div",Ft,[e("span",Et,u(p(B)(n)),1),N(" ("+u(i)+"\u6B21) ",1)])],2))),128))])]),_:1}),e("div",At,[e("label",Ct," \u672A\u51FA\u73FE\u865F\u78BC - \u4F9D\u5927\u5C0F\u6392\u5E8F\uFF08"+u(w.value.length)+"\u7B46) ",1)]),r(M,{flat:"",bordered:"",class:"q-pa-sm q-mb-md text-subtitle1 appearance"},{default:c(()=>[e("div",kt,[(t(!0),l(y,null,q(w.value,n=>(t(),l("div",{class:Q(["col-3 col-md-1",{predict:v(n),"predict-special-number":ue(n)}]),key:n},[e("span",qt,u(p(B)(n)),1)],2))),128))])]),_:1}),o[13]||(o[13]=e("div",{class:"row q-mb-sm"},[e("label",{class:"col text-h6 text-bold"},"\u9810\u6E2C\u5C3E\u6578\u78BC")],-1)),r(M,{flat:"",bordered:"",class:"q-pa-sm text-subtitle1 appearance"},{default:c(()=>[e("div",Dt,[(t(!0),l(y,null,q(F.value.tailNumAppearances.keys(),n=>{var i;return t(),l("div",{class:Q(["col-4 col-md-2",{predict:(i=m.predictResult.tailSet)==null?void 0:i.includes(n)}]),key:n},[e("div",Nt,[e("span",xt,u(n),1),N(" ("+u(F.value.tailNumAppearances.get(n))+"\u6B21) ",1)])],2)}),128))])]),_:1})]}),_:1}),r(_e,{name:"3"},{default:c(()=>[e("div",$t,[r(je,{spread:""},{default:c(()=>[r(te,{type:"button",icon:"arrow_downward",label:"\u7531\u820A\u5230\u65B0",color:"primary","text-color":"white",class:Q({"bg-secondary":G.value==="asc"}),onClick:o[6]||(o[6]=s=>K("asc"))},null,8,["class"]),r(te,{type:"button",icon:"arrow_upward",label:"\u7531\u65B0\u5230\u820A",color:"primary","text-color":"white",class:Q({"bg-secondary":G.value==="desc"}),onClick:o[7]||(o[7]=s=>K("desc"))},null,8,["class"])]),_:1})]),e("div",Rt,[(t(!0),l(y,null,q(O.value,s=>(t(),x(M,{bordered:"",key:s.period,class:"ball-card full-width q-mb-md"},{default:c(()=>[r(J,{class:"q-py-md"},{default:c(()=>[e("div",Vt,[e("div",Pt,[e("div",St,"\u7B2C "+u(s.period)+" \u671F",1),e("div",Qt," \u958B\u734E\u65E5\u671F\uFF1A"+u(s.draw_date),1)]),e("div",zt,[e("div",Lt,[(t(!0),l(y,null,q(s.draw_number_size,n=>(t(),l("div",{key:n,class:"col-auto"},[(t(),l("div",{class:"ball",key:n},u(p(B)(n)),1))]))),128)),s.special_number?(t(),l("div",Mt,[(t(),l("div",{class:"ball special-number",key:s.special_number},u(p(B)(s.special_number)),1))])):P("",!0)])])])]),_:2},1024)]),_:2},1024))),128))])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}))}});const It={class:"text-h6 q-mb-md"},Tt={class:"text-h6 q-mb-md"},jt={class:"reverse-order",style:{display:"flex","flex-direction":"column-reverse"}},Ot={class:"row text-h6 q-mb-md"},Gt={class:"col-auto"},Wt={class:"row balls"},Jt={key:0,class:"col-auto"},Zt={class:"row text-h6 q-mb-md"},Ht={class:"col-auto"},Kt={class:"row balls"},Xt={key:0,class:"col-auto"},Yt={key:0,class:"row text-h6"},es={class:"col-auto"},ts={class:"row balls"},ss={key:0,class:"col-auto"},ls={key:1,class:"predict-section"},us={class:"row text-h6 q-mt-sm"},as={class:"col-auto"},os={key:0,class:"predict-period"},rs={class:"predict-date"},ns={class:"row balls"},is={key:0,class:"col-auto"},ds={key:1,class:"col-auto pending-draw"},cs=be({__name:"BallFollowDetail",props:{modelValue:{type:Boolean},results:{},selectedDetail:{},occurrences:{},predictResult:{}},emits:["update:modelValue"],setup(se,{emit:_}){const f=se,A=Be(),D=he({get:()=>f.modelValue,set:a=>Z("update:modelValue",a)}),Z=_,le=a=>{if(!a)return"";const g=f.results.find(w=>w.period==a);return g==null?void 0:g.draw_date},C=a=>{if(!a)return{numbers:[],specialNumber:0};const g=f.results.find(w=>w.period==a);return{numbers:g==null?void 0:g.draw_number_size,specialNumber:g!=null&&g.special_number?Number(g.special_number):0}},ie=a=>{var w,z;if(!a)return 0;const g=a.firstNumbers.join(",")+"-"+a.secondNumbers.join(",")+"-"+a.gap+"-"+a.targetGap;return(z=(w=f.occurrences.get(g))==null?void 0:w.count)!=null?z:0},L=a=>{var w,z;if(!a)return[];const g=a.firstNumbers.join(",")+"-"+a.secondNumbers.join(",")+"-"+a.gap+"-"+a.targetGap;return(z=(w=f.occurrences.get(g))==null?void 0:w.periods)!=null?z:[]},F=a=>a.targetPeriod===void 0,T=()=>{Z("update:modelValue",!1)};return(a,g)=>(t(),x(qe,{modelValue:D.value,"onUpdate:modelValue":g[0]||(g[0]=w=>D.value=w)},{default:c(()=>[r(M,{style:{"max-width":"100%",width:"800px"}},{default:c(()=>[r(J,{class:"row items-center"},{default:c(()=>[g[1]||(g[1]=e("div",{class:"text-h6"},"\u8A73\u7D30\u8CC7\u6599",-1)),r(Te),r(te,{icon:"close",flat:"",round:"",dense:"",onClick:T})]),_:1}),r(J,{class:"q-pa-md"},{default:c(()=>{var w,z,j,$,U,H,O,G,K;return[e("div",It,[g[2]||(g[2]=N(" \u958B\u51FA ")),(t(!0),l(y,null,q((w=a.selectedDetail)==null?void 0:w.firstNumbers,v=>(t(),x(ne,{key:v,color:"primary","text-color":"white",class:"text-h6"},{default:c(()=>[N(u(p(B)(v)),1)]),_:2},1024))),128)),N(" \u4E0B"+u((z=a.selectedDetail)==null?void 0:z.gap)+"\u671F\u958B ",1),(t(!0),l(y,null,q((j=a.selectedDetail)==null?void 0:j.secondNumbers,v=>(t(),x(ne,{key:v,color:"secondary","text-color":"white",class:"text-h6"},{default:c(()=>[N(u(p(B)(v)),1)]),_:2},1024))),128)),N(" \u518D\u4E0B "+u(($=a.selectedDetail)==null?void 0:$.targetGap)+" \u671F\u9810\u6E2C\u62D6\u51FA ",1),(t(!0),l(y,null,q((U=a.selectedDetail)==null?void 0:U.targetNumbers,v=>(t(),x(ne,{key:v,color:"accent","text-color":"white",class:"text-h6"},{default:c(()=>[N(u(p(B)(v)),1)]),_:2},1024))),128))]),e("div",Tt," \u958B\u51FA"+u(ie(a.selectedDetail))+"\u6B21\uFF0C\u5171\u6E96"+u((O=(H=a.selectedDetail)==null?void 0:H.targetMatches)!=null?O:0)+"\u6B21\uFF0C\u6E96\u78BA\u7387: "+u((((K=(G=a.selectedDetail)==null?void 0:G.targetProbability)!=null?K:0)*100).toFixed(2))+"% ",1),e("div",jt,[(t(!0),l(y,null,q(L(a.selectedDetail),(v,ue)=>(t(),x(M,{key:ue,class:"q-mb-md",style:De({"background-color":F(v)?"#e8f5e8":"#fefefe"})},{default:c(()=>[r(J,null,{default:c(()=>{var m,o,s,n,i;return[e("div",Ot,[e("div",Gt,[e("div",null,"\u7B2C "+u(v.firstPeriod)+" \u671F",1),e("div",null," \u958B\u734E\u65E5\u671F\uFF1A"+u(le(v.firstPeriod)),1)]),e("div",Wt,[(t(!0),l(y,null,q(C(v.firstPeriod).numbers,(h,k)=>{var E;return t(),l("div",{class:"col-auto",key:k},[e("div",{class:Q(["ball",{"first-num":(E=a.selectedDetail)==null?void 0:E.firstNumbers.includes(h)}])},u(p(B)(h)),3)])}),128)),C(v.firstPeriod).specialNumber?(t(),l("div",Jt,[e("div",{class:Q(["ball special-number",{"first-num":((m=a.selectedDetail)==null?void 0:m.firstNumbers.includes(C(v.firstPeriod).specialNumber))&&!p(A).isSuperLotto}])},u(p(B)(C(v.firstPeriod).specialNumber)),3)])):P("",!0)])]),e("div",Zt,[e("div",Ht,[e("div",null,"\u7B2C "+u(v.secondPeriod)+" \u671F",1),e("div",null," \u958B\u734E\u65E5\u671F\uFF1A"+u(le(v.secondPeriod)),1)]),e("div",Kt,[(t(!0),l(y,null,q(C(v.secondPeriod).numbers,(h,k)=>{var E;return t(),l("div",{class:"col-auto",key:k},[e("div",{class:Q(["ball",{"second-num":(E=a.selectedDetail)==null?void 0:E.secondNumbers.includes(h)}])},u(p(B)(h)),3)])}),128)),C(v.secondPeriod).specialNumber?(t(),l("div",Xt,[e("div",{class:Q(["ball special-number",{"second-num":((o=a.selectedDetail)==null?void 0:o.secondNumbers.includes(C(v.secondPeriod).specialNumber))&&!p(A).isSuperLotto}])},u(p(B)(C(v.secondPeriod).specialNumber)),3)])):P("",!0)])]),v.targetPeriod?(t(),l("div",Yt,[e("div",es,[e("div",null,"\u7B2C "+u(v.targetPeriod)+" \u671F",1),e("div",null," \u958B\u734E\u65E5\u671F\uFF1A"+u((s=le(v.targetPeriod))!=null?s:"\u672A\u958B\u734E"),1)]),e("div",ts,[(t(!0),l(y,null,q(C(v.targetPeriod).numbers,(h,k)=>{var E;return t(),l("div",{class:"col-auto",key:k},[e("div",{class:Q(["ball",{"target-num":(E=a.selectedDetail)==null?void 0:E.targetNumbers.includes(h)}])},u(p(B)(h)),3)])}),128)),C(v.targetPeriod).specialNumber?(t(),l("div",ss,[e("div",{class:Q(["ball special-number",{"target-num":((n=a.selectedDetail)==null?void 0:n.targetNumbers.includes(C(v.targetPeriod).specialNumber))&&!p(A).isSuperLotto}])},u(p(B)(C(v.targetPeriod).specialNumber)),3)])):P("",!0)])])):(t(),l("div",ls,[e("div",us,[e("div",as,[a.predictResult.period?(t(),l("div",os," \u7B2C "+u(a.predictResult.period)+" \u671F ",1)):P("",!0),e("div",rs,[a.predictResult.period?(t(),l(y,{key:0},[N(" \u5BE6\u969B\u958B\u734E\u65E5\u671F\uFF1A"+u(a.predictResult.draw_date),1)],64)):(t(),l(y,{key:1},[N(" \u9810\u6E2C\u671F\u865F\uFF1A\u5C1A\u672A\u958B\u734E ")],64))])]),e("div",ns,[a.predictResult.period?(t(),l(y,{key:0},[(t(!0),l(y,null,q(a.predictResult.draw_number_size,(h,k)=>{var E;return t(),l("div",{class:"col-auto",key:k},[e("div",{class:Q(["ball",{"target-num":(E=a.selectedDetail)==null?void 0:E.targetNumbers.includes(h)}])},u(p(B)(h)),3)])}),128)),a.predictResult.special_number?(t(),l("div",is,[e("div",{class:Q(["ball special-number",{"target-num":((i=a.selectedDetail)==null?void 0:i.targetNumbers.includes(a.predictResult.special_number))&&!p(A).isSuperLotto}])},u(p(B)(a.predictResult.special_number)),3)])):P("",!0)],64)):(t(),l("div",ds,[r(Ne,{name:"schedule",size:"lg"}),g[3]||(g[3]=e("span",null,"\u5C1A\u672A\u958B\u734E",-1))]))])])]))]}),_:2},1024)]),_:2},1032,["style"]))),128))])]}),_:1})]),_:1})]),_:1},8,["modelValue"]))}});var ps=Fe(cs,[["__scopeId","data-v-20b290b8"]]);const ms={class:"row lto-ref q-mb-sm"},vs={class:"col-12 col-sm-4 self-center text-h6"},_s={class:"col-12 col-sm-6 self-center text-subtitle1"},bs={class:"row balls"},gs={class:"ball"},fs={key:0,class:"col-auto"},ys={class:"row q-mb-md"},ws={class:"col"},hs={key:1,class:"row q-mb-md"},Bs={class:"row q-mb-md"},Fs={class:"col-12 col-sm-4 q-pa-sm"},Es={class:"col-12 col-sm-4 q-pa-sm"},As={class:"col-12 col-sm-4 q-pa-sm"},Cs={class:"row q-mb-md"},ks={class:"col-12 col-sm-4"},qs={class:"q-pa-sm"},Ds={class:"col-12 col-sm-4"},Ns={class:"q-pa-sm"},xs={class:"col-12 col-sm-4"},$s={class:"q-pa-sm"},Rs={class:"text-center q-mb-sm"},Vs=be({__name:"BallFollowPage",setup(se){const _=Ue(),f=Be(),A=b(f.getLotto),D=b(!1),Z=()=>{D.value=!0},le=()=>{D.value=!1},C=()=>{D.value=!1};re(()=>f.getLotto,R=>{R&&(A.value=R)}),re(()=>{var R;return(R=A.value)==null?void 0:R.period},()=>{n.value=[]});const{analyzeWithProgress:ie,stopAnalyzer:L,setConfig:F,setResults:T}=Pe(),a=b([]),g=b(new Map),w=b(!1),z=b(null),j=R=>{z.value=R,w.value=!0},$=[{label:"\u4E00\u661F\u7D44\u5408",value:1},{label:"\u4E8C\u661F\u7D44\u5408",value:2},{label:"\u4E09\u661F\u7D44\u5408",value:3}],U=b(1),H=b(1),O=b(1);let G=b(Array.from({length:991},(R,d)=>({label:`${d+10}\u671F`,value:d+10})));const K=b(50),v=(R,d,X)=>{const Y=parseInt(R,10);(Y<10||Y>1e3)&&X(),d(()=>{G.value=Array.from({length:991},(W,oe)=>oe+10).filter(W=>W.toString().startsWith(R)).map(W=>({label:`${W.toString()}\u671F`,value:W}))})},ue=Array.from({length:21},(R,d)=>({label:`${d+10}\u671F`,value:d+10})),m=b(20),o=Array.from({length:15},(R,d)=>({label:`\u4E0B${d+1}\u671F`,value:d+1})),s=b(1),n=b([]),i=b({period:"",draw_date:"",draw_number_appear:[],draw_number_size:[],tails:new Map});let h=1;const k=b(49),E=b(!1),ae=async()=>{var R,d,X;try{_.startCalculating(),a.value=[],h=s.value,k.value=f.getMaxNumber,E.value=f.isSuperLotto;const Y=await ye.getLottoList({draw_type:f.getDrawType,date_end:(d=(R=A.value)==null?void 0:R.draw_date)!=null?d:"",limit:K.value});n.value=Y.data;const W=await ye.getLottoPredict({draw_type:f.getDrawType,draw_date:(X=f.getLotto)==null?void 0:X.draw_date,ahead_count:h});if(i.value=W.data,i.value.period){const S=new Set,I=[...i.value.draw_number_size];!E.value&&i.value.special_number&&I.push(i.value.special_number);for(const me of I)S.add(me%10);const V=Array.from(S).sort((me,fe)=>me===0?1:fe===0?-1:me-fe);i.value.tailSet=V}const oe=n.value.map(S=>{const I=[...S.draw_number_size];return S.special_number&&!f.isSuperLotto&&I.push(S.special_number),{numbers:[...I],period:String(S.period)}}).reverse();T(oe),F({firstGroupSize:U.value,secondGroupSize:H.value,targetGroupSize:O.value,maxRange:m.value,lookAheadCount:s.value});let de=Date.now();const pe=8,ce=await ie(async S=>{const I=Date.now();I-de>=pe&&(await _.updateProgress(S),de=I)},S=>{_.addWarning(S)});a.value=ce.data,g.value=ce.occurrences}catch(Y){console.error("\u6578\u64DA\u8F09\u5165\u5931\u6557:",Y)}finally{ge()}},ge=()=>{L(),_.stopCalculating()};return(R,d)=>(t(),x(ze,{class:"justify-center"},{default:c(()=>[r(M,null,{default:c(()=>[r(J,null,{default:c(()=>{var X,Y,W,oe,de,pe,ce,S,I;return[(X=p(f).getLotto)!=null&&X.draw_date?(t(),l(y,{key:0},[e("div",ms,[e("div",vs,[e("div",null,u(p(f).getDrawLabel),1),d[7]||(d[7]=e("span",null,"\u53C3\u8003\u671F\u865F\uFF1A",-1)),e("span",null,u((Y=A.value)==null?void 0:Y.period),1),e("span",null,"\uFF08"+u((W=A.value)==null?void 0:W.draw_date)+"\uFF09",1)]),e("div",_s,[e("div",bs,[(t(!0),l(y,null,q((oe=A.value)==null?void 0:oe.draw_number_size,V=>(t(),l("div",{class:"col-auto",key:V},[e("div",gs,u(p(B)(V)),1)]))),128)),(de=A.value)!=null&&de.special_number?(t(),l("div",fs,[(t(),l("div",{class:"ball special-number",key:(pe=A.value)==null?void 0:pe.special_number},u(p(B)((ce=A.value)==null?void 0:ce.special_number)),1))])):P("",!0)])])]),e("div",ys,[e("div",ws,[D.value?(t(),x(te,{key:1,type:"button",label:"\u53D6\u6D88\u9078\u64C7",color:"negative",class:"text-h6 q-ml-md",onClick:C})):(t(),x(te,{key:0,type:"button",label:"\u91CD\u65B0\u9078\u64C7",color:"primary",class:"text-h6 q-ml-md",onClick:Z}))])])],64)):(t(),l("div",hs,d[8]||(d[8]=[e("div",{class:"text-h6"},"\u203B\u8ACB\u9078\u64C7\u53C3\u8003\u671F\u865F",-1)]))),r(xe,{class:"q-mb-md"}),!D.value&&((S=p(f).getLotto)==null?void 0:S.draw_date)?(t(),l(y,{key:2},[d[14]||(d[14]=e("div",{class:"row q-mb-md"},[e("div",{class:"col-12 text-h5 text-weight-bolder text-center"}," \u7248\u8DEF\u5206\u6790\u8A2D\u5B9A ")],-1)),e("div",Bs,[d[9]||(d[9]=e("div",{class:"col-12 text-h6 text-weight-bold"},"\u62D6\u724C\u7D44\u5408",-1)),e("div",Fs,[r(ee,{outlined:"",dense:"",modelValue:U.value,"onUpdate:modelValue":d[0]||(d[0]=V=>U.value=V),options:$,"emit-value":"","map-options":""},null,8,["modelValue"])]),e("div",Es,[r(ee,{outlined:"",dense:"",modelValue:H.value,"onUpdate:modelValue":d[1]||(d[1]=V=>H.value=V),options:$,"emit-value":"","map-options":""},null,8,["modelValue"])]),e("div",As,[r(ee,{outlined:"",dense:"",modelValue:O.value,"onUpdate:modelValue":d[2]||(d[2]=V=>O.value=V),options:$,"emit-value":"","map-options":""},null,8,["modelValue"])])]),e("div",Cs,[e("div",ks,[d[11]||(d[11]=e("div",{class:"text-h6 text-weight-bold"},"\u63A8\u7B97\u671F\u6578",-1)),e("div",qs,[r(ee,{outlined:"",dense:"",modelValue:K.value,"onUpdate:modelValue":d[3]||(d[3]=V=>K.value=V),options:p(G),"input-debounce":"0","use-input":"","hide-selected":"","fill-input":"",onFilter:v,"emit-value":"","map-options":""},{"no-option":c(()=>[r(Re,null,{default:c(()=>[r(Ve,{class:"text-grey"},{default:c(()=>d[10]||(d[10]=[N(" \u7121\u53EF\u7528\u9078\u9805 ")])),_:1})]),_:1})]),_:1},8,["modelValue","options"])])]),e("div",Ds,[d[12]||(d[12]=e("div",{class:"text-h6 text-weight-bold"},"\u6700\u5927\u5340\u9593",-1)),e("div",Ns,[r(ee,{outlined:"",dense:"",modelValue:m.value,"onUpdate:modelValue":d[4]||(d[4]=V=>m.value=V),options:p(ue),"emit-value":"","map-options":""},null,8,["modelValue","options"])])]),e("div",xs,[d[13]||(d[13]=e("div",{class:"text-h6 text-weight-bold"},"\u9810\u6E2C\u671F\u6578",-1)),e("div",$s,[r(ee,{outlined:"",dense:"",modelValue:s.value,"onUpdate:modelValue":d[5]||(d[5]=V=>s.value=V),options:p(o),"emit-value":"","map-options":""},null,8,["modelValue","options"])])])]),r($e,{align:"right",class:"q-my-lg q-py-none q-px-md"},{default:c(()=>[p(_).isCalculating?(t(),x(te,{key:0,type:"button",label:"\u4E2D\u65B7\u8A08\u7B97",color:"negative",onClick:ge,class:"text-h6 q-mr-md"})):P("",!0),r(te,{type:"button",label:"\u958B\u59CB\u8A08\u7B97",color:"positive",class:"text-h6 q-px-lg q-py-sm",onClick:ae,loading:p(_).isCalculating},{loading:c(()=>[r(Se)]),_:1},8,["loading"])]),_:1})],64)):(t(),x(Ie,{key:3,"draw-type-query":p(f).drawType,"date-query":((I=A.value)==null?void 0:I.draw_date)||"","is-select-ref":!0,onSelectRef:le},null,8,["draw-type-query","date-query"]))]}),_:1}),p(_).isCalculating?(t(),x(J,{key:0},{default:c(()=>[e("div",Rs,u(p(_).progressMessage),1),r(Qe,{rounded:"",size:"md",value:p(_).progress,"animation-speed":50,color:"primary",class:"q-mb-xs"},null,8,["value"])]),_:1})):P("",!0)]),_:1}),!p(_).isCalculating&&n.value.length>0?(t(),x(Ut,{key:0,"max-number":k.value,"is-super-lotto":E.value,"draw-results":n.value,"predict-result":i.value,results:a.value,"occurrence-results":g.value,"page-size":50,onViewDetails:j},null,8,["max-number","is-super-lotto","draw-results","predict-result","results","occurrence-results"])):P("",!0),r(ps,{modelValue:w.value,"onUpdate:modelValue":d[6]||(d[6]=X=>w.value=X),results:n.value,"selected-detail":z.value,occurrences:g.value,"predict-result":i.value},null,8,["modelValue","results","selected-detail","occurrences","predict-result"])]),_:1}))}});var Ks=Fe(Vs,[["__scopeId","data-v-d50c406a"]]);export{Ks as default};
