import{C as q,c as u,h as f,Q as B,i as W,D as x,r as S,w,a as M,R as U,t as L,G as O,v as N,S as j,U as k,b as A,V as D,W as I,X as K,Y as G,Z as X,E as Y,_ as V,j as C,g as Z,F as J,$ as ee}from"./index.5cb20bf8.js";import{Q as F}from"./QResizeObserver.55c7aa89.js";var ae=q({name:"QToolbarTitle",props:{shrink:Boolean},setup(e,{slots:v}){const a=u(()=>"q-toolbar__title ellipsis"+(e.shrink===!0?" col-shrink":""));return()=>f("div",{class:a.value},B(v.default))}}),ie=q({name:"QToolbar",props:{inset:Boolean},setup(e,{slots:v}){const a=u(()=>"q-toolbar row no-wrap items-center"+(e.inset===!0?" q-toolbar--inset":""));return()=>f("div",{class:a.value,role:"toolbar"},B(v.default))}}),re=q({name:"QHeader",props:{modelValue:{type:Boolean,default:!0},reveal:Boolean,revealOffset:{type:Number,default:250},bordered:Boolean,elevated:Boolean,heightHint:{type:[String,Number],default:50}},emits:["reveal","focusin"],setup(e,{slots:v,emit:a}){const{proxy:{$q:l}}=L(),o=W(O,x);if(o===x)return console.error("QHeader needs to be child of QLayout"),x;const r=S(parseInt(e.heightHint,10)),c=S(!0),b=u(()=>e.reveal===!0||o.view.value.indexOf("H")!==-1||l.platform.is.ios&&o.isContainer.value===!0),g=u(()=>{if(e.modelValue!==!0)return 0;if(b.value===!0)return c.value===!0?r.value:0;const t=r.value-o.scroll.value.position;return t>0?t:0}),s=u(()=>e.modelValue!==!0||b.value===!0&&c.value!==!0),z=u(()=>e.modelValue===!0&&s.value===!0&&e.reveal===!0),y=u(()=>"q-header q-layout__section--marginal "+(b.value===!0?"fixed":"absolute")+"-top"+(e.bordered===!0?" q-header--bordered":"")+(s.value===!0?" q-header--hidden":"")+(e.modelValue!==!0?" q-layout--prevent-focus":"")),p=u(()=>{const t=o.rows.value.top,h={};return t[0]==="l"&&o.left.space===!0&&(h[l.lang.rtl===!0?"right":"left"]=`${o.left.size}px`),t[2]==="r"&&o.right.space===!0&&(h[l.lang.rtl===!0?"left":"right"]=`${o.right.size}px`),h});function i(t,h){o.update("header",t,h)}function m(t,h){t.value!==h&&(t.value=h)}function P({height:t}){m(r,t),i("size",t)}function H(t){z.value===!0&&m(c,!0),a("focusin",t)}w(()=>e.modelValue,t=>{i("space",t),m(c,!0),o.animate()}),w(g,t=>{i("offset",t)}),w(()=>e.reveal,t=>{t===!1&&m(c,e.modelValue)}),w(c,t=>{o.animate(),a("reveal",t)}),w(o.scroll,t=>{e.reveal===!0&&m(c,t.direction==="up"||t.position<=e.revealOffset||t.position-t.inflectionPoint<100)});const $={};return o.instances.header=$,e.modelValue===!0&&i("size",r.value),i("space",e.modelValue),i("offset",g.value),M(()=>{o.instances.header===$&&(o.instances.header=void 0,i("size",0),i("offset",0),i("space",!1))}),()=>{const t=U(v.default,[]);return e.elevated===!0&&t.push(f("div",{class:"q-layout__shadow absolute-full overflow-hidden no-pointer-events"})),t.push(f(F,{debounce:0,onResize:P})),f("header",{class:y.value,style:p.value,onFocusin:H},t)}}}),se=q({name:"QPageContainer",setup(e,{slots:v}){const{proxy:{$q:a}}=L(),l=W(O,x);if(l===x)return console.error("QPageContainer needs to be child of QLayout"),x;N(j,!0);const o=u(()=>{const r={};return l.header.space===!0&&(r.paddingTop=`${l.header.size}px`),l.right.space===!0&&(r[`padding${a.lang.rtl===!0?"Left":"Right"}`]=`${l.right.size}px`),l.footer.space===!0&&(r.paddingBottom=`${l.footer.size}px`),l.left.space===!0&&(r[`padding${a.lang.rtl===!0?"Right":"Left"}`]=`${l.left.size}px`),r});return()=>f("div",{class:"q-page-container",style:o.value},B(v.default))}});const{passive:E}=K,te=["both","horizontal","vertical"];var oe=q({name:"QScrollObserver",props:{axis:{type:String,validator:e=>te.includes(e),default:"vertical"},debounce:[String,Number],scrollTarget:k},emits:["scroll"],setup(e,{emit:v}){const a={position:{top:0,left:0},direction:"down",directionChanged:!1,delta:{top:0,left:0},inflectionPoint:{top:0,left:0}};let l=null,o,r;w(()=>e.scrollTarget,()=>{g(),b()});function c(){l!==null&&l();const y=Math.max(0,G(o)),p=X(o),i={top:y-a.position.top,left:p-a.position.left};if(e.axis==="vertical"&&i.top===0||e.axis==="horizontal"&&i.left===0)return;const m=Math.abs(i.top)>=Math.abs(i.left)?i.top<0?"up":"down":i.left<0?"left":"right";a.position={top:y,left:p},a.directionChanged=a.direction!==m,a.delta=i,a.directionChanged===!0&&(a.direction=m,a.inflectionPoint=a.position),v("scroll",{...a})}function b(){o=I(r,e.scrollTarget),o.addEventListener("scroll",s,E),s(!0)}function g(){o!==void 0&&(o.removeEventListener("scroll",s,E),o=void 0)}function s(y){if(y===!0||e.debounce===0||e.debounce==="0")c();else if(l===null){const[p,i]=e.debounce?[setTimeout(c,e.debounce),clearTimeout]:[requestAnimationFrame(c),cancelAnimationFrame];l=()=>{i(p),l=null}}}const{proxy:z}=L();return w(()=>z.$q.lang.rtl,c),A(()=>{r=z.$el.parentNode,b()}),M(()=>{l!==null&&l(),g()}),Object.assign(z,{trigger:s,getPosition:()=>a}),D}}),ue=q({name:"QLayout",props:{container:Boolean,view:{type:String,default:"hhh lpr fff",validator:e=>/^(h|l)h(h|r) lpr (f|l)f(f|r)$/.test(e.toLowerCase())},onScroll:Function,onScrollHeight:Function,onResize:Function},setup(e,{slots:v,emit:a}){const{proxy:{$q:l}}=L(),o=S(null),r=S(l.screen.height),c=S(e.container===!0?0:l.screen.width),b=S({position:0,direction:"down",inflectionPoint:0}),g=S(0),s=S(Y.value===!0?0:V()),z=u(()=>"q-layout q-layout--"+(e.container===!0?"containerized":"standard")),y=u(()=>e.container===!1?{minHeight:l.screen.height+"px"}:null),p=u(()=>s.value!==0?{[l.lang.rtl===!0?"left":"right"]:`${s.value}px`}:null),i=u(()=>s.value!==0?{[l.lang.rtl===!0?"right":"left"]:0,[l.lang.rtl===!0?"left":"right"]:`-${s.value}px`,width:`calc(100% + ${s.value}px)`}:null);function m(n){if(e.container===!0||document.qScrollPrevented!==!0){const d={position:n.position.top,direction:n.direction,directionChanged:n.directionChanged,inflectionPoint:n.inflectionPoint.top,delta:n.delta.top};b.value=d,e.onScroll!==void 0&&a("scroll",d)}}function P(n){const{height:d,width:T}=n;let Q=!1;r.value!==d&&(Q=!0,r.value=d,e.onScrollHeight!==void 0&&a("scrollHeight",d),$()),c.value!==T&&(Q=!0,c.value=T),Q===!0&&e.onResize!==void 0&&a("resize",n)}function H({height:n}){g.value!==n&&(g.value=n,$())}function $(){if(e.container===!0){const n=r.value>g.value?V():0;s.value!==n&&(s.value=n)}}let t=null;const h={instances:{},view:u(()=>e.view),isContainer:u(()=>e.container),rootRef:o,height:r,containerHeight:g,scrollbarWidth:s,totalWidth:u(()=>c.value+s.value),rows:u(()=>{const n=e.view.toLowerCase().split(" ");return{top:n[0].split(""),middle:n[1].split(""),bottom:n[2].split("")}}),header:C({size:0,offset:0,space:!1}),right:C({size:300,offset:0,space:!1}),footer:C({size:0,offset:0,space:!1}),left:C({size:300,offset:0,space:!1}),scroll:b,animate(){t!==null?clearTimeout(t):document.body.classList.add("q-body--layout-animate"),t=setTimeout(()=>{t=null,document.body.classList.remove("q-body--layout-animate")},155)},update(n,d,T){h[n][d]=T}};if(N(O,h),V()>0){let T=function(){n=null,d.classList.remove("hide-scrollbar")},Q=function(){if(n===null){if(d.scrollHeight>l.screen.height)return;d.classList.add("hide-scrollbar")}else clearTimeout(n);n=setTimeout(T,300)},R=function(_){n!==null&&_==="remove"&&(clearTimeout(n),T()),window[`${_}EventListener`]("resize",Q)},n=null;const d=document.body;w(()=>e.container!==!0?"add":"remove",R),e.container!==!0&&R("add"),Z(()=>{R("remove")})}return()=>{const n=J(v.default,[f(oe,{onScroll:m}),f(F,{onResize:P})]),d=f("div",{class:z.value,style:y.value,ref:e.container===!0?void 0:o,tabindex:-1},n);return e.container===!0?f("div",{class:"q-layout-container overflow-hidden",ref:o},[f(F,{onResize:H}),f("div",{class:"absolute-full",style:p.value},[f("div",{class:"scroll",style:i.value},[d])])]):d}}});const ce=ee("title",{state:()=>({val:"\u6A02\u900F\u5373\u6642\u5831"})});export{ue as Q,ie as a,ae as b,re as c,se as d,ce as u};
