import{k as Ce,r as p,b as ke,c as he,w as be,I as o,J as T,L as m,a2 as W,M as i,a3 as de,P as l,O as g,az as n,aC as k,aA as E,A as _,aB as J,bb as $,ba as x,N as B,a8 as ie,aD as xe,a6 as Fe}from"./index.8af2c4ae.js";import{Q as V}from"./QSelect.72df340f.js";import{Q as Se,a as qe}from"./QItem.9674e7d0.js";import{u as Ae,Q as Re}from"./useLotteryAnalysis.0590aea7.js";import{Q as Ve}from"./QLinearProgress.63feec29.js";import{Q as Be}from"./QPage.4e73e683.js";import{p as F,u as De,_ as Me}from"./IndexPage.f9e05f4a.js";import{Q as Le,a as $e,b as Te,c as Qe,u as ze}from"./QTabPanels.b6e0b032.js";import{L as we}from"./lotto.b0a54c20.js";import{Q as Pe,a as Ee,b as R,c as Ue}from"./QTable.930bb4a7.js";import{_ as je}from"./plugin-vue_export-helper.21dcd24c.js";import"./QPopupProxy.7629cd06.js";import"./QResizeObserver.ec6dd509.js";import"./touch.9135741d.js";import"./use-render-cache.3aae9b27.js";import"./QList.f7f340df.js";const Oe={class:"row q-gutter-y-md"},Ne={class:"col-12 col-sm-4 draw-title text-center"},Ie={class:"text-period"},Ge={class:"text-draw-date"},We={class:"col-12 col-sm-6 self-center"},Je={class:"row justify-center"},Ze={key:0,class:"col-auto"},He={class:"row q-my-md q-gutter-sm items-center"},Ke={class:"col-sm-auto"},Xe={class:"col-sm-auto"},Ye={class:"row q-my-md q-gutter-sm items-center"},et={class:"col-sm-auto"},tt={class:"col-sm-auto"},lt={class:"row q-col-gutter-xs"},st={class:"ball tail-number"},at={class:"row q-col-gutter-xs"},ut={class:"ball tail-number"},ot={class:"row q-col-gutter-xs"},rt={class:"ball tail-number"},nt={class:"row q-gutter-sm items-center q-mb-md"},it={class:"ball tail-number q-mx-xs"},dt={class:"row q-gutter-sm items-center q-mb-md"},ct={class:"ball tail-number q-mx-xs"},mt={class:"row q-gutter-sm items-center"},pt={class:"ball tail-number q-mx-xs"},vt={class:"text-subtitle1"},bt={class:"row justify-center"},ft={class:"row justify-center"},gt={class:"row justify-center"},_t={class:"row justify-center"},yt={class:"row justify-center"},wt={class:"row justify-center"},Et={class:"row justify-center"},Ct={class:"row justify-center"},At={class:"row justify-center"},kt={class:"row justify-center"},ht={class:"row no-wrap"},xt={key:0,class:"text-subtitle1 ball special-number"},Ft={class:"row no-wrap"},St=Ce({__name:"PatternResult",props:{isSuperLotto:{type:Boolean},drawResults:{},predictResult:{},rdResults:{},tailRdResults:{}},setup(fe){const C=fe,S=p("1"),q=Ae();let h=p(new Map);const ee=[{name:"draw_date",label:"\u65E5\u671F",field:"draw_date",align:"center"},{name:"tail1",label:"\u5C3E1",field:"tail1",align:"center"},{name:"tail2",label:"\u5C3E2",field:"tail2",align:"center"},{name:"tail3",label:"\u5C3E3",field:"tail3",align:"center"},{name:"tail4",label:"\u5C3E4",field:"tail4",align:"center"},{name:"tail5",label:"\u5C3E5",field:"tail5",align:"center"},{name:"tail6",label:"\u5C3E6",field:"tail6",align:"center"},{name:"tail7",label:"\u5C3E7",field:"tail7",align:"center"},{name:"tail8",label:"\u5C3E8",field:"tail8",align:"center"},{name:"tail9",label:"\u5C3E9",field:"tail9",align:"center"},{name:"tail0",label:"\u5C3E0",field:"tail0",align:"center"},{name:"draw_results",label:"\u734E\u865F",field:"draw_results",align:"center"},{name:"tail_set",label:"\u5C3E\u6578",field:"tail_set",align:"center"}];ke(()=>{Z()});const Q=p(new Map),D=p(new Map),Z=()=>{var s;h.value.clear(),G.value=[],O.value=[],w.value=[],Q.value.clear();for(const t of C.drawResults){t.tails=new Map;for(let a=0;a<10;a++)t.tails.set(a,[]);let e=[...t.draw_number_size];!C.isSuperLotto&&t.special_number&&(e.push(t.special_number),e=e.sort((a,u)=>a-u));for(const a of e){const u=a%10;(s=t.tails.get(u))==null||s.push(a)}t.tailSet=q.getTailSet(t,C.isSuperLotto);for(const a of t.tailSet)Q.value.set(a,(Q.value.get(a)||0)+1)}Q.value=I(Q.value);for(const t of Array(10).keys())h.value.set(t,0);oe(),K(),me(),ve(),h.value=I(h.value),r()},z=p(1),te=p([{label:"\u6E96\u904E 1 \u6B21\u4EE5\u4E0A",value:1},{label:"\u6E96\u904E 2 \u6B21\u4EE5\u4E0A",value:2},{label:"\u6E96\u904E 3 \u6B21\u4EE5\u4E0A",value:3},{label:"\u6E96\u904E 4 \u6B21\u4EE5\u4E0A",value:4},{label:"\u6E96\u904E 5 \u6B21\u4EE5\u4E0A",value:5}]),P=p(.5),le=p([{label:"\u6E96\u78BA\u7387 100%",value:1},{label:"\u6E96\u78BA\u7387 90% \u4EE5\u4E0A",value:.9},{label:"\u6E96\u78BA\u7387 80% \u4EE5\u4E0A",value:.8},{label:"\u6E96\u78BA\u7387 70% \u4EE5\u4E0A",value:.7},{label:"\u6E96\u78BA\u7387 60% \u4EE5\u4E0A",value:.6},{label:"\u6E96\u78BA\u7387 50% \u4EE5\u4E0A",value:.5}]),L=p(1),se=p(1),ce=he(()=>Array.from({length:se.value},(s,t)=>({label:`\u6E96\u904E ${t+1} \u6B21\u4EE5\u4E0A`,value:t+1}))),H=p(.5),ae=p([{label:"\u6E96\u78BA\u7387 100%",value:1},{label:"\u6E96\u78BA\u7387 90% \u4EE5\u4E0A",value:.9},{label:"\u6E96\u78BA\u7387 80% \u4EE5\u4E0A",value:.8},{label:"\u6E96\u78BA\u7387 70% \u4EE5\u4E0A",value:.7},{label:"\u6E96\u78BA\u7387 60% \u4EE5\u4E0A",value:.6},{label:"\u6E96\u78BA\u7387 50% \u4EE5\u4E0A",value:.5}]);be([z,P,L,H],()=>{Z()});const oe=()=>{const s=p([]);D.value.clear(),s.value=C.rdResults.filter(u=>u.targetMatches>=z.value&&u.targetProbability>=P.value);const t=new Map,e=re(s.value);let a=0;for(const u of s.value){let v=new Set;for(const y of u.targetNumbers){const d=y%10,Y=u.targetMatches*u.targetProbability,ne=U(u,e.get(d)||0);t.set(d,(t.get(d)||0)+Y+ne),a+=u.targetMatches,v.add(d)}for(const y of v)D.value.set(y,(D.value.get(y)||0)+1)}D.value=I(D.value);for(const u of t.keys()){const v=t.get(u)||0;X(u,v/a)}},M=p(new Map),K=()=>{const s=p([]);M.value.clear(),s.value=C.tailRdResults.filter(a=>a.targetMatches>=L.value&&a.targetProbability>=H.value),se.value=1,se.value=Math.max(...s.value.map(a=>a.targetMatches));const t=re(s.value);let e=0;for(const a of s.value)for(const u of a.targetNumbers){const v=a.targetMatches*a.targetProbability,y=U(a,t.get(u)||0);M.value.set(u,(M.value.get(u)||0)+v+y),e+=a.targetMatches}M.value=I(M.value);for(const a of M.value.keys()){const u=M.value.get(a)||0;X(a,u/e)}},re=s=>{const t=new Map,e=new Map;for(const a of s)for(const u of a.targetNumbers){const v=u%10;t.set(v,(t.get(v)||0)+u),e.set(v,(e.get(v)||0)+1)}for(const a of t.keys()){const u=e.get(a)||1;t.set(a,Number((t.get(a)||0)/u))}return t},U=(s,t)=>(s.targetMatches+5*t)/(s.targetMatches/s.targetProbability+5),me=()=>{const s=C.drawResults,t=s.slice(-10),e=ue(s),a=j(t),u=new Map;e.forEach((y,d)=>{const Y=a.get(d)||0;u.set(d,(y+Y)/2)});const v=N(u);for(const y of u.keys()){const d=v.get(y)||0;X(y,d)}},ue=s=>{const t=new Map;s.forEach(u=>{if(u.draw_number_size.forEach(v=>{const y=v%10;t.set(y,(t.get(y)||0)+1)}),!C.isSuperLotto&&u.special_number){const v=u.special_number%10;t.set(v,(t.get(v)||0)+1)}});const e=Math.max(...t.values()),a=new Map;return t.forEach((u,v)=>a.set(v,u/e)),a},j=s=>{const t=new Map,e=new Map;for(let u=1;u<s.length;u++){const v=s[u-1].tailSet,y=s[u].tailSet;!v||!y||v.length===0||y.length===0||v.forEach(d=>{t.set(d,(t.get(d)||0)+1),y.includes(d)&&e.set(d,(e.get(d)||0)+1)})}const a=new Map;return t.forEach((u,v)=>{const y=e.get(v)||0;a.set(v,y/u)}),a},N=s=>{const t=Array.from(s.values()),e=Math.min(...t),a=Math.max(...t),u=new Map;return s.forEach((v,y)=>u.set(y,a===e?0:(v-e)/(a-e))),u},X=(s,t)=>{h.value.set(s,(h.value.get(s)||0)+t)},I=s=>{const t=Array.from(s.entries());return t.sort((e,a)=>a[1]-e[1]),new Map(t)},pe=p(3),ve=()=>{for(const s of h.value.keys()){const t=h.value.get(s)||0;h.value.set(s,Number((t/pe.value*100).toFixed(1)))}},G=p([]),O=p([]),w=p([]),r=()=>{G.value=[],O.value=[],w.value=[];const s=Array.from(D.value.entries()).map(a=>a[0]).slice(0,5),t=Array.from(M.value.entries()).map(a=>a[0]).slice(0,5),e=Array.from(h.value.entries()).map(a=>a[0]).slice(0,5);for(const a of s)t.includes(a)&&G.value.push(a);for(const a of s)e.includes(a)&&O.value.push(a);for(const a of t)e.includes(a)&&w.value.push(a);G.value.sort((a,u)=>a===0?1:u===0?-1:a-u),O.value.sort((a,u)=>a===0?1:u===0?-1:a-u),w.value.sort((a,u)=>a===0?1:u===0?-1:a-u)},A=s=>s&&Array.from(s).length===0?"#f8d7da":"",f=s=>{var t,e;return(e=(t=C.predictResult)==null?void 0:t.tailSet)!=null&&e.length?C.predictResult.tailSet.includes(s):!1};return(s,t)=>(o(),T(W,{class:"q-mt-md"},{default:m(()=>[i(de,null,{default:m(()=>[i(Le,{modelValue:S.value,"onUpdate:modelValue":t[0]||(t[0]=e=>S.value=e),dense:"",align:"justify",class:"text-h6","active-color":"primary","indicator-color":"primary"},{default:m(()=>[i($e,{name:"1",label:"\u7248\u8DEF\u5206\u6790"})]),_:1},8,["modelValue"]),i(Te,{modelValue:S.value,"onUpdate:modelValue":t[5]||(t[5]=e=>S.value=e)},{default:m(()=>[i(Qe,{name:"1"},{default:m(()=>[s.predictResult.period?(o(),T(W,{key:0,bordered:"",class:"ball-card full-width q-my-lg"},{default:m(()=>[i(de,null,{default:m(()=>[l("div",Oe,[l("div",Ne,[t[6]||(t[6]=l("div",{class:"text-h6"},"\u9810\u6E2C\u958B\u734E\u7D50\u679C",-1)),l("div",Ie," \u7B2C "+g(s.predictResult.period)+" \u671F ",1),l("div",Ge," \u958B\u734E\u65E5\u671F\uFF1A"+g(s.predictResult.draw_date),1)]),l("div",We,[l("div",Je,[(o(!0),n(E,null,k(s.predictResult.draw_number_size,e=>(o(),n("div",{key:e,class:"col-auto"},[(o(),n("div",{class:"ball",key:e},g(_(F)(e)),1))]))),128)),s.predictResult.special_number?(o(),n("div",Ze,[(o(),n("div",{class:"ball special-number",key:s.predictResult.special_number},g(_(F)(s.predictResult.special_number)),1))])):J("",!0)])])])]),_:1})]),_:1})):J("",!0),l("div",He,[t[7]||(t[7]=l("div",{class:"col-12 text-h6"},"\u7248\u8DEF\u5206\u6790\u7BE9\u9078",-1)),l("div",Ke,[i(V,{outlined:"",dense:"",modelValue:z.value,"onUpdate:modelValue":t[1]||(t[1]=e=>z.value=e),options:te.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])]),l("div",Xe,[i(V,{outlined:"",dense:"",modelValue:P.value,"onUpdate:modelValue":t[2]||(t[2]=e=>P.value=e),options:le.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])])]),l("div",Ye,[t[8]||(t[8]=l("div",{class:"col-12 text-h6"},"\u5C3E\u6578\u5206\u6790\u7BE9\u9078",-1)),l("div",et,[i(V,{outlined:"",dense:"",modelValue:L.value,"onUpdate:modelValue":t[3]||(t[3]=e=>L.value=e),options:ce.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])]),l("div",tt,[i(V,{outlined:"",dense:"",modelValue:H.value,"onUpdate:modelValue":t[4]||(t[4]=e=>H.value=e),options:ae.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])])]),t[22]||(t[22]=l("div",{class:"row q-mb-sm"},[l("label",{class:"col text-h6 text-bold"}," \u62D6\u724C\uFF1A\u5C3E\u6578\u9810\u6E2C\u958B\u51FA\u6A5F\u7387 (\u7531\u9AD8\u81F3\u4F4E\u6392\u5E8F) ")],-1)),i(W,{flat:"",bordered:"",class:"q-mb-lg q-pa-sm appearance"},{default:m(()=>[l("div",lt,[(o(!0),n(E,null,k(D.value.entries(),([e])=>(o(),n("div",{class:x(["col-4 col-md-2 text-h6",{predict:f(e)}]),key:e},[l("span",st,g(e),1)],2))),128))])]),_:1}),t[23]||(t[23]=l("div",{class:"row q-mb-sm"},[l("label",{class:"col text-h6 text-bold"}," \u5C3E\u6578\uFF1A\u9810\u6E2C\u958B\u51FA\u6A5F\u7387 (\u7531\u9AD8\u81F3\u4F4E\u6392\u5E8F) ")],-1)),i(W,{flat:"",bordered:"",class:"q-mb-lg q-pa-sm appearance"},{default:m(()=>[l("div",at,[(o(!0),n(E,null,k(M.value.entries(),([e])=>(o(),n("div",{class:x(["col-4 col-md-2 text-h6",{predict:f(e)}]),key:e},[l("span",ut,g(e),1)],2))),128))])]),_:1}),t[24]||(t[24]=l("div",{class:"row q-mb-sm"},[l("label",{class:"col text-h6 text-bold"}," \u7248\u8DEF\uFF1A\u5C3E\u6578\u9810\u6E2C\u958B\u51FA\u6A5F\u7387\uFF08\u7531\u9AD8\u81F3\u4F4E\u6392\u5E8F\uFF09 ")],-1)),i(W,{flat:"",bordered:"",class:"q-mb-lg q-pa-sm appearance"},{default:m(()=>[l("div",ot,[(o(!0),n(E,null,k(_(h).entries(),([e])=>(o(),n("div",{class:x(["col-4 col-md-2 text-h6",{predict:f(e)}]),key:e},[l("span",rt,g(e),1)],2))),128))])]),_:1}),t[25]||(t[25]=l("div",{class:"row q-mb-sm"},[l("label",{class:"col text-h6 text-bold"}," \u7D9C\u5408\u6BD4\u5C0D\u9810\u6E2C\u5C3E\u6578 ")],-1)),i(W,{flat:"",bordered:"",class:"q-mb-lg q-pa-sm appearance"},{default:m(()=>[l("div",nt,[t[9]||(t[9]=l("span",{class:"text-h6"},"\u62D6\u724C+\u5C3E\u6578\uFF1A",-1)),(o(!0),n(E,null,k(G.value.entries(),([,e])=>(o(),n("span",{key:e,class:x({predict:f(e)})},[l("span",it,g(e),1)],2))),128))]),l("div",dt,[t[10]||(t[10]=l("span",{class:"text-h6"},"\u62D6\u724C+\u7248\u8DEF\uFF1A",-1)),(o(!0),n(E,null,k(O.value.entries(),([,e])=>(o(),n("span",{key:e,class:x({predict:f(e)})},[l("span",ct,g(e),1)],2))),128))]),l("div",mt,[t[11]||(t[11]=l("span",{class:"text-h6"},"\u5C3E\u6578+\u7248\u8DEF\uFF1A",-1)),(o(!0),n(E,null,k(w.value.entries(),([,e])=>(o(),n("span",{key:e,class:x({predict:f(e)})},[l("span",pt,g(e),1)],2))),128))])]),_:1}),i(Pe,{rows:s.drawResults,columns:ee,"rows-per-page-options":[0],"hide-bottom":"","hide-pagination":"","virtual-scroll":"",separator:"cell",class:"sticky-virtscroll-table q-mt-lg"},{header:m(e=>[i(Ee,{props:e,class:"bg-primary text-white"},{default:m(()=>[(o(!0),n(E,null,k(e.cols,a=>(o(),T(Ue,{key:a.name,props:e},{default:m(()=>[B(g(a.label),1)]),_:2},1032,["props"]))),128))]),_:2},1032,["props"])]),body:m(e=>[i(Ee,{props:e},{default:m(()=>{var a,u,v,y,d,Y,ne,ge,_e,ye;return[i(R,{key:"draw_date",props:e},{default:m(()=>[l("div",vt,g(e.row.draw_date),1)]),_:2},1032,["props"]),i(R,{key:"tail1",props:e,class:"fixed-col",style:$({backgroundColor:A((a=e.row.tails)==null?void 0:a.get(1))})},{default:m(()=>{var b;return[l("div",bt,[(o(!0),n(E,null,k((b=e.row.tails)==null?void 0:b.get(1),c=>(o(),n("span",{key:c,class:x(["text-h6 ball col-6",{"special-number":c===e.row.special_number&&!s.isSuperLotto}])},[B(g(_(F)(c))+" ",1),t[12]||(t[12]=l("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),i(R,{key:"tail2",props:e,class:"fixed-col",style:$({backgroundColor:A((u=e.row.tails)==null?void 0:u.get(2))})},{default:m(()=>{var b;return[l("div",ft,[(o(!0),n(E,null,k((b=e.row.tails)==null?void 0:b.get(2),c=>(o(),n("span",{key:c,class:x(["text-h6 ball col-6",{"special-number":c===e.row.special_number&&!s.isSuperLotto}])},[B(g(_(F)(c)),1),t[13]||(t[13]=l("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),i(R,{key:"tail3",props:e,class:"fixed-col",style:$({backgroundColor:A((v=e.row.tails)==null?void 0:v.get(3))})},{default:m(()=>{var b;return[l("div",gt,[(o(!0),n(E,null,k((b=e.row.tails)==null?void 0:b.get(3),c=>(o(),n("span",{key:c,class:x(["text-h6 ball col-6",{"special-number":c===e.row.special_number&&!s.isSuperLotto}])},[B(g(_(F)(c)),1),t[14]||(t[14]=l("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),i(R,{key:"tail4",props:e,class:"fixed-col",style:$({backgroundColor:A((y=e.row.tails)==null?void 0:y.get(4))})},{default:m(()=>{var b;return[l("div",_t,[(o(!0),n(E,null,k((b=e.row.tails)==null?void 0:b.get(4),c=>(o(),n("span",{key:c,class:x(["text-h6 ball col-6",{"special-number":c===e.row.special_number&&!s.isSuperLotto}])},[B(g(_(F)(c)),1),t[15]||(t[15]=l("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),i(R,{key:"tail5",props:e,class:"fixed-col",style:$({backgroundColor:A((d=e.row.tails)==null?void 0:d.get(5))})},{default:m(()=>{var b;return[l("div",yt,[(o(!0),n(E,null,k((b=e.row.tails)==null?void 0:b.get(5),c=>(o(),n("span",{key:c,class:x(["text-h6 ball col-6",{"special-number":c===e.row.special_number&&!s.isSuperLotto}])},[B(g(_(F)(c)),1),t[16]||(t[16]=l("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),i(R,{key:"tail6",props:e,class:"fixed-col",style:$({backgroundColor:A((Y=e.row.tails)==null?void 0:Y.get(6))})},{default:m(()=>{var b;return[l("div",wt,[(o(!0),n(E,null,k((b=e.row.tails)==null?void 0:b.get(6),c=>(o(),n("span",{key:c,class:x(["text-h6 ball col-6",{"special-number":c===e.row.special_number&&!s.isSuperLotto}])},[B(g(_(F)(c)),1),t[17]||(t[17]=l("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),i(R,{key:"tail7",props:e,class:"fixed-col",style:$({backgroundColor:A((ne=e.row.tails)==null?void 0:ne.get(7))})},{default:m(()=>{var b;return[l("div",Et,[(o(!0),n(E,null,k((b=e.row.tails)==null?void 0:b.get(7),c=>(o(),n("span",{key:c,class:x(["text-h6 ball col-6",{"special-number":c===e.row.special_number&&!s.isSuperLotto}])},[B(g(_(F)(c)),1),t[18]||(t[18]=l("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),i(R,{key:"tail8",props:e,class:"fixed-col",style:$({backgroundColor:A((ge=e.row.tails)==null?void 0:ge.get(8))})},{default:m(()=>{var b;return[l("div",Ct,[(o(!0),n(E,null,k((b=e.row.tails)==null?void 0:b.get(8),c=>(o(),n("span",{key:c,class:x(["text-h6 ball col-6",{"special-number":c===e.row.special_number&&!s.isSuperLotto}])},[B(g(_(F)(c)),1),t[19]||(t[19]=l("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),i(R,{key:"tail9",props:e,class:"fixed-col",style:$({backgroundColor:A((_e=e.row.tails)==null?void 0:_e.get(9))})},{default:m(()=>{var b;return[l("div",At,[(o(!0),n(E,null,k((b=e.row.tails)==null?void 0:b.get(9),c=>(o(),n("span",{key:c,class:x(["text-h6 ball col-6",{"special-number":c===e.row.special_number&&!s.isSuperLotto}])},[B(g(_(F)(c)),1),t[20]||(t[20]=l("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),i(R,{key:"tail0",props:e,class:"fixed-col",style:$({backgroundColor:A((ye=e.row.tails)==null?void 0:ye.get(0))})},{default:m(()=>{var b;return[l("div",kt,[(o(!0),n(E,null,k((b=e.row.tails)==null?void 0:b.get(0),c=>(o(),n("span",{key:c,class:x(["text-h6 ball col-6",{"special-number":c===e.row.special_number&&!s.isSuperLotto}])},[B(g(_(F)(c)),1),t[21]||(t[21]=l("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),i(R,{key:"draw_results",props:e},{default:m(()=>[l("div",ht,[(o(!0),n(E,null,k(e.row.draw_number_size,b=>(o(),n("span",{key:b,class:"text-subtitle1 ball"},g(_(F)(b)),1))),128)),e.row.special_number?(o(),n("span",xt,g(_(F)(e.row.special_number)),1)):J("",!0)])]),_:2},1032,["props"]),i(R,{key:"tail_set",props:e},{default:m(()=>[l("div",Ft,[(o(!0),n(E,null,k(e.row.tailSet,b=>(o(),n("span",{key:b,class:"text-subtitle1 ball tail-number"},g(b),1))),128))])]),_:2},1032,["props"])]}),_:2},1032,["props"])]),_:1},8,["rows"])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}))}});var qt=je(St,[["__scopeId","data-v-3dd21530"]]);const Rt={class:"row lto-ref q-mb-sm"},Vt={class:"col-12 col-sm-4 self-center text-h6"},Bt={class:"col-12 col-sm-6 self-center text-subtitle1"},Dt={class:"row balls"},Mt={class:"ball"},Lt={key:0,class:"col-auto"},$t={class:"row q-mb-md"},Tt={class:"col"},Qt={key:1,class:"row q-mb-md"},zt={class:"row q-mb-md"},Pt={class:"col-12 col-sm-4 q-pa-sm"},Ut={class:"col-12 col-sm-4 q-pa-sm"},jt={class:"col-12 col-sm-4 q-pa-sm"},Ot={class:"row q-mb-md"},Nt={class:"col-12 col-sm-4 q-pa-sm"},It={class:"col-12 col-sm-4 q-pa-sm"},Gt={class:"col-12 col-sm-4 q-pa-sm"},Wt={class:"row q-mb-md"},Jt={class:"col-12 col-sm-4"},Zt={class:"q-pa-sm"},Ht={class:"col-12 col-sm-4"},Kt={class:"q-pa-sm"},Xt={class:"col-12 col-sm-4"},Yt={class:"q-pa-sm"},el={class:"text-center q-mb-sm"},gl=Ce({__name:"PatternPage",setup(fe){const C=De(),S=p(C.getLotto),q=ze(),h=Ae(),ee=p(1),Q=p(1),D=p(1),Z=p(1),z=p(1),te=p(1),P=[{label:"\u4E00\u661F\u7D44\u5408",value:1},{label:"\u4E8C\u661F\u7D44\u5408",value:2},{label:"\u4E09\u661F\u7D44\u5408",value:3}],le=[{label:"\u4E00\u661F\u7D44\u5408",value:1},{label:"\u4E8C\u661F\u7D44\u5408",value:2},{label:"\u4E09\u661F\u7D44\u5408",value:3},{label:"\u56DB\u661F\u7D44\u5408",value:4},{label:"\u4E94\u661F\u7D44\u5408",value:5}],L=p(!1),se=()=>{L.value=!0},ce=()=>{L.value=!1},H=()=>{L.value=!1};be(()=>C.getLotto,w=>{w&&(S.value=w)}),be(()=>{var w;return(w=S.value)==null?void 0:w.period},()=>{j.value=[]});const ae=p(50);let oe=p(Array.from({length:991},(w,r)=>({label:`${r+10}\u671F`,value:r+10})));const M=(w,r,A)=>{const f=parseInt(w,10);(f<10||f>1e3)&&A(),r(()=>{oe.value=Array.from({length:991},(s,t)=>t+10).filter(s=>s.toString().startsWith(w)).map(s=>({label:`${s.toString()}\u671F`,value:s}))})},K=p(20),re=Array.from({length:21},(w,r)=>({label:`${r+10}\u671F`,value:r+10})),U=p(1),me=Array.from({length:15},(w,r)=>({label:`\u4E0B${r+1}\u671F`,value:r+1})),ue=p(!1),j=p([]),N=p({period:"",draw_date:"",draw_number_appear:[],draw_number_size:[],tails:new Map}),X=p([]),I=p([]),pe=async()=>{var w,r,A;try{q.startCalculating(),ue.value=C.isSuperLotto;const f=await we.getLottoList({draw_type:C.getDrawType,date_end:(r=(w=S.value)==null?void 0:w.draw_date)!=null?r:"",limit:ae.value});j.value=f.data.reverse();const s=await we.getLottoPredict({draw_type:C.getDrawType,draw_date:(A=C.getLotto)==null?void 0:A.draw_date,ahead_count:U.value});N.value=s.data,N.value.period&&(N.value.tailSet=h.getTailSet(N.value,ue.value));const t=await ve();X.value=t.data;const e=await G();I.value=e.data}catch(f){console.error("\u8A08\u7B97\u932F\u8AA4:",f)}finally{O()}},ve=()=>{const w=j.value.map(f=>{const s=[...f.draw_number_size];return f.special_number&&!C.isSuperLotto&&s.push(f.special_number),{numbers:[...s],period:String(f.period)}});h.setResults(w),h.setConfig({firstGroupSize:ee.value,secondGroupSize:Q.value,targetGroupSize:D.value,maxRange:K.value,lookAheadCount:U.value});let r=Date.now();const A=8;return h.analyzeWithProgress(async f=>{const s=Date.now();s-r>=A&&(await q.updateProgress(f),r=s)},f=>{q.addWarning(f)})},G=()=>{const w=j.value.map(f=>{const s=new Set;for(let e of f.draw_number_size)s.add(e%10);!C.isSuperLotto&&f.special_number&&s.add(f.special_number%10);const t=Array.from(s).sort((e,a)=>e===0?1:a===0?-1:e-a);return{period:String(f.period),numbers:[...t]}});h.init({firstGroupSize:Z.value,secondGroupSize:z.value,targetGroupSize:te.value,maxRange:K.value,lookAheadCount:U.value},w);let r=Date.now();const A=8;return h.analyzeWithProgress(async f=>{const s=Date.now();s-r>=A&&(await q.updateProgress(f),r=s)},f=>{q.addWarning(f)})},O=()=>{q.stopCalculating(),h.stopAnalyzer()};return(w,r)=>(o(),T(Be,{class:"justify-center"},{default:m(()=>[i(W,null,{default:m(()=>[i(de,null,{default:m(()=>{var A,f,s,t,e,a,u,v,y;return[(A=_(C).getLotto)!=null&&A.draw_date?(o(),n(E,{key:0},[l("div",Rt,[l("div",Vt,[l("div",null,g(_(C).getDrawLabel),1),r[9]||(r[9]=l("span",null,"\u53C3\u8003\u671F\u865F\uFF1A",-1)),l("span",null,g((f=S.value)==null?void 0:f.period),1),l("span",null,"\uFF08"+g((s=S.value)==null?void 0:s.draw_date)+"\uFF09",1)]),l("div",Bt,[l("div",Dt,[(o(!0),n(E,null,k((t=S.value)==null?void 0:t.draw_number_size,d=>(o(),n("div",{class:"col-auto",key:d},[l("div",Mt,g(_(F)(d)),1)]))),128)),(e=S.value)!=null&&e.special_number?(o(),n("div",Lt,[(o(),n("div",{class:"ball special-number",key:(a=S.value)==null?void 0:a.special_number},g(_(F)((u=S.value)==null?void 0:u.special_number)),1))])):J("",!0)])])]),l("div",$t,[l("div",Tt,[L.value?(o(),T(ie,{key:1,type:"button",label:"\u53D6\u6D88\u9078\u64C7",color:"negative",class:"text-h6 q-ml-md",onClick:H})):(o(),T(ie,{key:0,type:"button",label:"\u91CD\u65B0\u9078\u64C7",color:"primary",class:"text-h6 q-ml-md",onClick:se}))])])],64)):(o(),n("div",Qt,r[10]||(r[10]=[l("div",{class:"text-h6"},"\u203B\u8ACB\u9078\u64C7\u53C3\u8003\u671F\u865F",-1)]))),i(xe,{class:"q-mb-md"}),!L.value&&((v=_(C).getLotto)==null?void 0:v.draw_date)?(o(),n(E,{key:2},[r[17]||(r[17]=l("div",{class:"row q-mb-md"},[l("div",{class:"col-12 text-h5 text-weight-bolder text-center"}," \u7D9C\u5408\u5206\u6790\u8A2D\u5B9A ")],-1)),l("div",zt,[r[11]||(r[11]=l("div",{class:"col-12 text-h6 text-weight-bold"},"\u734E\u865F\u62D6\u724C\u7D44\u5408",-1)),l("div",Pt,[i(V,{outlined:"",dense:"",modelValue:ee.value,"onUpdate:modelValue":r[0]||(r[0]=d=>ee.value=d),options:P,"emit-value":"","map-options":""},null,8,["modelValue"])]),l("div",Ut,[i(V,{outlined:"",dense:"",modelValue:Q.value,"onUpdate:modelValue":r[1]||(r[1]=d=>Q.value=d),options:P,"emit-value":"","map-options":""},null,8,["modelValue"])]),l("div",jt,[i(V,{outlined:"",dense:"",modelValue:D.value,"onUpdate:modelValue":r[2]||(r[2]=d=>D.value=d),options:P,"emit-value":"","map-options":""},null,8,["modelValue"])])]),l("div",Ot,[r[12]||(r[12]=l("div",{class:"col-12 text-h6 text-weight-bold"},"\u5C3E\u6578\u62D6\u724C\u7D44\u5408",-1)),l("div",Nt,[i(V,{outlined:"",dense:"",modelValue:Z.value,"onUpdate:modelValue":r[3]||(r[3]=d=>Z.value=d),options:le,"emit-value":"","map-options":""},null,8,["modelValue"])]),l("div",It,[i(V,{outlined:"",dense:"",modelValue:z.value,"onUpdate:modelValue":r[4]||(r[4]=d=>z.value=d),options:le,"emit-value":"","map-options":""},null,8,["modelValue"])]),l("div",Gt,[i(V,{outlined:"",dense:"",modelValue:te.value,"onUpdate:modelValue":r[5]||(r[5]=d=>te.value=d),options:le,"emit-value":"","map-options":""},null,8,["modelValue"])])]),l("div",Wt,[l("div",Jt,[r[14]||(r[14]=l("div",{class:"text-h6 text-weight-bold"},"\u63A8\u7B97\u671F\u6578",-1)),l("div",Zt,[i(V,{outlined:"",dense:"",modelValue:ae.value,"onUpdate:modelValue":r[6]||(r[6]=d=>ae.value=d),options:_(oe),"input-debounce":"0","use-input":"","hide-selected":"","fill-input":"",onFilter:M,"emit-value":"","map-options":""},{"no-option":m(()=>[i(Se,null,{default:m(()=>[i(qe,{class:"text-grey"},{default:m(()=>r[13]||(r[13]=[B(" \u7121\u53EF\u7528\u9078\u9805 ")])),_:1})]),_:1})]),_:1},8,["modelValue","options"])])]),l("div",Ht,[r[15]||(r[15]=l("div",{class:"text-h6 text-weight-bold"},"\u6700\u5927\u5340\u9593",-1)),l("div",Kt,[i(V,{outlined:"",dense:"",modelValue:K.value,"onUpdate:modelValue":r[7]||(r[7]=d=>K.value=d),options:_(re),"emit-value":"","map-options":""},null,8,["modelValue","options"])])]),l("div",Xt,[r[16]||(r[16]=l("div",{class:"text-h6 text-weight-bold"},"\u9810\u6E2C\u671F\u6578",-1)),l("div",Yt,[i(V,{outlined:"",dense:"",modelValue:U.value,"onUpdate:modelValue":r[8]||(r[8]=d=>U.value=d),options:_(me),"emit-value":"","map-options":""},null,8,["modelValue","options"])])])]),i(Fe,{align:"right",class:"q-my-lg q-py-none q-px-md"},{default:m(()=>[_(q).isCalculating?(o(),T(ie,{key:0,type:"button",label:"\u4E2D\u65B7\u8A08\u7B97",color:"negative",class:"text-h6 q-mr-md",onClick:O})):J("",!0),i(ie,{type:"button",label:"\u958B\u59CB\u8A08\u7B97",color:"positive",class:"text-h6 q-px-lg q-py-sm",onClick:pe,loading:_(q).isCalculating},{loading:m(()=>[i(Re)]),_:1},8,["loading"])]),_:1})],64)):(o(),T(Me,{key:3,"draw-type-query":_(C).drawType,"date-query":((y=S.value)==null?void 0:y.draw_date)||"","is-select-ref":!0,onSelectRef:ce},null,8,["draw-type-query","date-query"]))]}),_:1}),_(q).isCalculating?(o(),T(de,{key:0},{default:m(()=>[l("div",el,g(_(q).progressMessage),1),i(Ve,{rounded:"",size:"md",value:_(q).progress,"animation-speed":50,color:"primary",class:"q-mb-xs"},null,8,["value"])]),_:1})):J("",!0)]),_:1}),!_(q).isCalculating&&j.value.length>0?(o(),T(qt,{key:0,"is-super-lotto":ue.value,"draw-results":j.value,"predict-result":N.value,"rd-results":X.value,"tail-rd-results":I.value},null,8,["is-super-lotto","draw-results","predict-result","rd-results","tail-rd-results"])):J("",!0)]),_:1}))}});export{gl as default};
