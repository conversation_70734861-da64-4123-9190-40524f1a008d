import{$ as Qe,r as p,i as Ve,D as le,c as g,a as Se,b as We,be as ke,p as Ne,aQ as Ke,h as w,ab as H,aI as je,b6 as ze,a5 as J,F as Ue,t as Z,b4 as Oe,bf as He,C as ee,aT as ue,au as Ce,w as O,v as Xe,o as Ye,f as Ge,Q as te,ah as Je,ai as U,V as Ze,aj as et,ak as G,al as ce,ao as Pe,ap as de,n as tt,T as at,bg as nt,K as rt,ar as it,at as ot,ax as st}from"./index.8af2c4ae.js";import{Q as lt}from"./QResizeObserver.ec6dd509.js";import{r as ut}from"./QSelect.72df340f.js";import{g as we,s as qe}from"./touch.9135741d.js";import{c as ct}from"./QItem.9674e7d0.js";import{u as dt}from"./use-render-cache.3aae9b27.js";const At=Qe("progress",{state:()=>({progress:p(0),progressMessage:p(""),isCalculating:p(!1),stage:p("preparation"),estimatedTime:p(0),startTime:p(0),warnings:p([]),memoryUsage:p(null)}),actions:{startCalculating(){this.progress=0,this.progressMessage="\u6E96\u5099\u958B\u59CB\u5206\u6790...",this.isCalculating=!0,this.stage="preparation",this.startTime=Date.now(),this.estimatedTime=0},async updateProgress(e){return new Promise(i=>{requestAnimationFrame(()=>{if(!e.progress||!e.total)return;const v=Math.round(e.progress/e.total*100),l=Math.max(0,Math.min(v,100));if(this.progress=l*.01,this.stage=e.stage,l>0&&this.startTime>0){const r=Date.now()-this.startTime,a=r/l*100;this.estimatedTime=Math.max(0,a-r)}this.progressMessage=this.getStageMessage(e.stage,l),i()})})},getStageMessage(e,i){switch(e){case"preparation":return"\u6B63\u5728\u6E96\u5099\u5206\u6790\u6578\u64DA...";case"processing":if(i===100)return"\u5206\u6790\u5B8C\u6210\uFF0C\u6B63\u5728\u6574\u7406\u7D50\u679C...";{const v=this.estimatedTime>0?` (\u9810\u4F30\u5269\u9918: ${Math.ceil(this.estimatedTime/1e3)}\u79D2)`:"";return`\u6B63\u5728\u5206\u6790\u4E2D\uFF1A${i.toFixed(0)}%${v}`}case"finalization":return"\u6B63\u5728\u751F\u6210\u6700\u7D42\u7D50\u679C...";case"complete":return"\u5206\u6790\u5B8C\u6210\uFF01";default:return`\u8655\u7406\u4E2D\uFF1A${i.toFixed(0)}%`}},stopCalculating(){this.isCalculating=!1,this.estimatedTime=0},addWarning(e){this.warnings.push({...e,timestamp:Date.now(),id:Math.random().toString(36).substring(2,11)}),e.usedMB&&e.limitMB&&e.percentage&&(this.memoryUsage={usedMB:e.usedMB,limitMB:e.limitMB,percentage:e.percentage})},clearWarnings(){this.warnings=[],this.memoryUsage=null},resetProgress(){this.progress=0,this.progressMessage="",this.isCalculating=!1,this.stage="preparation",this.estimatedTime=0,this.startTime=0,this.warnings=[],this.memoryUsage=null}}});let vt=0;const ft=["click","keydown"],ht={icon:String,label:[Number,String],alert:[Boolean,String],alertIcon:String,name:{type:[Number,String],default:()=>`t_${vt++}`},noCaps:Boolean,tabindex:[String,Number],disable:Boolean,contentClass:String,ripple:{type:[Boolean,Object],default:!0}};function mt(e,i,v,l){const r=Ve(ke,le);if(r===le)return console.error("QTab/QRouteTab component needs to be child of QTabs"),le;const{proxy:a}=Z(),o=p(null),T=p(null),L=p(null),I=g(()=>e.disable===!0||e.ripple===!1?!1:Object.assign({keyCodes:[13,32],early:!0},e.ripple===!0?{}:e.ripple)),b=g(()=>r.currentModel.value===e.name),E=g(()=>"q-tab relative-position self-stretch flex flex-center text-center"+(b.value===!0?" q-tab--active"+(r.tabProps.value.activeClass?" "+r.tabProps.value.activeClass:"")+(r.tabProps.value.activeColor?` text-${r.tabProps.value.activeColor}`:"")+(r.tabProps.value.activeBgColor?` bg-${r.tabProps.value.activeBgColor}`:""):" q-tab--inactive")+(e.icon&&e.label&&r.tabProps.value.inlineLabel===!1?" q-tab--full":"")+(e.noCaps===!0||r.tabProps.value.noCaps===!0?" q-tab--no-caps":"")+(e.disable===!0?" disabled":" q-focusable q-hoverable cursor-pointer")+(l!==void 0?l.linkClass.value:"")),m=g(()=>"q-tab__content self-stretch flex-center relative-position q-anchor--skip non-selectable "+(r.tabProps.value.inlineLabel===!0?"row no-wrap q-tab__content--inline":"column")+(e.contentClass!==void 0?` ${e.contentClass}`:"")),y=g(()=>e.disable===!0||r.hasFocus.value===!0||b.value===!1&&r.hasActiveTab.value===!0?-1:e.tabindex||0);function x(s,S){if(S!==!0&&o.value!==null&&o.value.focus(),e.disable===!0){l!==void 0&&l.hasRouterLink.value===!0&&H(s);return}if(l===void 0){r.updateModel({name:e.name}),v("click",s);return}if(l.hasRouterLink.value===!0){const M=(q={})=>{let k;const R=q.to===void 0||Oe(q.to,e.to)===!0?r.avoidRouteWatcher=He():null;return l.navigateToRouterLink(s,{...q,returnRouterError:!0}).catch(D=>{k=D}).then(D=>{if(R===r.avoidRouteWatcher&&(r.avoidRouteWatcher=!1,k===void 0&&(D===void 0||D.message!==void 0&&D.message.startsWith("Avoided redundant navigation")===!0)&&r.updateModel({name:e.name})),q.returnRouterError===!0)return k!==void 0?Promise.reject(k):D})};v("click",s,M),s.defaultPrevented!==!0&&M();return}v("click",s)}function B(s){je(s,[13,32])?x(s,!0):ze(s)!==!0&&s.keyCode>=35&&s.keyCode<=40&&s.altKey!==!0&&s.metaKey!==!0&&r.onKbdNavigate(s.keyCode,a.$el)===!0&&H(s),v("keydown",s)}function $(){const s=r.tabProps.value.narrowIndicator,S=[],M=w("div",{ref:L,class:["q-tab__indicator",r.tabProps.value.indicatorClass]});e.icon!==void 0&&S.push(w(J,{class:"q-tab__icon",name:e.icon})),e.label!==void 0&&S.push(w("div",{class:"q-tab__label"},e.label)),e.alert!==!1&&S.push(e.alertIcon!==void 0?w(J,{class:"q-tab__alert-icon",color:e.alert!==!0?e.alert:void 0,name:e.alertIcon}):w("div",{class:"q-tab__alert"+(e.alert!==!0?` text-${e.alert}`:"")})),s===!0&&S.push(M);const q=[w("div",{class:"q-focus-helper",tabindex:-1,ref:o}),w("div",{class:m.value},Ue(i.default,S))];return s===!1&&q.push(M),q}const F={name:g(()=>e.name),rootRef:T,tabIndicatorRef:L,routeData:l};Se(()=>{r.unregisterTab(F)}),We(()=>{r.registerTab(F)});function K(s,S){const M={ref:T,class:E.value,tabindex:y.value,role:"tab","aria-selected":b.value===!0?"true":"false","aria-disabled":e.disable===!0?"true":void 0,onClick:x,onKeydown:B,...S};return Ne(w(s,M,$()),[[Ke,I.value]])}return{renderTab:K,$tabs:r}}var Bt=ee({name:"QTab",props:ht,emits:ft,setup(e,{slots:i,emit:v}){const{renderTab:l}=mt(e,i,v);return()=>l("div")}});function gt(e,i,v){const l=v===!0?["left","right"]:["top","bottom"];return`absolute-${i===!0?l[0]:l[1]}${e?` text-${e}`:""}`}const bt=["left","center","right","justify"];var Rt=ee({name:"QTabs",props:{modelValue:[Number,String],align:{type:String,default:"center",validator:e=>bt.includes(e)},breakpoint:{type:[String,Number],default:600},vertical:Boolean,shrink:Boolean,stretch:Boolean,activeClass:String,activeColor:String,activeBgColor:String,indicatorColor:String,leftIcon:String,rightIcon:String,outsideArrows:Boolean,mobileArrows:Boolean,switchIndicator:Boolean,narrowIndicator:Boolean,inlineLabel:Boolean,noCaps:Boolean,dense:Boolean,contentClass:String,"onUpdate:modelValue":[Function,Array]},setup(e,{slots:i,emit:v}){const{proxy:l}=Z(),{$q:r}=l,{registerTick:a}=ue(),{registerTick:o}=ue(),{registerTick:T}=ue(),{registerTimeout:L,removeTimeout:I}=Ce(),{registerTimeout:b,removeTimeout:E}=Ce(),m=p(null),y=p(null),x=p(e.modelValue),B=p(!1),$=p(!0),F=p(!1),K=p(!1),s=[],S=p(0),M=p(!1);let q=null,k=null,R;const D=g(()=>({activeClass:e.activeClass,activeColor:e.activeColor,activeBgColor:e.activeBgColor,indicatorClass:gt(e.indicatorColor,e.switchIndicator,e.vertical),narrowIndicator:e.narrowIndicator,inlineLabel:e.inlineLabel,noCaps:e.noCaps})),ae=g(()=>{const t=S.value,n=x.value;for(let u=0;u<t;u++)if(s[u].name.value===n)return!0;return!1}),ne=g(()=>`q-tabs__content--align-${B.value===!0?"left":K.value===!0?"justify":e.align}`),re=g(()=>`q-tabs row no-wrap items-center q-tabs--${B.value===!0?"":"not-"}scrollable q-tabs--${e.vertical===!0?"vertical":"horizontal"} q-tabs__arrows--${e.outsideArrows===!0?"outside":"inside"} q-tabs--mobile-with${e.mobileArrows===!0?"":"out"}-arrows`+(e.dense===!0?" q-tabs--dense":"")+(e.shrink===!0?" col-shrink":"")+(e.stretch===!0?" self-stretch":"")),c=g(()=>"q-tabs__content scroll--mobile row no-wrap items-center self-stretch hide-scrollbar relative-position "+ne.value+(e.contentClass!==void 0?` ${e.contentClass}`:"")),h=g(()=>e.vertical===!0?{container:"height",content:"offsetHeight",scroll:"scrollHeight"}:{container:"width",content:"offsetWidth",scroll:"scrollWidth"}),A=g(()=>e.vertical!==!0&&r.lang.rtl===!0),V=g(()=>ut===!1&&A.value===!0);O(A,z),O(()=>e.modelValue,t=>{ie({name:t,setCurrent:!0,skipEmit:!0})}),O(()=>e.outsideArrows,X);function ie({name:t,setCurrent:n,skipEmit:u}){x.value!==t&&(u!==!0&&e["onUpdate:modelValue"]!==void 0&&v("update:modelValue",t),(n===!0||e["onUpdate:modelValue"]===void 0)&&(_e(x.value,t),x.value=t))}function X(){a(()=>{ve({width:m.value.offsetWidth,height:m.value.offsetHeight})})}function ve(t){if(h.value===void 0||y.value===null)return;const n=t[h.value.container],u=Math.min(y.value[h.value.scroll],Array.prototype.reduce.call(y.value.children,(P,f)=>P+(f[h.value.content]||0),0)),C=n>0&&u>n;B.value=C,C===!0&&o(z),K.value=n<parseInt(e.breakpoint,10)}function _e(t,n){const u=t!=null&&t!==""?s.find(P=>P.name.value===t):null,C=n!=null&&n!==""?s.find(P=>P.name.value===n):null;if(u&&C){const P=u.tabIndicatorRef.value,f=C.tabIndicatorRef.value;q!==null&&(clearTimeout(q),q=null),P.style.transition="none",P.style.transform="none",f.style.transition="none",f.style.transform="none";const d=P.getBoundingClientRect(),_=f.getBoundingClientRect();f.style.transform=e.vertical===!0?`translate3d(0,${d.top-_.top}px,0) scale3d(1,${_.height?d.height/_.height:1},1)`:`translate3d(${d.left-_.left}px,0,0) scale3d(${_.width?d.width/_.width:1},1,1)`,T(()=>{q=setTimeout(()=>{q=null,f.style.transition="transform .25s cubic-bezier(.4, 0, .2, 1)",f.style.transform="none"},70)})}C&&B.value===!0&&j(C.rootRef.value)}function j(t){const{left:n,width:u,top:C,height:P}=y.value.getBoundingClientRect(),f=t.getBoundingClientRect();let d=e.vertical===!0?f.top-C:f.left-n;if(d<0){y.value[e.vertical===!0?"scrollTop":"scrollLeft"]+=Math.floor(d),z();return}d+=e.vertical===!0?f.height-P:f.width-u,d>0&&(y.value[e.vertical===!0?"scrollTop":"scrollLeft"]+=Math.ceil(d),z())}function z(){const t=y.value;if(t===null)return;const n=t.getBoundingClientRect(),u=e.vertical===!0?t.scrollTop:Math.abs(t.scrollLeft);A.value===!0?($.value=Math.ceil(u+n.width)<t.scrollWidth-1,F.value=u>0):($.value=u>0,F.value=e.vertical===!0?Math.ceil(u+n.height)<t.scrollHeight:Math.ceil(u+n.width)<t.scrollWidth)}function fe(t){k!==null&&clearInterval(k),k=setInterval(()=>{Be(t)===!0&&W()},5)}function he(){fe(V.value===!0?Number.MAX_SAFE_INTEGER:0)}function me(){fe(V.value===!0?0:Number.MAX_SAFE_INTEGER)}function W(){k!==null&&(clearInterval(k),k=null)}function Me(t,n){const u=Array.prototype.filter.call(y.value.children,_=>_===n||_.matches&&_.matches(".q-tab.q-focusable")===!0),C=u.length;if(C===0)return;if(t===36)return j(u[0]),u[0].focus(),!0;if(t===35)return j(u[C-1]),u[C-1].focus(),!0;const P=t===(e.vertical===!0?38:37),f=t===(e.vertical===!0?40:39),d=P===!0?-1:f===!0?1:void 0;if(d!==void 0){const _=A.value===!0?-1:1,Q=u.indexOf(n)+d*_;return Q>=0&&Q<C&&(j(u[Q]),u[Q].focus({preventScroll:!0})),!0}}const Ae=g(()=>V.value===!0?{get:t=>Math.abs(t.scrollLeft),set:(t,n)=>{t.scrollLeft=-n}}:e.vertical===!0?{get:t=>t.scrollTop,set:(t,n)=>{t.scrollTop=n}}:{get:t=>t.scrollLeft,set:(t,n)=>{t.scrollLeft=n}});function Be(t){const n=y.value,{get:u,set:C}=Ae.value;let P=!1,f=u(n);const d=t<f?-1:1;return f+=d*5,f<0?(P=!0,f=0):(d===-1&&f<=t||d===1&&f>=t)&&(P=!0,f=t),C(n,f),z(),P}function ge(t,n){for(const u in t)if(t[u]!==n[u])return!1;return!0}function Re(){let t=null,n={matchedLen:0,queryDiff:9999,hrefLen:0};const u=s.filter(d=>d.routeData!==void 0&&d.routeData.hasRouterLink.value===!0),{hash:C,query:P}=l.$route,f=Object.keys(P).length;for(const d of u){const _=d.routeData.exact.value===!0;if(d.routeData[_===!0?"linkIsExactActive":"linkIsActive"].value!==!0)continue;const{hash:Q,query:oe,matched:Ee,href:Fe}=d.routeData.resolvedLink.value,se=Object.keys(oe).length;if(_===!0){if(Q!==C||se!==f||ge(P,oe)===!1)continue;t=d.name.value;break}if(Q!==""&&Q!==C||se!==0&&ge(oe,P)===!1)continue;const N={matchedLen:Ee.length,queryDiff:f-se,hrefLen:Fe.length-Q.length};if(N.matchedLen>n.matchedLen){t=d.name.value,n=N;continue}else if(N.matchedLen!==n.matchedLen)continue;if(N.queryDiff<n.queryDiff)t=d.name.value,n=N;else if(N.queryDiff!==n.queryDiff)continue;N.hrefLen>n.hrefLen&&(t=d.name.value,n=N)}t===null&&s.some(d=>d.routeData===void 0&&d.name.value===x.value)===!0||ie({name:t,setCurrent:!0})}function Le(t){if(I(),M.value!==!0&&m.value!==null&&t.target&&typeof t.target.closest=="function"){const n=t.target.closest(".q-tab");n&&m.value.contains(n)===!0&&(M.value=!0,B.value===!0&&j(n))}}function $e(){L(()=>{M.value=!1},30)}function Y(){pe.avoidRouteWatcher===!1?b(Re):E()}function be(){if(R===void 0){const t=O(()=>l.$route.fullPath,Y);R=()=>{t(),R=void 0}}}function De(t){s.push(t),S.value++,X(),t.routeData===void 0||l.$route===void 0?b(()=>{if(B.value===!0){const n=x.value,u=n!=null&&n!==""?s.find(C=>C.name.value===n):null;u&&j(u.rootRef.value)}}):(be(),t.routeData.hasRouterLink.value===!0&&Y())}function Ie(t){s.splice(s.indexOf(t),1),S.value--,X(),R!==void 0&&t.routeData!==void 0&&(s.every(n=>n.routeData===void 0)===!0&&R(),Y())}const pe={currentModel:x,tabProps:D,hasFocus:M,hasActiveTab:ae,registerTab:De,unregisterTab:Ie,verifyRouteModel:Y,updateModel:ie,onKbdNavigate:Me,avoidRouteWatcher:!1};Xe(ke,pe);function Te(){q!==null&&clearTimeout(q),W(),R!==void 0&&R()}let ye;return Se(Te),Ye(()=>{ye=R!==void 0,Te()}),Ge(()=>{ye===!0&&be(),X()}),()=>w("div",{ref:m,class:re.value,role:"tablist",onFocusin:Le,onFocusout:$e},[w(lt,{onResize:ve}),w("div",{ref:y,class:c.value,onScroll:z},te(i.default)),w(J,{class:"q-tabs__arrow q-tabs__arrow--left absolute q-tab__icon"+($.value===!0?"":" q-tabs__arrow--faded"),name:e.leftIcon||r.iconSet.tabs[e.vertical===!0?"up":"left"],onMousedownPassive:he,onTouchstartPassive:he,onMouseupPassive:W,onMouseleavePassive:W,onTouchendPassive:W}),w(J,{class:"q-tabs__arrow q-tabs__arrow--right absolute q-tab__icon"+(F.value===!0?"":" q-tabs__arrow--faded"),name:e.rightIcon||r.iconSet.tabs[e.vertical===!0?"down":"right"],onMousedownPassive:me,onTouchstartPassive:me,onMouseupPassive:W,onMouseleavePassive:W,onTouchendPassive:W})])}});function pt(e){const i=[.06,6,50];return typeof e=="string"&&e.length&&e.split(":").forEach((v,l)=>{const r=parseFloat(v);r&&(i[l]=r)}),i}var Tt=Je({name:"touch-swipe",beforeMount(e,{value:i,arg:v,modifiers:l}){if(l.mouse!==!0&&U.has.touch!==!0)return;const r=l.mouseCapture===!0?"Capture":"",a={handler:i,sensitivity:pt(v),direction:we(l),noop:Ze,mouseStart(o){qe(o,a)&&et(o)&&(G(a,"temp",[[document,"mousemove","move",`notPassive${r}`],[document,"mouseup","end","notPassiveCapture"]]),a.start(o,!0))},touchStart(o){if(qe(o,a)){const T=o.target;G(a,"temp",[[T,"touchmove","move","notPassiveCapture"],[T,"touchcancel","end","notPassiveCapture"],[T,"touchend","end","notPassiveCapture"]]),a.start(o)}},start(o,T){U.is.firefox===!0&&ce(e,!0);const L=Pe(o);a.event={x:L.left,y:L.top,time:Date.now(),mouse:T===!0,dir:!1}},move(o){if(a.event===void 0)return;if(a.event.dir!==!1){H(o);return}const T=Date.now()-a.event.time;if(T===0)return;const L=Pe(o),I=L.left-a.event.x,b=Math.abs(I),E=L.top-a.event.y,m=Math.abs(E);if(a.event.mouse!==!0){if(b<a.sensitivity[1]&&m<a.sensitivity[1]){a.end(o);return}}else if(window.getSelection().toString()!==""){a.end(o);return}else if(b<a.sensitivity[2]&&m<a.sensitivity[2])return;const y=b/T,x=m/T;a.direction.vertical===!0&&b<m&&b<100&&x>a.sensitivity[0]&&(a.event.dir=E<0?"up":"down"),a.direction.horizontal===!0&&b>m&&m<100&&y>a.sensitivity[0]&&(a.event.dir=I<0?"left":"right"),a.direction.up===!0&&b<m&&E<0&&b<100&&x>a.sensitivity[0]&&(a.event.dir="up"),a.direction.down===!0&&b<m&&E>0&&b<100&&x>a.sensitivity[0]&&(a.event.dir="down"),a.direction.left===!0&&b>m&&I<0&&m<100&&y>a.sensitivity[0]&&(a.event.dir="left"),a.direction.right===!0&&b>m&&I>0&&m<100&&y>a.sensitivity[0]&&(a.event.dir="right"),a.event.dir!==!1?(H(o),a.event.mouse===!0&&(document.body.classList.add("no-pointer-events--children"),document.body.classList.add("non-selectable"),ct(),a.styleCleanup=B=>{a.styleCleanup=void 0,document.body.classList.remove("non-selectable");const $=()=>{document.body.classList.remove("no-pointer-events--children")};B===!0?setTimeout($,50):$()}),a.handler({evt:o,touch:a.event.mouse!==!0,mouse:a.event.mouse,direction:a.event.dir,duration:T,distance:{x:b,y:m}})):a.end(o)},end(o){a.event!==void 0&&(de(a,"temp"),U.is.firefox===!0&&ce(e,!1),a.styleCleanup!==void 0&&a.styleCleanup(!0),o!==void 0&&a.event.dir!==!1&&H(o),a.event=void 0)}};if(e.__qtouchswipe=a,l.mouse===!0){const o=l.mouseCapture===!0||l.mousecapture===!0?"Capture":"";G(a,"main",[[e,"mousedown","mouseStart",`passive${o}`]])}U.has.touch===!0&&G(a,"main",[[e,"touchstart","touchStart",`passive${l.capture===!0?"Capture":""}`],[e,"touchmove","noop","notPassiveCapture"]])},updated(e,i){const v=e.__qtouchswipe;v!==void 0&&(i.oldValue!==i.value&&(typeof i.value!="function"&&v.end(),v.handler=i.value),v.direction=we(i.modifiers))},beforeUnmount(e){const i=e.__qtouchswipe;i!==void 0&&(de(i,"main"),de(i,"temp"),U.is.firefox===!0&&ce(e,!1),i.styleCleanup!==void 0&&i.styleCleanup(),delete e.__qtouchswipe)}});const yt={name:{required:!0},disable:Boolean},xe={setup(e,{slots:i}){return()=>w("div",{class:"q-panel scroll",role:"tabpanel"},te(i.default))}},Ct={modelValue:{required:!0},animated:Boolean,infinite:Boolean,swipeable:Boolean,vertical:Boolean,transitionPrev:String,transitionNext:String,transitionDuration:{type:[String,Number],default:300},keepAlive:Boolean,keepAliveInclude:[String,Array,RegExp],keepAliveExclude:[String,Array,RegExp],keepAliveMax:Number},Pt=["update:modelValue","beforeTransition","transition"];function wt(){const{props:e,emit:i,proxy:v}=Z(),{getCache:l}=dt();let r,a;const o=p(null),T=p(null);function L(c){const h=e.vertical===!0?"up":"left";k((v.$q.lang.rtl===!0?-1:1)*(c.direction===h?1:-1))}const I=g(()=>[[Tt,L,void 0,{horizontal:e.vertical!==!0,vertical:e.vertical,mouse:!0}]]),b=g(()=>e.transitionPrev||`slide-${e.vertical===!0?"down":"right"}`),E=g(()=>e.transitionNext||`slide-${e.vertical===!0?"up":"left"}`),m=g(()=>`--q-transition-duration: ${e.transitionDuration}ms`),y=g(()=>typeof e.modelValue=="string"||typeof e.modelValue=="number"?e.modelValue:String(e.modelValue)),x=g(()=>({include:e.keepAliveInclude,exclude:e.keepAliveExclude,max:e.keepAliveMax})),B=g(()=>e.keepAliveInclude!==void 0||e.keepAliveExclude!==void 0);O(()=>e.modelValue,(c,h)=>{const A=s(c)===!0?S(c):-1;a!==!0&&q(A===-1?0:A<S(h)?-1:1),o.value!==A&&(o.value=A,i("beforeTransition",c,h),tt(()=>{i("transition",c,h)}))});function $(){k(1)}function F(){k(-1)}function K(c){i("update:modelValue",c)}function s(c){return c!=null&&c!==""}function S(c){return r.findIndex(h=>h.props.name===c&&h.props.disable!==""&&h.props.disable!==!0)}function M(){return r.filter(c=>c.props.disable!==""&&c.props.disable!==!0)}function q(c){const h=c!==0&&e.animated===!0&&o.value!==-1?"q-transition--"+(c===-1?b.value:E.value):null;T.value!==h&&(T.value=h)}function k(c,h=o.value){let A=h+c;for(;A!==-1&&A<r.length;){const V=r[A];if(V!==void 0&&V.props.disable!==""&&V.props.disable!==!0){q(c),a=!0,i("update:modelValue",V.props.name),setTimeout(()=>{a=!1});return}A+=c}e.infinite===!0&&r.length!==0&&h!==-1&&h!==r.length&&k(c,c===-1?r.length:-1)}function R(){const c=S(e.modelValue);return o.value!==c&&(o.value=c),!0}function D(){const c=s(e.modelValue)===!0&&R()&&r[o.value];return e.keepAlive===!0?[w(rt,x.value,[w(B.value===!0?l(y.value,()=>({...xe,name:y.value})):xe,{key:y.value,style:m.value},()=>c)])]:[w("div",{class:"q-panel scroll",style:m.value,key:y.value,role:"tabpanel"},[c])]}function ae(){if(r.length!==0)return e.animated===!0?[w(at,{name:T.value},D)]:D()}function ne(c){return r=nt(te(c.default,[])).filter(h=>h.props!==null&&h.props.slot===void 0&&s(h.props.name)===!0),r.length}function re(){return r}return Object.assign(v,{next:$,previous:F,goTo:K}),{panelIndex:o,panelDirectives:I,updatePanelsList:ne,updatePanelIndex:R,getPanelContent:ae,getEnabledPanels:M,getPanels:re,isValidPanelName:s,keepAliveProps:x,needsUniqueKeepAliveWrapper:B,goToPanelByOffset:k,goToPanel:K,nextPanel:$,previousPanel:F}}var Lt=ee({name:"QTabPanel",props:yt,setup(e,{slots:i}){return()=>w("div",{class:"q-tab-panel",role:"tabpanel"},te(i.default))}}),$t=ee({name:"QTabPanels",props:{...Ct,...it},emits:Pt,setup(e,{slots:i}){const v=Z(),l=ot(e,v.proxy.$q),{updatePanelsList:r,getPanelContent:a,panelDirectives:o}=wt(),T=g(()=>"q-tab-panels q-panel-parent"+(l.value===!0?" q-tab-panels--dark q-dark":""));return()=>(r(i),st("div",{class:T.value},a(),"pan",e.swipeable,()=>o.value))}});export{Rt as Q,Bt as a,$t as b,Lt as c,At as u};
