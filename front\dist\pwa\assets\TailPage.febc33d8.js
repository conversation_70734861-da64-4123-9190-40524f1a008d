import{k as ge,r as _,c as ke,b as Ee,w as ue,I as l,J as q,L as d,a2 as W,M as r,a3 as Z,P as t,O as o,az as u,aC as f,aA as v,A as p,aB as $,bk as Ce,N as y,ba as F,a8 as ie,b7 as Fe,bb as De,a5 as Be,aD as Ae,a6 as qe}from"./index.5cb20bf8.js";import{Q as J,c as ne}from"./QSelect.a87fec46.js";import{Q as xe,a as $e}from"./QItem.ecbdb62a.js";import{u as Ve,Q as Se}from"./useLotteryAnalysis.b196cc0f.js";import{Q as Re}from"./QLinearProgress.7b973567.js";import{Q as Ne}from"./QPage.070ae342.js";import{Q as Pe,a as ve,b as Le,c as be,u as Qe}from"./QTabPanels.ec4a05ed.js";import{p as k,u as he,_ as ze}from"./IndexPage.82a3c419.js";import{L as _e}from"./lotto.3734c012.js";import{Q as ye}from"./QPagination.c60c89d4.js";import{Q as Te,a as we,b as z,c as Me}from"./QTable.41dda7e7.js";import{u as Ue}from"./use-quasar.ee12aee5.js";import{Q as je}from"./QSpace.7a70be6f.js";import{_ as Ie}from"./plugin-vue_export-helper.21dcd24c.js";import"./QResizeObserver.55c7aa89.js";import"./touch.9135741d.js";import"./use-render-cache.3aae9b27.js";import"./QPopupProxy.73021305.js";import"./QList.116dfdf0.js";const Oe={class:"row q-gutter-y-md"},Ge={class:"col-12 col-sm-4 draw-title text-center"},We={class:"text-period"},Je={class:"text-draw-date"},Ze={class:"col-12 col-sm-6 self-center"},He={class:"row justify-center"},Ke={key:0,class:"col-auto"},Xe={class:"row q-my-md q-gutter-sm items-center"},Ye={class:"col-sm-auto"},et={class:"col-sm-auto"},tt={class:"col-sm-auto"},lt={class:"row q-my-md"},st={class:"col-auto text-h6"},ut={class:"row q-col-gutter-sm"},at={class:"row items-center"},ot={class:"ball tail-number"},rt={key:0,class:"row q-my-md justify-center"},it={class:"row q-col-gutter-md"},nt={key:0,class:"col-12 q-my-sm"},dt={class:"row q-col-gutter-md items-center text-h6"},ct={class:"col-12 col-sm-4"},mt={class:"col-12 col-sm-6"},pt={class:"row q-col-gutter-md"},vt={class:"col-auto"},bt={class:"col-auto"},gt={class:"col-12 col-sm-2 text-center"},ft={key:1,class:"row justify-center"},_t={class:"row q-my-md"},yt={class:"row q-my-sm"},wt={class:"col text-h6 text-bold"},kt={class:"row q-gutter-xs"},ht={class:"row items-center"},Et={class:"text-h6"},Ct={class:"row justify-center"},Ft={key:0,class:"col-auto"},Dt={class:"row items-center"},Bt={class:"col-12 q-mb-sm text-h6 text-center"},At={class:"col-12 col"},qt={class:"row justify-center"},xt={key:0,class:"col-auto"},$t={class:"col-12 q-mt-md"},Vt={class:"row q-gutter-xs justify-center"},St={class:"text-subtitle1",style:{"border-bottom":"1px solid black"}},Rt=ge({__name:"TailFollowResult",props:{isSuperLotto:{type:Boolean},drawResults:{},predictResult:{},drawTailResults:{},rdResults:{},occurrenceResults:{},pageSize:{}},emits:["view-detail"],setup(ce){const h=ce,x=!Ue().platform.is.desktop,P=_("1"),V=_(1),B=_({pageItems:[],targetNumAppearances:new Map,totalCount:0,totalPages:0}),C=_(1),de=[{label:"\u4E00\u661F\u7D44\u5408",value:1},{label:"\u4E8C\u661F\u7D44\u5408",value:2},{label:"\u4E09\u661F\u7D44\u5408",value:3},{label:"\u56DB\u661F\u7D44\u5408",value:4},{label:"\u4E94\u661F\u7D44\u5408",value:5}],H=_(new Map),I=_(1),n=_(1),g=ke(()=>Array.from({length:n.value},(i,a)=>({label:`\u6E96\u904E ${a+1} \u6B21\u4EE5\u4E0A`,value:a+1}))),E=_(.5),R=_([{label:"\u6E96\u78BA\u7387 100%",value:1},{label:"\u6E96\u78BA\u7387 90% \u4EE5\u4E0A",value:.9},{label:"\u6E96\u78BA\u7387 80% \u4EE5\u4E0A",value:.8},{label:"\u6E96\u78BA\u7387 70% \u4EE5\u4E0A",value:.7},{label:"\u6E96\u78BA\u7387 60% \u4EE5\u4E0A",value:.6},{label:"\u6E96\u78BA\u7387 50% \u4EE5\u4E0A",value:.5}]),T=_("count"),ee=_([{label:"\u6E96\u78BA\u6B21\u6578\u7D71\u8A08",value:"count"},{label:"\u9810\u6E2C\u7D44\u6578\u7D71\u8A08",value:"group"}]),ae=[{name:"period",label:"\u671F\u6578",field:"period",align:"center"},{name:"draw_number_size",label:"\u958B\u734E\u865F\u78BC",field:"draw_number_size",align:"center",format:i=>i.join(" ")},{name:"tail1",label:"\u5C3E1",field:"tail1",align:"center"},{name:"tail2",label:"\u5C3E2",field:"tail2",align:"center"},{name:"tail3",label:"\u5C3E3",field:"tail3",align:"center"},{name:"tail4",label:"\u5C3E4",field:"tail4",align:"center"},{name:"tail5",label:"\u5C3E5",field:"tail5",align:"center"},{name:"tail6",label:"\u5C3E6",field:"tail6",align:"center"},{name:"tail7",label:"\u5C3E7",field:"tail7",align:"center"},{name:"tail8",label:"\u5C3E8",field:"tail8",align:"center"},{name:"tail9",label:"\u5C3E9",field:"tail9",align:"center"},{name:"tail0",label:"\u5C3E0",field:"tail0",align:"center"}],M=_(!1);Ee(()=>{M.value=!1,le(),U(),M.value=!0}),ue(()=>I.value,()=>{!M.value||U()}),ue(()=>E.value,()=>{!M.value||U()}),ue(()=>T.value,()=>{!M.value||U()}),ue(()=>V.value,()=>{!M.value||K()}),ue(()=>C.value,()=>{O()});const O=()=>{const i=new Map;for(const a of h.drawTailResults){const e=te(a.numbers,C.value),s=Array.from(e);for(const L of s){let w="";for(let N=0;N<L.length;N++)w+=`${L[N]}`,N<L.length-1&&(w+=", ");const m=i.get(w);m?i.set(w,m+1):i.set(w,1)}const c=Array.from(i.entries()).sort((L,w)=>w[1]-L[1]);H.value=new Map(c)}},Q=i=>{const a=te(h.predictResult.tailSet||[],C.value),e=Array.from(a),s=[];for(const c of e){let L="";for(let w=0;w<c.length;w++)L+=`${c[w]}`,w<c.length-1&&(L+=", ");s.push(L)}return s.includes(i)};function te(i,a){return b(i,a)}function*b(i,a,e=0,s=[]){if(s.length===a){yield[...s];return}for(let c=e;c<i.length;c++)s.push(i[c]),yield*b(i,a,c+1,s),s.pop()}const le=()=>{var i;V.value=1,C.value=1,O();for(let a of h.drawResults){a.tails=new Map;for(let s=0;s<10;s++)a.tails.set(s,[]);let e=[...a.draw_number_size];!h.isSuperLotto&&a.special_number&&(e.push(a.special_number),e=e.sort((s,c)=>s-c));for(const s of e){const c=s%10;(i=a.tails.get(c))==null||i.push(s)}}},oe=i=>{var e;const a=i.firstNumbers.join(",")+"-"+i.secondNumbers.join(",")+"-"+i.gap+"-"+i.targetGap;return((e=h.occurrenceResults.get(a))==null?void 0:e.count)||0},S=_([]),U=()=>{S.value=[],V.value=1;const i=new Map;n.value=1;for(const e of h.rdResults)if(e.targetMatches>=I.value&&e.targetProbability>=E.value){if(S.value.push(e),e.targetMatches>n.value&&(n.value=e.targetMatches),T.value==="count")for(const c of e.targetNumbers){const L=e.targetMatches;i.set(c,(i.get(c)||0)+L)}else if(T.value==="group")for(const c of e.targetNumbers)i.set(c,(i.get(c)||0)+1)}const a=Array.from(i.entries()).sort((e,s)=>s[1]-e[1]);B.value.targetNumAppearances=new Map(a),K()},K=()=>{const i=(V.value-1)*h.pageSize,a=i+h.pageSize;B.value.pageItems=S.value.slice(i,a),B.value.totalCount=S.value.length,B.value.totalPages=Math.ceil(S.value.length/h.pageSize)};return(i,a)=>(l(),q(W,{class:"q-mt-md"},{default:d(()=>[r(Z,null,{default:d(()=>[r(Pe,{modelValue:P.value,"onUpdate:modelValue":a[0]||(a[0]=e=>P.value=e),dense:"",align:"justify",class:"text-h6","active-color":"primary","indicator-color":"primary"},{default:d(()=>[r(ve,{name:"1",label:"\u5206\u6790\u7D50\u679C"}),r(ve,{name:"2",label:"\u7D44\u5408\u7D71\u8A08\u7D50\u679C"}),r(ve,{name:"3",label:"\u958B\u734E\u7D50\u679C"})]),_:1},8,["modelValue"]),i.predictResult.period?(l(),q(W,{key:0,bordered:"",class:"ball-card full-width q-my-lg"},{default:d(()=>[r(Z,null,{default:d(()=>[t("div",Oe,[t("div",Ge,[a[8]||(a[8]=t("div",{class:"text-h6"},"\u9810\u6E2C\u958B\u734E\u7D50\u679C",-1)),t("div",We,"\u7B2C "+o(i.predictResult.period)+" \u671F",1),t("div",Je," \u958B\u734E\u65E5\u671F\uFF1A"+o(i.predictResult.draw_date),1)]),t("div",Ze,[t("div",He,[(l(!0),u(v,null,f(i.predictResult.draw_number_size,e=>(l(),u("div",{key:e,class:"col-auto"},[(l(),u("div",{class:"ball",key:e},o(p(k)(e)),1))]))),128)),i.predictResult.special_number?(l(),u("div",Ke,[(l(),u("div",{class:"ball special-number",key:i.predictResult.special_number},o(p(k)(i.predictResult.special_number)),1))])):$("",!0)])])])]),_:1})]),_:1})):$("",!0),r(Le,{modelValue:P.value,"onUpdate:modelValue":a[7]||(a[7]=e=>P.value=e)},{default:d(()=>[r(be,{name:"1"},{default:d(()=>[t("div",Xe,[a[9]||(a[9]=t("div",{class:"col-sm-auto text-h6"},"\u7BE9\u9078",-1)),t("div",Ye,[r(J,{outlined:"",dense:"",modelValue:I.value,"onUpdate:modelValue":a[1]||(a[1]=e=>I.value=e),options:g.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])]),t("div",et,[r(J,{outlined:"",dense:"",modelValue:E.value,"onUpdate:modelValue":a[2]||(a[2]=e=>E.value=e),options:R.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])]),t("div",tt,[r(J,{outlined:"",dense:"",modelValue:T.value,"onUpdate:modelValue":a[3]||(a[3]=e=>T.value=e),options:ee.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])])]),t("div",lt,[t("div",st," \u5171 "+o(B.value.totalCount)+" \u7B46\u8CC7\u6599 ",1)]),a[12]||(a[12]=t("div",{class:"row q-my-sm"},[t("label",{class:"col text-h6 text-bold"},"\u9810\u6E2C\u7D50\u679C")],-1)),r(W,{flat:"",bordered:"",class:"q-pa-sm q-mb-md text-subtitle1 appearance"},{default:d(()=>[t("div",ut,[(l(!0),u(v,null,f(B.value.targetNumAppearances.keys(),e=>{var s;return l(),u("div",{class:F(["col-4 col-md-2",{predict:(s=i.predictResult.tailSet)==null?void 0:s.includes(e)}]),key:e},[t("div",at,[t("span",ot,o(e),1),y(" ("+o(B.value.targetNumAppearances.get(e))+"\u6B21) ",1)])],2)}),128))])]),_:1}),B.value.totalPages>1?(l(),u("div",rt,[r(ye,{modelValue:V.value,"onUpdate:modelValue":a[4]||(a[4]=e=>V.value=e),max:B.value.totalPages,input:!0},null,8,["modelValue","max"])])):$("",!0),t("div",it,[B.value.pageItems.length===0?(l(),u("div",nt,[r(W,{flat:"",bordered:""},{default:d(()=>[r(Z,null,{default:d(()=>a[10]||(a[10]=[t("div",{class:"text-h6 text-center"},"\u6C92\u6709\u7B26\u5408\u689D\u4EF6\u7684\u7D50\u679C",-1)])),_:1})]),_:1})])):$("",!0),(l(!0),u(v,null,f(B.value.pageItems,(e,s)=>(l(),u("div",{key:s,class:"col-12 q-my-sm"},[r(W,{flat:"",bordered:""},{default:d(()=>[r(Z,null,{default:d(()=>[t("div",dt,[t("div",ct,[t("div",null,[a[11]||(a[11]=y(" \u958B\u51FA ")),(l(!0),u(v,null,f(e.firstNumbers,c=>(l(),q(ne,{key:c,color:"primary","text-color":"white",size:"lg",dense:""},{default:d(()=>[y(o(c)+"\u5C3E ",1)]),_:2},1024))),128)),y(" \u4E0B"+o(e.gap)+"\u671F\u958B ",1),(l(!0),u(v,null,f(e.secondNumbers,c=>(l(),q(ne,{key:c,color:"secondary","text-color":"white",size:"lg",dense:""},{default:d(()=>[y(o(c)+"\u5C3E ",1)]),_:2},1024))),128)),y(" \u518D\u4E0B"+o(e.targetGap)+"\u671F\u9810\u6E2C\u62D6\u51FA ",1),(l(!0),u(v,null,f(e.targetNumbers,c=>(l(),q(ne,{key:c,color:"accent","text-color":"white",size:"lg",dense:""},{default:d(()=>[y(o(c)+"\u5C3E ",1)]),_:2},1024))),128))])]),t("div",mt,[t("div",pt,[t("div",vt," \u958B\u51FA"+o(oe(e))+"\u6B21\uFF0C\u5171\u6E96"+o(e.targetMatches)+"\u6B21 ",1),t("div",bt," \u6E96\u78BA\u7387: "+o((e.targetProbability*100).toFixed(2))+"% ",1)])]),t("div",gt,[r(ie,{dense:"",color:"primary",icon:"visibility",label:"\u6AA2\u8996\u8A73\u60C5",class:"text-subtitle1 text-bold",onClick:c=>i.$emit("view-detail",e)},null,8,["onClick"])])])]),_:2},1024)]),_:2},1024)]))),128))]),B.value.totalPages>1?(l(),u("div",ft,[r(ye,{modelValue:V.value,"onUpdate:modelValue":a[5]||(a[5]=e=>V.value=e),max:B.value.totalPages,input:!0},null,8,["modelValue","max"])])):$("",!0)]),_:1}),r(be,{name:"2"},{default:d(()=>[t("div",_t,[a[13]||(a[13]=t("span",{class:"text-h6 q-mr-sm"}," \u5C3E\u6578\u7D44\u5408\uFF1A ",-1)),r(J,{modelValue:C.value,"onUpdate:modelValue":a[6]||(a[6]=e=>C.value=e),options:de,"map-options":"","emit-value":"",outlined:"",dense:""},null,8,["modelValue"])]),t("div",yt,[t("label",wt," \u5C3E\u6578\u7D44\u5408\u51FA\u73FE\u6B21\u6578 ("+o(i.drawResults.length)+"\u671F) ",1)]),r(W,{flat:"",bordered:"",class:"q-pa-sm q-mb-md text-subtitle1 appearance"},{default:d(()=>[t("div",kt,[(l(!0),u(v,null,f(H.value.keys(),e=>(l(),u("div",{class:F(["col-4 col-md-2",{predict:Q(e)}]),key:e},[t("div",ht,[(l(!0),u(v,null,f(e.split(","),s=>(l(),u("span",{class:"ball tail-number",key:s},o(s),1))),128)),y(" ("+o(H.value.get(e))+"\u6B21) ",1)])],2))),128))])]),_:1})]),_:1}),r(be,{name:"3"},{default:d(()=>[r(Te,{rows:i.drawResults,columns:ae,"rows-per-page-options":[0],"hide-bottom":"",separator:"cell",class:"q-mt-lg"},Ce({header:d(e=>[x?$("",!0):(l(),q(we,{key:0,props:e,class:"bg-primary text-white"},{default:d(()=>[(l(!0),u(v,null,f(e.cols,s=>(l(),q(Me,{key:s.name,props:e},{default:d(()=>[y(o(s.label),1)]),_:2},1032,["props"]))),128))]),_:2},1032,["props"]))]),_:2},[x?{name:"body",fn:d(e=>[(l(),q(W,{square:"",bordered:"",key:e.row.period},{default:d(()=>[r(Z,{class:"q-px-none q-py-sm"},{default:d(()=>[t("div",Dt,[t("div",Bt,[t("span",null,o(e.row.period)+"\u671F ",1),a[25]||(a[25]=t("span",null," | ",-1)),t("span",null,o(e.row.drawDate),1)]),t("div",At,[t("div",qt,[(l(!0),u(v,null,f(e.row.draw_number_size,s=>(l(),u("div",{key:s,class:"col-auto"},[(l(),u("div",{class:"ball",key:s},o(p(k)(s)),1))]))),128)),e.row.special_number?(l(),u("div",xt,[(l(),u("div",{class:"ball special-number",key:e.row.special_number},o(p(k)(e.row.special_number)),1))])):$("",!0)])]),t("div",$t,[t("div",Vt,[(l(),u(v,null,f([1,2,3,4,5,6,7,8,9,0],s=>t("div",{class:"col-1 text-center",style:{border:"1px solid black"},key:s},[t("div",St," \u5C3E"+o(s),1),t("div",null,[(l(!0),u(v,null,f(e.row.tails.get(s),c=>(l(),u("span",{key:c,class:F(["text-h6",{"text-negative":c===e.row.special_number&&!i.isSuperLotto}])},[y(o(p(k)(c)),1),a[26]||(a[26]=t("br",null,null,-1))],2))),128))])])),64))])])])]),_:2},1024)]),_:2},1024))]),key:"1"}:{name:"body",fn:d(e=>[r(we,{props:e},{default:d(()=>[r(z,{key:"period",props:e},{default:d(()=>[t("div",Et,[y(o(e.row.period)+" ",1),a[14]||(a[14]=t("br",null,null,-1)),y(" "+o(e.row.draw_date),1)])]),_:2},1032,["props"]),r(z,{key:"draw_number_size",props:e},{default:d(()=>[t("div",Ct,[(l(!0),u(v,null,f(e.row.draw_number_size,s=>(l(),u("div",{key:s,class:"col-auto"},[(l(),u("div",{class:"ball",key:s},o(p(k)(s)),1))]))),128)),e.row.special_number?(l(),u("div",Ft,[(l(),u("div",{class:"ball special-number",key:e.row.special_number},o(p(k)(e.row.special_number)),1))])):$("",!0)])]),_:2},1032,["props"]),r(z,{key:"tail1",props:e},{default:d(()=>[(l(!0),u(v,null,f(e.row.tails.get(1),s=>(l(),u("span",{key:s,class:F(["text-h6",{"text-negative":s===e.row.special_number&&!i.isSuperLotto}])},[y(o(p(k)(s))+" ",1),a[15]||(a[15]=t("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),r(z,{key:"tail2",props:e},{default:d(()=>[(l(!0),u(v,null,f(e.row.tails.get(2),s=>(l(),u("span",{key:s,class:F(["text-h6",{"text-negative":s===e.row.special_number&&!i.isSuperLotto}])},[y(o(p(k)(s)),1),a[16]||(a[16]=t("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),r(z,{key:"tail3",props:e},{default:d(()=>[(l(!0),u(v,null,f(e.row.tails.get(3),s=>(l(),u("span",{key:s,class:F(["text-h6",{"text-negative":s===e.row.special_number&&!i.isSuperLotto}])},[y(o(p(k)(s)),1),a[17]||(a[17]=t("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),r(z,{key:"tail4",props:e},{default:d(()=>[(l(!0),u(v,null,f(e.row.tails.get(4),s=>(l(),u("span",{key:s,class:F(["text-h6",{"text-negative":s===e.row.special_number&&!i.isSuperLotto}])},[y(o(p(k)(s)),1),a[18]||(a[18]=t("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),r(z,{key:"tail5",props:e},{default:d(()=>[(l(!0),u(v,null,f(e.row.tails.get(5),s=>(l(),u("span",{key:s,class:F(["text-h6",{"text-negative":s===e.row.special_number&&!i.isSuperLotto}])},[y(o(p(k)(s)),1),a[19]||(a[19]=t("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),r(z,{key:"tail6",props:e},{default:d(()=>[(l(!0),u(v,null,f(e.row.tails.get(6),s=>(l(),u("span",{key:s,class:F(["text-h6",{"text-negative":s===e.row.special_number&&!i.isSuperLotto}])},[y(o(p(k)(s)),1),a[20]||(a[20]=t("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),r(z,{key:"tail7",props:e},{default:d(()=>[(l(!0),u(v,null,f(e.row.tails.get(7),s=>(l(),u("span",{key:s,class:F(["text-h6",{"text-negative":s===e.row.special_number&&!i.isSuperLotto}])},[y(o(p(k)(s)),1),a[21]||(a[21]=t("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),r(z,{key:"tail8",props:e},{default:d(()=>[(l(!0),u(v,null,f(e.row.tails.get(8),s=>(l(),u("span",{key:s,class:F(["text-h6",{"text-negative":s===e.row.special_number&&!i.isSuperLotto}])},[y(o(p(k)(s)),1),a[22]||(a[22]=t("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),r(z,{key:"tail9",props:e},{default:d(()=>[(l(!0),u(v,null,f(e.row.tails.get(9),s=>(l(),u("span",{key:s,class:F(["text-h6",{"text-negative":s===e.row.special_number&&!i.isSuperLotto}])},[y(o(p(k)(s)),1),a[23]||(a[23]=t("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),r(z,{key:"tail0",props:e},{default:d(()=>[(l(!0),u(v,null,f(e.row.tails.get(0),s=>(l(),u("span",{key:s,class:F(["text-h6",{"text-negative":s===e.row.special_number&&!i.isSuperLotto}])},[y(o(p(k)(s)),1),a[24]||(a[24]=t("br",null,null,-1))],2))),128))]),_:2},1032,["props"])]),_:2},1032,["props"])]),key:"0"}]),1032,["rows"])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}))}});const Nt={class:"text-h6 q-mb-md"},Pt={class:"text-h6 q-mb-md"},Lt={class:"reverse-order",style:{display:"flex","flex-direction":"column-reverse"}},Qt={class:"row text-h6 q-mb-md"},zt={class:"col-auto"},Tt={class:"row balls"},Mt={key:0,class:"col-auto"},Ut={key:0,class:"row text-h6 q-mb-md"},jt={class:"col-auto"},It={class:"row balls"},Ot={key:0,class:"col-auto"},Gt={key:1,class:"row text-h6"},Wt={class:"col-auto"},Jt={class:"row balls"},Zt={key:0,class:"col-auto"},Ht={key:2,class:"predict-section"},Kt={class:"row text-h6 q-mt-sm"},Xt={class:"col-auto"},Yt={key:0,class:"predict-period"},el={class:"predict-date"},tl={class:"row balls"},ll={key:0,class:"col-auto"},sl={key:1,class:"col-auto pending-draw"},ul=ge({__name:"TailFollowDetail",props:{modelValue:{type:Boolean},results:{},selectedDetail:{},occurrences:{},predictResult:{}},emits:["update:modelValue"],setup(ce,{emit:h}){const D=ce,x=he(),P=ke({get:()=>D.modelValue,set:n=>V("update:modelValue",n)}),V=h,B=n=>{if(!n)return"";const g=D.results.find(E=>E.period==n);return g==null?void 0:g.draw_date},C=n=>{if(!n)return{numbers:[],specialNumber:0};const g=D.results.find(E=>E.period==n);return{numbers:g==null?void 0:g.draw_number_size,specialNumber:g!=null&&g.special_number?Number(g.special_number):0}},de=n=>{var E,R;if(!n)return 0;const g=n.firstNumbers.join(",")+"-"+n.secondNumbers.join(",")+"-"+n.gap+"-"+n.targetGap;return(R=(E=D.occurrences.get(g))==null?void 0:E.count)!=null?R:0},H=n=>{var E,R;if(!n)return[];const g=n.firstNumbers.join(",")+"-"+n.secondNumbers.join(",")+"-"+n.gap+"-"+n.targetGap;return(R=(E=D.occurrences.get(g))==null?void 0:E.periods)!=null?R:[]},I=n=>n.targetPeriod===void 0;return(n,g)=>(l(),q(Fe,{modelValue:P.value,"onUpdate:modelValue":g[1]||(g[1]=E=>P.value=E)},{default:d(()=>[r(W,{style:{"max-width":"100%",width:"800px"}},{default:d(()=>[r(Z,{class:"row items-center"},{default:d(()=>[g[2]||(g[2]=t("div",{class:"text-h6"},"\u8A73\u7D30\u8CC7\u6599",-1)),r(je),r(ie,{icon:"close",flat:"",round:"",dense:"",onClick:g[0]||(g[0]=E=>P.value=!1)})]),_:1}),r(Z,{class:"q-pa-md"},{default:d(()=>{var E,R,T,ee,ae,M,O,Q,te;return[t("div",Nt,[g[3]||(g[3]=y(" \u958B\u51FA ")),(l(!0),u(v,null,f((E=n.selectedDetail)==null?void 0:E.firstNumbers,b=>(l(),q(ne,{key:b,color:"primary","text-color":"white",class:"text-h6"},{default:d(()=>[y(o(b)+"\u5C3E ",1)]),_:2},1024))),128)),y(" \u4E0B"+o((R=n.selectedDetail)==null?void 0:R.gap)+"\u671F\u958B ",1),(l(!0),u(v,null,f((T=n.selectedDetail)==null?void 0:T.secondNumbers,b=>(l(),q(ne,{key:b,color:"secondary","text-color":"white",class:"text-h6"},{default:d(()=>[y(o(b)+"\u5C3E ",1)]),_:2},1024))),128)),y(" \u518D\u4E0B "+o((ee=n.selectedDetail)==null?void 0:ee.targetGap)+" \u671F\u9810\u6E2C\u62D6\u51FA ",1),(l(!0),u(v,null,f((ae=n.selectedDetail)==null?void 0:ae.targetNumbers,b=>(l(),q(ne,{key:b,color:"accent","text-color":"white",class:"text-h6"},{default:d(()=>[y(o(b)+"\u5C3E ",1)]),_:2},1024))),128))]),t("div",Pt," \u958B\u51FA"+o(de(n.selectedDetail))+"\u6B21\uFF0C\u5171\u6E96"+o((O=(M=n.selectedDetail)==null?void 0:M.targetMatches)!=null?O:0)+"\u6B21\uFF0C\u6E96\u78BA\u7387: "+o((((te=(Q=n.selectedDetail)==null?void 0:Q.targetProbability)!=null?te:0)*100).toFixed(2))+"% ",1),t("div",Lt,[(l(!0),u(v,null,f(H(n.selectedDetail),(b,le)=>(l(),q(W,{key:le,class:"q-mb-md",style:De({"background-color":I(b)?"#e8f5e8":"#fefefe"})},{default:d(()=>[r(Z,null,{default:d(()=>{var oe,S,U,K,i,a;return[t("div",Qt,[t("div",zt,[t("div",null,"\u7B2C "+o(b.firstPeriod)+" \u671F",1),t("div",null," \u958B\u734E\u65E5\u671F\uFF1A"+o(B(b.firstPeriod)),1)]),t("div",Tt,[(l(!0),u(v,null,f(C(b.firstPeriod).numbers,(e,s)=>{var c;return l(),u("div",{class:"col-auto",key:s},[t("div",{class:F(["ball",{"first-num":(c=n.selectedDetail)==null?void 0:c.firstNumbers.includes(e%10)}])},o(p(k)(e)),3)])}),128)),C(b.firstPeriod).specialNumber?(l(),u("div",Mt,[t("div",{class:F(["ball special-number",{"first-num":((oe=n.selectedDetail)==null?void 0:oe.firstNumbers.includes(C(b.firstPeriod).specialNumber%10))&&!p(x).isSuperLotto}])},o(p(k)(C(b.firstPeriod).specialNumber)),3)])):$("",!0)])]),b.secondPeriod?(l(),u("div",Ut,[t("div",jt,[t("div",null,"\u7B2C "+o(b.secondPeriod)+" \u671F",1),t("div",null," \u958B\u734E\u65E5\u671F\uFF1A"+o((S=B(b.secondPeriod))!=null?S:"\u672A\u958B\u734E"),1)]),t("div",It,[(l(!0),u(v,null,f(C(b.secondPeriod).numbers,(e,s)=>{var c;return l(),u("div",{class:"col-auto",key:s},[t("div",{class:F(["ball",{"second-num":(c=n.selectedDetail)==null?void 0:c.secondNumbers.includes(e%10)}])},o(p(k)(e)),3)])}),128)),C(b.secondPeriod).specialNumber?(l(),u("div",Ot,[t("div",{class:F(["ball special-number",{"second-num":((U=n.selectedDetail)==null?void 0:U.secondNumbers.includes(C(b.secondPeriod).specialNumber%10))&&!p(x).isSuperLotto}])},o(p(k)(C(b.secondPeriod).specialNumber)),3)])):$("",!0)])])):$("",!0),b.targetPeriod?(l(),u("div",Gt,[t("div",Wt,[t("div",null,"\u7B2C "+o(b.targetPeriod)+" \u671F",1),t("div",null," \u958B\u734E\u65E5\u671F\uFF1A"+o((K=B(b.targetPeriod))!=null?K:"\u672A\u958B\u734E"),1)]),t("div",Jt,[(l(!0),u(v,null,f(C(b.targetPeriod).numbers,(e,s)=>{var c;return l(),u("div",{class:"col-auto",key:s},[t("div",{class:F(["ball",{"target-num":(c=n.selectedDetail)==null?void 0:c.targetNumbers.includes(e%10)}])},o(p(k)(e)),3)])}),128)),C(b.targetPeriod).specialNumber?(l(),u("div",Zt,[t("div",{class:F(["ball special-number",{"target-num":((i=n.selectedDetail)==null?void 0:i.targetNumbers.includes(C(b.targetPeriod).specialNumber%10))&&!p(x).isSuperLotto}])},o(p(k)(C(b.targetPeriod).specialNumber)),3)])):$("",!0)])])):(l(),u("div",Ht,[t("div",Kt,[t("div",Xt,[n.predictResult.period?(l(),u("div",Yt," \u7B2C "+o(n.predictResult.period)+" \u671F ",1)):$("",!0),t("div",el,[n.predictResult.period?(l(),u(v,{key:0},[y(" \u5BE6\u969B\u958B\u734E\u65E5\u671F\uFF1A"+o(n.predictResult.draw_date),1)],64)):(l(),u(v,{key:1},[y(" \u9810\u6E2C\u671F\u865F\uFF1A\u5C1A\u672A\u958B\u734E ")],64))])]),t("div",tl,[n.predictResult.period?(l(),u(v,{key:0},[(l(!0),u(v,null,f(n.predictResult.draw_number_size,(e,s)=>{var c;return l(),u("div",{class:"col-auto",key:s},[t("div",{class:F(["ball",{"target-num":(c=n.selectedDetail)==null?void 0:c.targetNumbers.includes(e%10)}])},o(p(k)(e)),3)])}),128)),n.predictResult.special_number?(l(),u("div",ll,[t("div",{class:F(["ball special-number",{"target-num":((a=n.selectedDetail)==null?void 0:a.targetNumbers.includes(n.predictResult.special_number%10))&&!p(x).isSuperLotto}])},o(p(k)(n.predictResult.special_number)),3)])):$("",!0)],64)):(l(),u("div",sl,[r(Be,{name:"schedule",size:"lg"}),g[4]||(g[4]=t("span",null,"\u5C1A\u672A\u958B\u734E",-1))]))])])]))]}),_:2},1024)]),_:2},1032,["style"]))),128))])]}),_:1})]),_:1})]),_:1},8,["modelValue"]))}});var al=Ie(ul,[["__scopeId","data-v-796b7358"]]);const ol={class:"row lto-ref q-mb-sm"},rl={class:"col-12 col-sm-4 self-center text-h6"},il={class:"col-12 col-sm-6 self-center text-subtitle1"},nl={class:"row balls"},dl={class:"ball"},cl={key:0,class:"col-auto"},ml={class:"row q-mb-md"},pl={class:"col"},vl={key:1,class:"row q-mb-md"},bl={class:"row q-mb-md"},gl={class:"col-12 col-sm-4 q-pa-sm"},fl={class:"col-12 col-sm-4 q-pa-sm"},_l={class:"col-12 col-sm-4 q-pa-sm"},yl={class:"row q-mb-md"},wl={class:"col-12 col-sm-4"},kl={class:"q-pa-sm"},hl={class:"col-12 col-sm-4"},El={class:"q-pa-sm"},Cl={class:"col-12 col-sm-4"},Fl={class:"q-pa-sm"},Dl={class:"text-center q-mb-sm"},Gl=ge({__name:"TailPage",setup(ce){const h=Qe(),D=he(),x=_(D.getLotto),P=Ve(),V=_(!1),B=()=>{V.value=!0},C=()=>{V.value=!1},de=()=>{V.value=!1};ue(()=>D.getLotto,w=>{w&&(x.value=w)}),ue(()=>{var w;return(w=x.value)==null?void 0:w.period},()=>{Q.value=[]});const H=[{label:"\u4E00\u661F\u7D44\u5408",value:1},{label:"\u4E8C\u661F\u7D44\u5408",value:2},{label:"\u4E09\u661F\u7D44\u5408",value:3},{label:"\u56DB\u661F\u7D44\u5408",value:4},{label:"\u4E94\u661F\u7D44\u5408",value:5}],I=_(1),n=_(1),g=_(1),E=Array.from({length:21},(w,m)=>({label:`${m+10}\u671F`,value:m+10})),R=_(20);let T=_(Array.from({length:991},(w,m)=>({label:`${m+10}\u671F`,value:m+10})));const ee=_(50),ae=(w,m,N)=>{const X=parseInt(w,10);(X<10||X>1e3)&&N(),m(()=>{T.value=Array.from({length:991},(G,se)=>se+10).filter(G=>G.toString().startsWith(w)).map(G=>({label:`${G.toString()}\u671F`,value:G}))})},M=Array.from({length:15},(w,m)=>({label:`\u4E0B${m+1}\u671F`,value:m+1})),O=_(1),Q=_([]),te=_("super_lotto638"),b=_([]),le=_(new Map),oe=_(new Map),S=_({period:"",draw_date:"",draw_number_appear:[],draw_number_size:[],tails:new Map}),U=_([]),K=_(!1),i=async()=>{var w,m,N;try{te.value=D.drawType,K.value=D.isSuperLotto,h.startCalculating(),a();const X=await _e.getLottoList({draw_type:D.getDrawType,date_end:(m=(w=x.value)==null?void 0:w.draw_date)!=null?m:"",limit:ee.value});Q.value=X.data;const G=await _e.getLottoPredict({draw_type:D.getDrawType,draw_date:(N=D.getLotto)==null?void 0:N.draw_date,ahead_count:O.value});S.value=G.data,S.value.period&&(S.value.tailSet=P.getTailSet(S.value,K.value)),U.value=Q.value.map(j=>{const Y=new Set;for(let A of j.draw_number_size)Y.add(A%10);j.special_number&&!D.isSuperLotto&&Y.add(j.special_number%10);const pe=Array.from(Y).sort((A,fe)=>A===0?1:fe===0?-1:A-fe);return{period:String(j.period),numbers:[...pe]}}).reverse(),P.init({firstGroupSize:I.value,secondGroupSize:n.value,targetGroupSize:g.value,maxRange:R.value,lookAheadCount:O.value},U.value);let se=Date.now();const me=8,re=await P.analyzeWithProgress(async j=>{const Y=Date.now();Y-se>=me&&(await h.updateProgress(j),se=Y)},j=>{h.addWarning(j)});b.value=re.data,le.value=re.occurrences,oe.value=re.matchData}catch(X){console.error(X)}finally{e()}},a=()=>{Q.value=[]},e=()=>{P.stopAnalyzer(),h.stopCalculating()},s=_(!1),c=_(null),L=w=>{c.value=w,s.value=!0};return(w,m)=>(l(),q(Ne,{class:"justify-center"},{default:d(()=>[r(W,{class:"q-mx-auto q-py-sm"},{default:d(()=>[r(Z,null,{default:d(()=>{var N,X,G,se,me,re,j,Y,pe;return[(N=p(D).getLotto)!=null&&N.draw_date?(l(),u(v,{key:0},[t("div",ol,[t("div",rl,[t("div",null,o(p(D).getDrawLabel),1),m[7]||(m[7]=t("span",null,"\u53C3\u8003\u671F\u865F\uFF1A",-1)),t("span",null,o((X=x.value)==null?void 0:X.period),1),t("span",null,"\uFF08"+o((G=x.value)==null?void 0:G.draw_date)+"\uFF09",1)]),t("div",il,[t("div",nl,[(l(!0),u(v,null,f((se=x.value)==null?void 0:se.draw_number_size,A=>(l(),u("div",{class:"col-auto",key:A},[t("div",dl,o(p(k)(A)),1)]))),128)),(me=x.value)!=null&&me.special_number?(l(),u("div",cl,[(l(),u("div",{class:"ball special-number",key:(re=x.value)==null?void 0:re.special_number},o(p(k)((j=x.value)==null?void 0:j.special_number)),1))])):$("",!0)])])]),t("div",ml,[t("div",pl,[V.value?(l(),q(ie,{key:1,type:"button",label:"\u53D6\u6D88\u9078\u64C7",color:"negative",class:"text-h6 q-ml-md",onClick:de})):(l(),q(ie,{key:0,type:"button",label:"\u91CD\u65B0\u9078\u64C7",color:"primary",class:"text-h6 q-ml-md",onClick:B}))])])],64)):(l(),u("div",vl,m[8]||(m[8]=[t("div",{class:"text-h6"},"\u203B\u8ACB\u9078\u64C7\u53C3\u8003\u671F\u865F",-1)]))),r(Ae,{class:"q-mb-md"}),!V.value&&((Y=p(D).getLotto)==null?void 0:Y.draw_date)?(l(),u(v,{key:2},[m[14]||(m[14]=t("div",{class:"row q-mb-md"},[t("div",{class:"col-12 text-h5 text-weight-bolder text-center"}," \u5C3E\u6578\u5206\u6790\u8A2D\u5B9A ")],-1)),t("div",bl,[m[9]||(m[9]=t("div",{class:"col-12 text-h6 text-weight-bold"},"\u5C3E\u6578\u62D6\u724C\u7D44\u5408",-1)),t("div",gl,[r(J,{outlined:"",dense:"",modelValue:I.value,"onUpdate:modelValue":m[0]||(m[0]=A=>I.value=A),options:H,"emit-value":"","map-options":""},null,8,["modelValue"])]),t("div",fl,[r(J,{outlined:"",dense:"",modelValue:n.value,"onUpdate:modelValue":m[1]||(m[1]=A=>n.value=A),options:H,"emit-value":"","map-options":""},null,8,["modelValue"])]),t("div",_l,[r(J,{outlined:"",dense:"",modelValue:g.value,"onUpdate:modelValue":m[2]||(m[2]=A=>g.value=A),options:H,"emit-value":"","map-options":""},null,8,["modelValue"])])]),t("div",yl,[t("div",wl,[m[11]||(m[11]=t("div",{class:"text-h6 text-weight-bold"},"\u63A8\u7B97\u671F\u6578",-1)),t("div",kl,[r(J,{outlined:"",dense:"",modelValue:ee.value,"onUpdate:modelValue":m[3]||(m[3]=A=>ee.value=A),options:p(T),"input-debounce":"0","use-input":"","hide-selected":"","fill-input":"",onFilter:ae,"emit-value":"","map-options":""},{"no-option":d(()=>[r(xe,null,{default:d(()=>[r($e,{class:"text-grey"},{default:d(()=>m[10]||(m[10]=[y(" \u7121\u53EF\u7528\u9078\u9805 ")])),_:1})]),_:1})]),_:1},8,["modelValue","options"])])]),t("div",hl,[m[12]||(m[12]=t("div",{class:"text-h6 text-weight-bold"},"\u6700\u5927\u5340\u9593",-1)),t("div",El,[r(J,{outlined:"",dense:"",modelValue:R.value,"onUpdate:modelValue":m[4]||(m[4]=A=>R.value=A),options:p(E),"emit-value":"","map-options":""},null,8,["modelValue","options"])])]),t("div",Cl,[m[13]||(m[13]=t("div",{class:"text-h6 text-weight-bold"},"\u9810\u6E2C\u671F\u6578",-1)),t("div",Fl,[r(J,{outlined:"",dense:"",modelValue:O.value,"onUpdate:modelValue":m[5]||(m[5]=A=>O.value=A),options:p(M),"emit-value":"","map-options":""},null,8,["modelValue","options"])])])]),r(qe,{align:"right",class:"q-my-lg q-py-none q-px-md"},{default:d(()=>[p(h).isCalculating?(l(),q(ie,{key:0,type:"button",label:"\u4E2D\u65B7\u8A08\u7B97",color:"negative",class:"text-h6 q-mr-md",onClick:e})):$("",!0),r(ie,{type:"button",label:"\u958B\u59CB\u8A08\u7B97",color:"positive",class:"text-h6 q-px-lg q-py-sm",onClick:i,loading:p(h).isCalculating},{loading:d(()=>[r(Se)]),_:1},8,["loading"])]),_:1})],64)):(l(),q(ze,{key:3,"draw-type-query":p(D).drawType,"date-query":((pe=x.value)==null?void 0:pe.draw_date)||"",isSelectRef:!0,onSelectRef:C},null,8,["draw-type-query","date-query"]))]}),_:1}),p(h).isCalculating?(l(),q(Z,{key:0},{default:d(()=>[t("div",Dl,o(p(h).progressMessage),1),r(Re,{rounded:"",size:"md",value:p(h).progress,"animation-speed":50,color:"primary",class:"q-mb-xs"},null,8,["value"])]),_:1})):$("",!0)]),_:1}),!p(h).isCalculating&&Q.value.length>0?(l(),u(v,{key:0},[r(Rt,{"is-super-lotto":K.value,"draw-results":Q.value,"predict-result":S.value,"draw-tail-results":U.value,"rd-results":b.value,"occurrence-results":le.value,"page-size":50,onViewDetail:L},null,8,["is-super-lotto","draw-results","predict-result","draw-tail-results","rd-results","occurrence-results"]),r(al,{modelValue:s.value,"onUpdate:modelValue":m[6]||(m[6]=N=>s.value=N),results:Q.value,"predict-result":S.value,"selected-detail":c.value,occurrences:le.value},null,8,["modelValue","results","predict-result","selected-detail","occurrences"])],64)):$("",!0)]),_:1}))}});export{Gl as default};
