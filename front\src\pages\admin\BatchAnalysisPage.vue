<template>
  <q-page class="justify-center">
    <q-card>
      <q-card-section>
        <div class="text-h4 text-center q-mb-lg">分析報表</div>

        <!-- 分析方法選擇 -->
        <div class="row q-mb-lg">
          <div class="col-12 col-sm-6">
            <div class="text-h6">分析方法</div>
            <div class="q-pa-sm">
              <q-select
                outlined
                dense
                v-model="selectedMethod"
                :options="analysisMethodOptions"
                map-options
                emit-value
                class="text-h6"
              />
            </div>
          </div>
        </div>

        <!-- 彩種選擇 -->
        <div class="row q-mb-lg">
          <div class="col-12 col-sm-6">
            <div class="text-h6 text-weight-bold">彩種選擇</div>
            <div class="q-pa-sm">
              <q-select
                outlined
                dense
                v-model="selectedLottoType"
                :options="lottoTypeOptions"
                emit-value
                map-options
                @update:model-value="onLottoTypeChange"
              />
            </div>
          </div>
          <div class="col-12 col-sm-6">
            <div class="text-h6 text-weight-bold">參考期號</div>
            <div class="q-pa-sm">
              <q-input
                outlined
                dense
                v-model="referenceDate"
                type="date"
                :max="maxDate"
                class="text-h6"
              />
            </div>
          </div>
        </div>

        <!-- 批次設定 -->
        <div class="row q-mb-lg">
          <div class="col-12 col-sm-6">
            <div class="text-h6 text-weight-bold">推算期數</div>
            <div class="q-pa-sm">
              <q-select
                outlined
                dense
                v-model="batchPeriods"
                :options="periodOptions"
                emit-value
                map-options
              />
            </div>
          </div>
          <div class="col-12 col-sm-6">
            <div class="text-h6 text-weight-bold">輸出格式</div>
            <div class="q-pa-sm">
              <q-select
                outlined
                dense
                v-model="outputFormat"
                :options="outputFormatOptions"
                emit-value
                map-options
              />
            </div>
          </div>
        </div>

        <!-- 動態參數設定區域 -->
        <div class="row q-mb-lg" v-if="selectedMethod">
          <div class="col-12">
            <div class="text-h6 text-weight-bold q-mb-md">參數設定</div>

            <!-- 版路分析參數 -->
            <template v-if="selectedMethod === 'ball-follow'">
              <div class="row q-mb-md">
                <div class="col-12 text-h6">拖牌組合</div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="ballFollowParams.num1"
                    :options="combOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="ballFollowParams.num2"
                    :options="combOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="ballFollowParams.num3"
                    :options="combOptions"
                    emit-value
                    map-options
                  />
                </div>
              </div>
              <div class="row q-mb-md">
                <div class="col-12 col-sm-4">
                  <div class="text-h6">推算期數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="ballFollowParams.periodNum"
                      :options="periodNumOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">最大區間</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="ballFollowParams.maxRange"
                      :options="maxRangeOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">預測期數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="ballFollowParams.aheadNum"
                      :options="aheadNumOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
              </div>
            </template>

            <!-- 尾數分析參數 -->
            <template v-if="selectedMethod === 'tail'">
              <div class="row q-mb-md">
                <div class="col-12 text-h6">尾數拖牌組合</div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="tailParams.num1"
                    :options="tailCombOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="tailParams.num2"
                    :options="tailCombOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="tailParams.num3"
                    :options="tailCombOptions"
                    emit-value
                    map-options
                  />
                </div>
              </div>
              <div class="row q-mb-md">
                <div class="col-12 col-sm-4">
                  <div class="text-h6">推算期數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="tailParams.periodNum"
                      :options="periodNumOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">最大區間</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="tailParams.maxRange"
                      :options="maxRangeOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">預測期數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="tailParams.aheadNum"
                      :options="aheadNumOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
              </div>
            </template>

            <!-- 綜合分析參數 -->
            <template v-if="selectedMethod === 'pattern'">
              <div class="row q-mb-md">
                <div class="col-12 text-h6">拖牌組合</div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="patternParams.comb1"
                    :options="combOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="patternParams.comb2"
                    :options="combOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="patternParams.comb3"
                    :options="combOptions"
                    emit-value
                    map-options
                  />
                </div>
              </div>
              <div class="row q-mb-md">
                <div class="col-12 text-h6">尾數拖牌組合</div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="patternParams.tailComb1"
                    :options="tailCombOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="patternParams.tailComb2"
                    :options="tailCombOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="patternParams.tailComb3"
                    :options="tailCombOptions"
                    emit-value
                    map-options
                  />
                </div>
              </div>
              <div class="row q-mb-md">
                <div class="col-12 col-sm-4">
                  <div class="text-h6">推算期數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="patternParams.period"
                      :options="periodOptions"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">預測期數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="patternParams.ahead"
                      :options="aheadNumOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>

        <!-- 操作按鈕 -->
        <q-card-actions align="right" class="q-my-lg q-py-none q-px-md">
          <q-btn
            type="button"
            label="中斷計算"
            color="negative"
            @click="stopBatchAnalysis"
            class="text-h6 q-mr-md"
            v-if="isCalculating"
          />
          <q-btn
            type="button"
            label="產生報表"
            color="positive"
            class="text-h6 q-px-lg q-py-sm"
            @click="startBatchAnalysis"
            :loading="isCalculating"
            :disable="!canStartAnalysis"
          >
            <template v-slot:loading>
              <q-spinner-dots />
            </template>
          </q-btn>
        </q-card-actions>
      </q-card-section>

      <!-- 進度條 -->
      <q-card-section v-if="isCalculating">
        <div class="text-center q-mb-sm">
          {{ progressMessage }}
        </div>
        <q-linear-progress
          rounded
          size="md"
          :value="progress"
          :animation-speed="50"
          color="primary"
          class="q-mb-xs"
        />
      </q-card-section>
    </q-card>

    <!-- 結果顯示區域 -->
    <q-card v-if="batchResults.length > 0" class="q-mt-lg">
      <q-card-section>
        <div class="text-h6 text-weight-bold q-mb-md">分析結果</div>
        <div class="row q-mb-md">
          <div class="col-12 col-sm-6">
            <div class="text-subtitle1">
              分析方法：{{ getMethodName(selectedMethod) }}
            </div>
            <div class="text-subtitle1">
              彩種：{{ getLottoTypeName(selectedLottoType) }}
            </div>
          </div>
          <div class="col-12 col-sm-6 text-right">
            <q-btn
              color="primary"
              icon="download"
              :label="`下載 ${outputFormat.toUpperCase()} 檔案`"
              @click="downloadResults"
              :disable="batchResults.length === 0"
            />
          </div>
        </div>
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { Notify } from 'quasar';
// import * as XLSX from 'xlsx';
import { LOTTO_API } from '@/api';
import { LottoItem } from '@/api/modules/lotto';
import { useLotteryAnalysis } from '@/composables/useLotteryAnalysis';
import { handleError } from '@/utils';
import { AnalysisParameters, BallFollowParameters, TailParameters } from '@/models/batch-analysis';

const analysis = useLotteryAnalysis();

// 分析方法選項
const analysisMethodOptions = [
  { label: '版路分析', value: 'ball-follow' },
  { label: '尾數分析', value: 'tail' },
  { label: '綜合分析', value: 'pattern' }
];

// 彩種選項
const lottoTypeOptions = [
  { label: '威力彩', value: 'super_lotto638' },
  { label: '大樂透', value: 'lotto649' },
  { label: '今彩539', value: 'daily539' },
  { label: '六合彩', value: 'lotto_hk' }
];

// 輸出格式選項
const outputFormatOptions = [
  { label: 'Excel (.xlsx)', value: 'excel' },
  { label: 'CSV (.csv)', value: 'csv' }
];

// 響應式數據
const selectedMethod = ref('ball-follow');
const selectedLottoType = ref('super_lotto638');
const referenceDate = ref('');
const batchPeriods = ref(50);
const outputFormat = ref('excel');
const isCalculating = ref(false);
const progress = ref(0);
const progressMessage = ref('');
const batchResults = ref([]);

// 當前日期（用於限制參考期號）
const maxDate = computed(() => {
  return new Date().toISOString().split('T')[0];
});

// 檢查是否可以開始分析
const canStartAnalysis = computed(() => {
  return selectedMethod.value &&
         selectedLottoType.value &&
         referenceDate.value &&
         batchPeriods.value > 0 &&
         !isCalculating.value;
});

// 版路分析參數
const ballFollowParams = ref({
  num1: 1,
  num2: 1,
  num3: 1,
  periodNum: 50,
  maxRange: 20,
  aheadNum: 1
});

// 尾數分析參數
const tailParams = ref({
  num1: 1,
  num2: 1,
  num3: 1,
  periodNum: 50,
  maxRange: 20,
  aheadNum: 1
});

// 綜合分析參數
const patternParams = ref({
  comb1: 1,
  comb2: 1,
  comb3: 1,
  tailComb1: 1,
  tailComb2: 1,
  tailComb3: 1,
  period: 50,
  ahead: 1
});

// 獎號組合
const combOptions = [
  { label: '一星組合', value: 1 },
  { label: '二星組合', value: 2 },
  { label: '三星組合', value: 3 },
];
// 尾數組合
const tailCombOptions = [
  { label: '一星組合', value: 1 },
  { label: '二星組合', value: 2 },
  { label: '三星組合', value: 3 },
  { label: '四星組合', value: 4 },
  { label: '五星組合', value: 5 },
];

const periodNumOpts = ref(
  Array.from({ length: 491 }, (_, i) => ({
    label: `${i + 10}期`,
    value: i + 10,
  }))
);

const maxRangeOpts = ref(
  Array.from({ length: 21 }, (_, i) => ({
    label: `${i + 10}期`,
    value: i + 10,
  }))
);

const aheadNumOpts = ref(
  Array.from({ length: 15 }, (_, i) => ({
    label: `下${i + 1}期`,
    value: i + 1,
  }))
);

const periodOptions = ref(
  Array.from({ length: 991 }, (_, i) => ({
    label: `${i + 10}期`,
    value: i + 10,
  }))
);

// 結果表格欄位
// const resultColumns = {}

// 方法
const getMethodName = (method: string) => {
  const methodMap: Record<string, string> = {
    'ball-follow': '版路分析',
    'tail': '尾數分析',
    'pattern': '綜合分析'
  };
  return methodMap[method] || method;
};

const getLottoTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    'super_lotto638': '威力彩',
    'lotto649': '大樂透',
    'daily539': '今彩539',
    'lotto_hk': '六合彩'
  };
  return typeMap[type] || type;
};

const onLottoTypeChange = () => {
  // 當彩種改變時，可以在這裡更新相關設定
};

const getAnalysisParameters = (): AnalysisParameters => {
  switch (selectedMethod.value) {
    case 'ball-follow':
      return {
        comb1: ballFollowParams.value.num1,
        comb2: ballFollowParams.value.num2,
        comb3: ballFollowParams.value.num3,
        periodNum: ballFollowParams.value.periodNum,
        maxRange: ballFollowParams.value.maxRange,
        aheadNum: ballFollowParams.value.aheadNum
      };
    case 'tail':
      return {
        tailComb1: tailParams.value.num1,
        tailComb2: tailParams.value.num2,
        tailComb3: tailParams.value.num3,
        periodNum: tailParams.value.periodNum,
        maxRange: tailParams.value.maxRange,
        aheadNum: tailParams.value.aheadNum
      };
    case 'pattern':
      return {
        comb1: patternParams.value.comb1,
        comb2: patternParams.value.comb2,
        comb3: patternParams.value.comb3,
        tailComb1: patternParams.value.tailComb1,
        tailComb2: patternParams.value.tailComb2,
        tailComb3: patternParams.value.tailComb3,
        period: patternParams.value.period,
        ahead: patternParams.value.ahead
      };
    default:
      // Return default ball-follow parameters as fallback
      return {
        comb1: 1,
        comb2: 1,
        comb3: 1,
        tailComb1: 1,
        tailComb2: 1,
        tailComb3: 1,
        periodNum: 50,
        maxRange: 20,
        aheadNum: 1
      };
  }
};

const isSuperLotto = ref(false);
const startBatchAnalysis = async () => {
  if (!canStartAnalysis.value) {
    Notify.create({
      type: 'warning',
      message: '請完整填寫所有必要參數'
    });
    return;
  }

  try {
    isCalculating.value = true;
    isSuperLotto.value = selectedLottoType.value === 'super_lotto638';
    progress.value = 0;
    progressMessage.value = '準備開始...';

    // 準備API請求參數
    const response = await LOTTO_API.getLottoList({
      draw_type: selectedLottoType.value,
      date_end: referenceDate.value,
      limit: batchPeriods.value
    });
    const referenceResults = response.data;

    progressMessage.value = '正在進行分析...';

    // 進行分析
    let count = 0;
    for (const result of referenceResults) {
      // 進行分析的邏輯
      // 更新進度條
      progress.value += 1 / referenceResults.length;
      progressMessage.value = `分析中... ${++count}/${referenceResults.length}`;

      let response;
      switch (selectedMethod.value) {
        case 'ball-follow':
          // 執行版路分析
          response = await LOTTO_API.getLottoList({
            draw_type: selectedLottoType.value,
            date_end: result.draw_date,
            limit: batchPeriods.value
          });
          await doRdCalculating(response.data);
          break;
        case 'tail':
          // 執行尾數分析
          response = await LOTTO_API.getLottoList({
            draw_type: selectedLottoType.value,
            date_end: result.draw_date,
            limit: batchPeriods.value
          });
          await doTailRdCalculating(response.data);
          break;
        case 'pattern':
          // 執行綜合分析
          response = await LOTTO_API.getLottoList({
            draw_type: selectedLottoType.value,
            date_end: result.draw_date,
            limit: batchPeriods.value
          });
          await doRdCalculating(response.data);
          await doTailRdCalculating(response.data);
          break;
      }
    }

    progressMessage.value = '分析完成！';
    progress.value = 1;

    Notify.create({
      type: 'positive',
      position: 'top',
      message: '分析完成！'
    });

  } catch (error) {
    handleError(error);
  } finally {
    isCalculating.value = false;
  }
};

const stopBatchAnalysis = () => {
  isCalculating.value = false;
  progressMessage.value = '分析已中斷';

  Notify.create({
    type: 'warning',
    message: '分析已中斷'
  });
};

// 獎號拖牌
const doRdCalculating = (drawResults: LottoItem[]) => {
  const params = getAnalysisParameters() as BallFollowParameters;
  const serializedDrawResults = drawResults.map((item: LottoItem) => {
    const numbers = [...item.draw_number_size];
    if (!isSuperLotto.value && item.special_number) {
      // 除威力彩，其餘有彩種皆須加入特別號
      numbers.push(item.special_number);
    }
    // 返回一個純粹的對象
    return {
      numbers: [...numbers], // 確保創建新數組
      period: String(item.period), // 確保 period 是字符串
    };
  });

  // 設置配置
  analysis.init(
    {
      firstGroupSize: params.comb1,
      secondGroupSize: params.comb2,
      targetGroupSize: params.comb3,
      maxRange: params.maxRange,
      lookAheadCount: params.aheadNum,
    },
    serializedDrawResults
  );

  return analysis.analyzeWithProgress();
};

// 尾數拖牌
const doTailRdCalculating = (drawResults: LottoItem[]) => {
  const params = getAnalysisParameters() as TailParameters;

  // 創建數據結構
  const serializedTailResults = drawResults.map((item: LottoItem) => {
    const numbers = new Set<number>();

    for (let number of item.draw_number_size) {
      numbers.add(number % 10);
    }

    if (!isSuperLotto.value && item.special_number) {
      numbers.add(item.special_number % 10);
    }

    const sorted = Array.from(numbers).sort((a, b) => {
      if (a === 0) return 1;
      if (b === 0) return -1;
      return a - b;
    });

    return {
      period: String(item.period),
      numbers: [...sorted],
    };
  });

  analysis.init(
    {
      firstGroupSize: params.tailComb1,
      secondGroupSize: params.tailComb2,
      targetGroupSize: params.tailComb3,
      maxRange: params.maxRange,
      lookAheadCount: params.aheadNum,
    },
    serializedTailResults
  );

  return analysis.analyzeWithProgress();
};

const downloadResults = () => {
  // if (batchResults.value.length === 0) {
  //   Notify.create({
  //     type: 'warning',
  //     message: '沒有可下載的結果'
  //   });
  //   return;
  // }

  try {
    if (outputFormat.value === 'csv') {
      downloadCSV();
    } else {
      downloadExcel();
    }
  } catch (error) {
    console.error('下載失敗:', error);
    Notify.create({
      type: 'negative',
      message: '檔案下載失敗'
    });
  }
};

const downloadCSV = () => {
  // const headers = resultColumns.value.map(col => col.label).join(',');
  // const rows = batchResults.value.map(row =>
  //   resultColumns.value.map(col => {
  //     const value = typeof col.field === 'function' ? col.field(row) : row[col.field as keyof BatchAnalysisResult];
  //     return `"${String(value || '').replace(/"/g, '""')}"`;
  //   }).join(',')
  // );

  // const csvContent = [headers, ...rows].join('\n');
  // const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });

  // const link = document.createElement('a');
  // link.href = URL.createObjectURL(blob);
  // link.download = `批次分析結果_${getMethodName(selectedMethod.value)}_${getLottoTypeName(selectedLottoType.value)}_${new Date().toISOString().split('T')[0]}.csv`;
  // link.click();

  // Notify.create({
  //   type: 'positive',
  //   message: 'CSV 檔案下載完成'
  // });
};

const downloadExcel = () => {
  // try {
  //   // 準備工作表數據
  //   const worksheetData = [
  //     // 標題行
  //     resultColumns.value.map(col => col.label),
  //     // 數據行
  //     ...batchResults.value.map(row =>
  //       resultColumns.value.map(col => {
  //         const value = typeof col.field === 'function' ? col.field(row) : row[col.field as keyof BatchAnalysisResult];
  //         return String(value || '');
  //       })
  //     )
  //   ];

  //   // 創建工作表
  //   const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

  //   // 設置列寬
  //   const colWidths = resultColumns.value.map(col => {
  //     if (col.name === 'period' || col.name === 'date') return { wch: 12 };
  //     if (col.name === 'method') return { wch: 15 };
  //     if (col.name.includes('Numbers') || col.name.includes('prediction')) return { wch: 25 };
  //     return { wch: 15 };
  //   });
  //   worksheet['!cols'] = colWidths;

  //   // 創建工作簿
  //   const workbook = XLSX.utils.book_new();
  //   XLSX.utils.book_append_sheet(workbook, worksheet, '批次分析結果');

  //   // 添加分析參數工作表
  //   const paramData = [
  //     ['分析參數', ''],
  //     ['分析方法', getMethodName(selectedMethod.value)],
  //     ['彩種', getLottoTypeName(selectedLottoType.value)],
  //     ['參考期號', referenceDate.value],
  //     ['推算期數', batchPeriods.value.toString()],
  //     ['', ''],
  //     ['詳細參數', '']
  //   ];

  //   if (selectedMethod.value === 'ball-follow') {
  //     paramData.push(
  //       ['第一組號碼數', ballFollowParams.value.num1.toString()],
  //       ['第二組號碼數', ballFollowParams.value.num2.toString()],
  //       ['目標組號碼數', ballFollowParams.value.num3.toString()],
  //       ['推算期數', ballFollowParams.value.periodNum.toString()],
  //       ['最大區間', ballFollowParams.value.maxRange.toString()],
  //       ['預測期數', ballFollowParams.value.aheadNum.toString()]
  //     );
  //   } else if (selectedMethod.value === 'tail') {
  //     paramData.push(
  //       ['第一組尾數數', tailParams.value.num1.toString()],
  //       ['第二組尾數數', tailParams.value.num2.toString()],
  //       ['目標組尾數數', tailParams.value.num3.toString()],
  //       ['推算期數', tailParams.value.periodNum.toString()],
  //       ['最大區間', tailParams.value.maxRange.toString()],
  //       ['預測期數', tailParams.value.aheadNum.toString()]
  //     );
  //   } else if (selectedMethod.value === 'pattern') {
  //     paramData.push(
  //       ['拖牌組合1', patternParams.value.comb1.toString()],
  //       ['拖牌組合2', patternParams.value.comb2.toString()],
  //       ['拖牌組合3', patternParams.value.comb3.toString()],
  //       ['尾數組合1', patternParams.value.tailComb1.toString()],
  //       ['尾數組合2', patternParams.value.tailComb2.toString()],
  //       ['尾數組合3', patternParams.value.tailComb3.toString()],
  //       ['推算期數', patternParams.value.period.toString()],
  //       ['預測期數', patternParams.value.ahead.toString()]
  //     );
  //   }

  //   const paramWorksheet = XLSX.utils.aoa_to_sheet(paramData);
  //   paramWorksheet['!cols'] = [{ wch: 20 }, { wch: 15 }];
  //   XLSX.utils.book_append_sheet(workbook, paramWorksheet, '分析參數');

  //   // 生成檔案名稱
  //   const fileName = `批次分析結果_${getMethodName(selectedMethod.value)}_${getLottoTypeName(selectedLottoType.value)}_${new Date().toISOString().split('T')[0]}.xlsx`;

  //   // 下載檔案
  //   XLSX.writeFile(workbook, fileName);

  //   Notify.create({
  //     type: 'positive',
  //     message: 'Excel 檔案下載完成'
  //   });
  // } catch (error) {
  //   console.error('Excel 下載失敗:', error);
  //   Notify.create({
  //     type: 'negative',
  //     message: 'Excel 檔案下載失敗'
  //   });
  // }
};

// 初始化
onMounted(() => {
  // 設定預設參考日期為今天
  referenceDate.value = new Date().toISOString().split('T')[0];
});
</script>

<style lang="scss" scoped>
.q-card {
  max-width: 1200px;
  margin: 0 auto;
}

.q-option-group {
  .q-radio {
    margin-right: 2rem;
  }
}
</style>
