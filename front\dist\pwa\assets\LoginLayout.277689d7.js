import{u as q,Q as N,a as R,b as S,c as T,d as I}from"./title.c07ae61a.js";import{C as O,i as P,D as g,r as y,E as $,c as r,w as c,a as k,F as E,h as b,t as M,G as j,k as G,H as J,I as K,J as U,L as d,M as f,N as Y,O as L,A as W,P as v}from"./index.5cb20bf8.js";import{Q as X}from"./QResizeObserver.55c7aa89.js";var Z=O({name:"QFooter",props:{modelValue:{type:Boolean,default:!0},reveal:Boolean,bordered:Boolean,elevated:Boolean,heightHint:{type:[String,Number],default:50}},emits:["reveal","focusin"],setup(a,{slots:m,emit:_}){const{proxy:{$q:l}}=M(),t=P(j,g);if(t===g)return console.error("QFooter needs to be child of QLayout"),g;const s=y(parseInt(a.heightHint,10)),n=y(!0),p=y($.value===!0||t.isContainer.value===!0?0:window.innerHeight),h=r(()=>a.reveal===!0||t.view.value.indexOf("F")!==-1||l.platform.is.ios&&t.isContainer.value===!0),w=r(()=>t.isContainer.value===!0?t.containerHeight.value:p.value),x=r(()=>{if(a.modelValue!==!0)return 0;if(h.value===!0)return n.value===!0?s.value:0;const e=t.scroll.value.position+w.value+s.value-t.height.value;return e>0?e:0}),C=r(()=>a.modelValue!==!0||h.value===!0&&n.value!==!0),D=r(()=>a.modelValue===!0&&C.value===!0&&a.reveal===!0),F=r(()=>"q-footer q-layout__section--marginal "+(h.value===!0?"fixed":"absolute")+"-bottom"+(a.bordered===!0?" q-footer--bordered":"")+(C.value===!0?" q-footer--hidden":"")+(a.modelValue!==!0?" q-layout--prevent-focus"+(h.value!==!0?" hidden":""):"")),Q=r(()=>{const e=t.rows.value.bottom,o={};return e[0]==="l"&&t.left.space===!0&&(o[l.lang.rtl===!0?"right":"left"]=`${t.left.size}px`),e[2]==="r"&&t.right.space===!0&&(o[l.lang.rtl===!0?"left":"right"]=`${t.right.size}px`),o});function u(e,o){t.update("footer",e,o)}function i(e,o){e.value!==o&&(e.value=o)}function V({height:e}){i(s,e),u("size",e)}function A(){if(a.reveal!==!0)return;const{direction:e,position:o,inflectionPoint:z}=t.scroll.value;i(n,e==="up"||o-z<100||t.height.value-w.value-o-s.value<300)}function H(e){D.value===!0&&i(n,!0),_("focusin",e)}c(()=>a.modelValue,e=>{u("space",e),i(n,!0),t.animate()}),c(x,e=>{u("offset",e)}),c(()=>a.reveal,e=>{e===!1&&i(n,a.modelValue)}),c(n,e=>{t.animate(),_("reveal",e)}),c([s,t.scroll,t.height],A),c(()=>l.screen.height,e=>{t.isContainer.value!==!0&&i(p,e)});const B={};return t.instances.footer=B,a.modelValue===!0&&u("size",s.value),u("space",a.modelValue),u("offset",x.value),k(()=>{t.instances.footer===B&&(t.instances.footer=void 0,u("size",0),u("offset",0),u("space",!1))}),()=>{const e=E(m.default,[b(X,{debounce:0,onResize:V})]);return a.elevated===!0&&e.push(b("div",{class:"q-layout__shadow absolute-full overflow-hidden no-pointer-events"})),b("footer",{class:F.value,style:Q.value,onFocusin:H},e)}}});const ee={class:"footer-content"},te={class:"copyright text-subtitle2"},le=G({name:"LoginLayout",__name:"LoginLayout",setup(a){const m=q();return(_,l)=>{const t=J("router-view");return K(),U(N,{view:"hHh Lpr lFr"},{default:d(()=>[f(T,{reveal:"",elevated:""},{default:d(()=>[f(R,null,{default:d(()=>[f(S,{class:"text-center"},{default:d(()=>[Y(L(W(m).val),1)]),_:1})]),_:1})]),_:1}),f(I,null,{default:d(()=>[f(t)]),_:1}),f(Z,{bordered:"",class:"footer user-select-all"},{default:d(()=>[v("div",ee,[l[0]||(l[0]=v("div",{class:"contact-info"},[v("span",{class:"text-subtitle1"},"\u5BA2\u670D\u5C08\u7DDA: 0979-139-262"),v("span",{class:"text-subtitle1 separator"},"\u5BA2\u670DLine ID: lotto888")],-1)),v("div",te," \u7D18\u9A30\u8CC7\u8A0A\u5DE5\u4F5C\u5BA4 \xA9 "+L(new Date().getFullYear())+" \u7248\u6B0A\u6240\u6709\u3002\u4FDD\u7559\u6240\u6709\u6B0A\u5229\u3002 ",1)])]),_:1})]),_:1})}}});export{le as default};
