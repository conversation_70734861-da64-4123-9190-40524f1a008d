import{C as b,r as p,c as h,w as P,b8 as x,h as y,t as C,b7 as Q}from"./index.5cb20bf8.js";import{u as j,a as k,b as M}from"./QSelect.a87fec46.js";var H=b({name:"QPopupProxy",props:{...j,breakpoint:{type:[String,Number],default:450}},emits:["show","hide"],setup(a,{slots:g,emit:c,attrs:v}){const{proxy:u}=C(),{$q:l}=u,n=p(!1),t=p(null),i=h(()=>parseInt(a.breakpoint,10)),{canShow:f}=k({showing:n});function r(){return l.screen.width<i.value||l.screen.height<i.value?"dialog":"menu"}const o=p(r()),m=h(()=>o.value==="menu"?{maxHeight:"99vh"}:{});P(()=>r(),e=>{n.value!==!0&&(o.value=e)});function d(e){n.value=!0,c("show",e)}function w(e){n.value=!1,o.value=r(),c("hide",e)}return Object.assign(u,{show(e){f(e)===!0&&t.value.show(e)},hide(e){t.value.hide(e)},toggle(e){t.value.toggle(e)}}),x(u,"currentComponent",()=>({type:o.value,ref:t.value})),()=>{const e={ref:t,...m.value,...v,onShow:d,onHide:w};let s;return o.value==="dialog"?s=Q:(s=M,Object.assign(e,{target:a.target,contextMenu:a.contextMenu,noParentEvent:!0,separateClosePopup:!0})),y(s,e,g.default)}}});export{H as Q};
