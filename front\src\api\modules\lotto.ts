import { api } from '@/boot/axios';

export interface LottoItem {
  period: string;
  draw_date: string;
  draw_number_appear: number[];
  draw_number_size: number[];
  special_number?: number;
  tails: Map<number, number[]>;
  tailSet?: number[];
}

interface LottoFilter {
  draw_type: string;
  draw_date?: string;
  date_start?: string;
  date_end?: string;
  limit?: number;
  ascending?: boolean;
  ahead_count?: number;
}

export interface DragLottoResult {
  num1: number;
  num2: number;
  num3: number;
  data: [];
}

const LOTTO_API = {
  getLottoList: (filter: LottoFilter) =>
    api.get<LottoItem[]>('/lotto', { params: filter }),
  getLottoPredict: (filter: LottoFilter) =>
    api.get<LottoItem>('/lotto/predict', { params: filter }),
};

export default LOTTO_API;
