import{ae as o,af as t}from"./index.8af2c4ae.js";const{Axios:f,AxiosError:i,CanceledError:l,isCancel:p,CancelToken:c,VERSION:g,all:m,Cancel:r,isAxiosError:d,spread:A,toFormData:C,AxiosHeaders:u,HttpStatusCode:x,formToJSON:E,getAdapter:y,mergeConfig:F}=o;function N(e){var a;let s="\u767C\u751F\u932F\u8AA4";e instanceof i?s=((a=e.response)==null?void 0:a.data.message)||e.message:e instanceof Error?s=e.message:typeof e=="string"&&(s=e),t.create({type:"negative",message:s,position:"top"})}export{N as h};
