// 自動更新檢查服務
export class UpdateChecker {
  private static instance: UpdateChecker;
  private checkInterval: number | null = null;
  private registration: ServiceWorkerRegistration | null = null;

  private constructor() {
    this.init();
  }

  public static getInstance(): UpdateChecker {
    if (!UpdateChecker.instance) {
      UpdateChecker.instance = new UpdateChecker();
    }
    return UpdateChecker.instance;
  }

  private async init() {
    if ('serviceWorker' in navigator) {
      try {
        // 獲取當前的 Service Worker 註冊
        this.registration = await navigator.serviceWorker.getRegistration() || null;

        if (this.registration) {
          console.log('UpdateChecker: Service Worker 已註冊');
          this.startPeriodicCheck();
        } else {
          console.log('UpdateChecker: 等待 Service Worker 註冊');
          // 等待 Service Worker 註冊完成
          navigator.serviceWorker.ready.then((registration) => {
            this.registration = registration;
            this.startPeriodicCheck();
          });
        }
      } catch (error) {
        console.error('UpdateChecker: 初始化失敗', error);
      }
    }
  }

  private startPeriodicCheck() {
    // 每30秒檢查一次更新
    this.checkInterval = window.setInterval(() => {
      this.checkForUpdates();
    }, 30000);

    // 立即執行一次檢查
    setTimeout(() => this.checkForUpdates(), 5000);
  }

  private async checkForUpdates() {
    if (!this.registration) {
      console.log('UpdateChecker: Service Worker 未註冊，跳過檢查');
      return;
    }

    try {
      console.log('UpdateChecker: 檢查更新中...');

      // 手動觸發更新檢查
      await this.registration.update();

      // 檢查是否有等待中的 Service Worker
      if (this.registration.waiting) {
        console.log('UpdateChecker: 發現新版本，準備更新');
        this.handleUpdate();
      } else if (this.registration.installing) {
        console.log('UpdateChecker: 新版本正在安裝中');
        // 監聽安裝完成
        this.registration.installing.addEventListener('statechange', () => {
          if (this.registration?.installing?.state === 'installed') {
            console.log('UpdateChecker: 新版本安裝完成');
            this.handleUpdate();
          }
        });
      } else {
        console.log('UpdateChecker: 沒有發現新版本');
      }
    } catch (error) {
      console.error('UpdateChecker: 檢查更新失敗', error);

      // 如果是 Service Worker 腳本評估失敗，嘗試重新註冊
      if (error instanceof Error && error.message.includes('ServiceWorker script evaluation failed')) {
        console.log('UpdateChecker: Service Worker 腳本評估失敗，嘗試重新註冊');
        try {
          await navigator.serviceWorker.register('/sw.js');
          console.log('UpdateChecker: Service Worker 重新註冊成功');
        } catch (reregisterError) {
          console.error('UpdateChecker: Service Worker 重新註冊失敗', reregisterError);
        }
      }
    }
  }

  private handleUpdate() {
    if (!this.registration?.waiting) {
      return;
    }

    console.log('UpdateChecker: 發現 Service Worker 更新');

    // 顯示更新確認對話框
    this.showUpdateDialog();
  }

  private showUpdateDialog() {
    // 檢查是否已經有對話框存在
    if (document.getElementById('sw-update-dialog')) {
      return;
    }

    // 創建更新提示對話框
    const dialog = document.createElement('div');
    dialog.id = 'sw-update-dialog';
    dialog.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    const dialogContent = document.createElement('div');
    dialogContent.style.cssText = `
      background: white;
      padding: 24px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      max-width: 400px;
      text-align: center;
    `;

    dialogContent.innerHTML = `
      <div style="margin-bottom: 16px;">
        <div style="font-size: 18px; font-weight: 600; color: #1976d2; margin-bottom: 8px;">
          🔄 應用程式更新
        </div>
        <div style="color: #666; font-size: 14px;">
          檢測到新的應用程式版本，是否要立即更新？
        </div>
      </div>
      <div style="display: flex; gap: 12px; justify-content: center;">
        <button id="sw-update-later" style="
          padding: 8px 16px;
          border: 1px solid #ddd;
          background: white;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
        ">稍後更新</button>
        <button id="sw-update-now" style="
          padding: 8px 16px;
          border: none;
          background: #1976d2;
          color: white;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
        ">立即更新</button>
      </div>
    `;

    dialog.appendChild(dialogContent);
    document.body.appendChild(dialog);

    // 綁定按鈕事件
    const updateNowBtn = dialog.querySelector('#sw-update-now');
    const updateLaterBtn = dialog.querySelector('#sw-update-later');

    updateNowBtn?.addEventListener('click', () => {
      document.body.removeChild(dialog);
      this.performUpdate();
    });

    updateLaterBtn?.addEventListener('click', () => {
      document.body.removeChild(dialog);
      console.log('UpdateChecker: 用戶選擇稍後更新');
      // 設置一個標記，30分鐘後再次提醒
      setTimeout(() => {
        this.showUpdateDialog();
      }, 30 * 60 * 1000); // 30分鐘
    });
  }

  private performUpdate() {
    if (!this.registration?.waiting) {
      return;
    }

    console.log('UpdateChecker: 開始執行 Service Worker 更新');

    // 發送跳過等待消息
    this.registration.waiting.postMessage({ type: 'SKIP_WAITING' });

    // 監聽控制器變更
    navigator.serviceWorker.addEventListener('controllerchange', () => {
      console.log('UpdateChecker: 控制器已變更，重新載入頁面');
      window.location.reload();
    }, { once: true });
  }

  public destroy() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }

  // 手動檢查更新
  public async manualCheck(): Promise<boolean> {
    if (!this.registration) {
      console.log('UpdateChecker: Service Worker 未註冊');
      throw new Error('Service Worker 未註冊');
    }

    try {
      await this.registration.update();

      if (this.registration.waiting || this.registration.installing) {
        // 手動檢查時直接顯示對話框
        this.showUpdateDialog();
        return true;
      }

      console.log('UpdateChecker: 已是最新版本');
      return false;
    } catch (error) {
      console.error('UpdateChecker: 手動檢查更新失敗', error);
      throw error;
    }
  }
}

// 創建全局實例
export const updateChecker = UpdateChecker.getInstance();
