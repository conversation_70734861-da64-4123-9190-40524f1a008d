<template>
  <q-layout view="hHh Lpr lFf">
    <q-header elevated>
      <q-toolbar>
        <q-btn
          flat
          dense
          round
          icon="menu"
          aria-label="Menu"
          @click="toggleLeftDrawer"
        />

        <q-toolbar-title>{{ title.val }}</q-toolbar-title>

        <q-space />

        <div class="q-mr-md">
          {{ authStore.userInfo?.name }}
        </div>
        <q-btn
          flat
          dense
          round
          icon="logout"
          aria-label="Logout"
          @click="logout"
        />
      </q-toolbar>
    </q-header>

    <q-drawer v-model="leftDrawerOpen" bordered class="drawer-container">
      <div class="drawer-content">
        <q-list separator>
          <MenuLink
            v-for="link in linksList"
            :key="link.title"
            v-bind="link"
            @toggle-menu="toggleLeftDrawer"
          />
        </q-list>
      </div>

      <!-- 版本號 -->
      <div class="version-footer">
        <q-separator />
        <div class="text-caption text-center text-grey-6 q-pa-md">
          版本:{{ appVersion }}
        </div>
      </div>
    </q-drawer>

    <q-page-container>
      <router-view />
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import MenuLink, { MenuLinkProps } from '@/components/Menu.vue';
import { useTitleStore } from '@/stores/title';
import { useAuthStore } from '@/stores/auth';
import AUTH_API from '@/api/modules/auth';
import packageJson from '@/../package.json';

defineOptions({
  name: 'MainLayout',
});

const appVersion = ref(packageJson.version);

const router = useRouter();
const title = useTitleStore();
const authStore = useAuthStore();

const linksList: MenuLinkProps[] = [
  {
    title: '會員管理',
    link: '/admin/dashboard/user',
    requireAdmin: true,
  },
  {
    title: '個人資料',
    link: '/profile',
  },
  {
    title: '開獎結果',
    link: '/index',
  },
  {
    title: '版路分析',
    link: '/rd1',
  },
  {
    title: '尾數分析',
    link: '/tail',
  },
  {
    title: '綜合分析',
    link: '/pattern',
  },
  {
    title: '分析報表',
    link: '/admin/dashboard/batch-analysis',
    requireAdmin: true,
  },
  {
    title: '安裝程式',
    link: '/install',
  },
  {
    title: '免責聲明',
    link: '/disclaimer',
  },
];

const leftDrawerOpen = ref(false);

function toggleLeftDrawer() {
  leftDrawerOpen.value = !leftDrawerOpen.value;
}

const logout = async () => {
  await AUTH_API.logout();
  authStore.logout();
  router.push('/login');
};
</script>

<style lang="scss">
* {
  -webkit-user-select: none;
  user-select: none;
}

.q-page-container {
  width: 80%;
  margin: 1rem auto;
  padding-right: 16px;
  padding-left: 16px;
}

.drawer-container {
  display: flex;
  flex-direction: column;
}

.drawer-content {
  flex: 1;
  overflow-y: auto;

  .q-item__label {
    font-size: 1.3rem;
  }
}

.version-footer {
  margin-top: auto;
}

@media (max-width: 991px) {
  .q-page-container {
    width: 100%;
  }

  .drawer-content {
    .q-item__label {
      font-size: 1.6rem;
    }
  }
}
</style>
