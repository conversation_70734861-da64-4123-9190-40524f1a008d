import{ah as Ye,ai as O,V as Ue,aj as Re,ak as W,al as ee,am as ye,an as te,ao as ue,ap as ae,ab as Ke,C as Je,aq as Ge,ar as Ze,as as et,at as tt,au as at,i as nt,D as ne,r as S,c as p,av as ot,aw as it,w as g,b as rt,n as be,a as ut,p as lt,h as z,ax as ge,Q as st,G as dt,t as ct,ay as vt,k as Me,a1 as De,A as le,I as A,az as Se,J as P,L as w,aA as Ae,aB as oe,M as h,a5 as we,N as se,O as $,a0 as ft,H as mt,a8 as ke,P as N,aC as pt,aD as ht,a9 as yt,aE as bt}from"./index.5cb20bf8.js";import{u as gt,Q as wt,a as kt,b as Ct,c as _t,d as qt}from"./title.c07ae61a.js";import{Q as xt}from"./QSpace.7a70be6f.js";import{Q as Bt}from"./QList.116dfdf0.js";import{g as Ce,s as _e}from"./touch.9135741d.js";import{c as Mt,b as H,Q as qe,a as j,d as xe}from"./QItem.ecbdb62a.js";import"./QResizeObserver.55c7aa89.js";function ie(n,i,u){const y=ue(n);let e,t=y.left-i.event.x,r=y.top-i.event.y,d=Math.abs(t),f=Math.abs(r);const s=i.direction;s.horizontal===!0&&s.vertical!==!0?e=t<0?"left":"right":s.horizontal!==!0&&s.vertical===!0?e=r<0?"up":"down":s.up===!0&&r<0?(e="up",d>f&&(s.left===!0&&t<0?e="left":s.right===!0&&t>0&&(e="right"))):s.down===!0&&r>0?(e="down",d>f&&(s.left===!0&&t<0?e="left":s.right===!0&&t>0&&(e="right"))):s.left===!0&&t<0?(e="left",d<f&&(s.up===!0&&r<0?e="up":s.down===!0&&r>0&&(e="down"))):s.right===!0&&t>0&&(e="right",d<f&&(s.up===!0&&r<0?e="up":s.down===!0&&r>0&&(e="down")));let o=!1;if(e===void 0&&u===!1){if(i.event.isFirst===!0||i.event.lastDir===void 0)return{};e=i.event.lastDir,o=!0,e==="left"||e==="right"?(y.left-=t,d=0,t=0):(y.top-=r,f=0,r=0)}return{synthetic:o,payload:{evt:n,touch:i.event.mouse!==!0,mouse:i.event.mouse===!0,position:y,direction:e,isFirst:i.event.isFirst,isFinal:u===!0,duration:Date.now()-i.event.time,distance:{x:d,y:f},offset:{x:t,y:r},delta:{x:y.left-i.event.lastX,y:y.top-i.event.lastY}}}}let Dt=0;var re=Ye({name:"touch-pan",beforeMount(n,{value:i,modifiers:u}){if(u.mouse!==!0&&O.has.touch!==!0)return;function y(t,r){u.mouse===!0&&r===!0?Ke(t):(u.stop===!0&&te(t),u.prevent===!0&&ye(t))}const e={uid:"qvtp_"+Dt++,handler:i,modifiers:u,direction:Ce(u),noop:Ue,mouseStart(t){_e(t,e)&&Re(t)&&(W(e,"temp",[[document,"mousemove","move","notPassiveCapture"],[document,"mouseup","end","passiveCapture"]]),e.start(t,!0))},touchStart(t){if(_e(t,e)){const r=t.target;W(e,"temp",[[r,"touchmove","move","notPassiveCapture"],[r,"touchcancel","end","passiveCapture"],[r,"touchend","end","passiveCapture"]]),e.start(t)}},start(t,r){if(O.is.firefox===!0&&ee(n,!0),e.lastEvt=t,r===!0||u.stop===!0){if(e.direction.all!==!0&&(r!==!0||e.modifiers.mouseAllDir!==!0&&e.modifiers.mousealldir!==!0)){const s=t.type.indexOf("mouse")!==-1?new MouseEvent(t.type,t):new TouchEvent(t.type,t);t.defaultPrevented===!0&&ye(s),t.cancelBubble===!0&&te(s),Object.assign(s,{qKeyEvent:t.qKeyEvent,qClickOutside:t.qClickOutside,qAnchorHandled:t.qAnchorHandled,qClonedBy:t.qClonedBy===void 0?[e.uid]:t.qClonedBy.concat(e.uid)}),e.initialEvent={target:t.target,event:s}}te(t)}const{left:d,top:f}=ue(t);e.event={x:d,y:f,time:Date.now(),mouse:r===!0,detected:!1,isFirst:!0,isFinal:!1,lastX:d,lastY:f}},move(t){if(e.event===void 0)return;const r=ue(t),d=r.left-e.event.x,f=r.top-e.event.y;if(d===0&&f===0)return;e.lastEvt=t;const s=e.event.mouse===!0,o=()=>{y(t,s);let b;u.preserveCursor!==!0&&u.preservecursor!==!0&&(b=document.documentElement.style.cursor||"",document.documentElement.style.cursor="grabbing"),s===!0&&document.body.classList.add("no-pointer-events--children"),document.body.classList.add("non-selectable"),Mt(),e.styleCleanup=v=>{if(e.styleCleanup=void 0,b!==void 0&&(document.documentElement.style.cursor=b),document.body.classList.remove("non-selectable"),s===!0){const B=()=>{document.body.classList.remove("no-pointer-events--children")};v!==void 0?setTimeout(()=>{B(),v()},50):B()}else v!==void 0&&v()}};if(e.event.detected===!0){e.event.isFirst!==!0&&y(t,e.event.mouse);const{payload:b,synthetic:v}=ie(t,e,!1);b!==void 0&&(e.handler(b)===!1?e.end(t):(e.styleCleanup===void 0&&e.event.isFirst===!0&&o(),e.event.lastX=b.position.left,e.event.lastY=b.position.top,e.event.lastDir=v===!0?void 0:b.direction,e.event.isFirst=!1));return}if(e.direction.all===!0||s===!0&&(e.modifiers.mouseAllDir===!0||e.modifiers.mousealldir===!0)){o(),e.event.detected=!0,e.move(t);return}const C=Math.abs(d),c=Math.abs(f);C!==c&&(e.direction.horizontal===!0&&C>c||e.direction.vertical===!0&&C<c||e.direction.up===!0&&C<c&&f<0||e.direction.down===!0&&C<c&&f>0||e.direction.left===!0&&C>c&&d<0||e.direction.right===!0&&C>c&&d>0?(e.event.detected=!0,e.move(t)):e.end(t,!0))},end(t,r){if(e.event!==void 0){if(ae(e,"temp"),O.is.firefox===!0&&ee(n,!1),r===!0)e.styleCleanup!==void 0&&e.styleCleanup(),e.event.detected!==!0&&e.initialEvent!==void 0&&e.initialEvent.target.dispatchEvent(e.initialEvent.event);else if(e.event.detected===!0){e.event.isFirst===!0&&e.handler(ie(t===void 0?e.lastEvt:t,e).payload);const{payload:d}=ie(t===void 0?e.lastEvt:t,e,!0),f=()=>{e.handler(d)};e.styleCleanup!==void 0?e.styleCleanup(f):f()}e.event=void 0,e.initialEvent=void 0,e.lastEvt=void 0}}};if(n.__qtouchpan=e,u.mouse===!0){const t=u.mouseCapture===!0||u.mousecapture===!0?"Capture":"";W(e,"main",[[n,"mousedown","mouseStart",`passive${t}`]])}O.has.touch===!0&&W(e,"main",[[n,"touchstart","touchStart",`passive${u.capture===!0?"Capture":""}`],[n,"touchmove","noop","notPassiveCapture"]])},updated(n,i){const u=n.__qtouchpan;u!==void 0&&(i.oldValue!==i.value&&(typeof value!="function"&&u.end(),u.handler=i.value),u.direction=Ce(i.modifiers))},beforeUnmount(n){const i=n.__qtouchpan;i!==void 0&&(i.event!==void 0&&i.end(),ae(i,"main"),ae(i,"temp"),O.is.firefox===!0&&ee(n,!1),i.styleCleanup!==void 0&&i.styleCleanup(),delete n.__qtouchpan)}});const Be=150;var St=Je({name:"QDrawer",inheritAttrs:!1,props:{...Ge,...Ze,side:{type:String,default:"left",validator:n=>["left","right"].includes(n)},width:{type:Number,default:300},mini:Boolean,miniToOverlay:Boolean,miniWidth:{type:Number,default:57},noMiniAnimation:Boolean,breakpoint:{type:Number,default:1023},showIfAbove:Boolean,behavior:{type:String,validator:n=>["default","desktop","mobile"].includes(n),default:"default"},bordered:Boolean,elevated:Boolean,overlay:Boolean,persistent:Boolean,noSwipeOpen:Boolean,noSwipeClose:Boolean,noSwipeBackdrop:Boolean},emits:[...et,"onLayout","miniState"],setup(n,{slots:i,emit:u,attrs:y}){const e=ct(),{proxy:{$q:t}}=e,r=tt(n,t),{preventBodyScroll:d}=vt(),{registerTimeout:f,removeTimeout:s}=at(),o=nt(dt,ne);if(o===ne)return console.error("QDrawer needs to be child of QLayout"),ne;let C,c=null,b;const v=S(n.behavior==="mobile"||n.behavior!=="desktop"&&o.totalWidth.value<=n.breakpoint),B=p(()=>n.mini===!0&&v.value!==!0),_=p(()=>B.value===!0?n.miniWidth:n.width),m=S(n.showIfAbove===!0&&v.value===!1?!0:n.modelValue===!0),de=p(()=>n.persistent!==!0&&(v.value===!0||Te.value===!0));function ce(a,l){if(Ee(),a!==!1&&o.animate(),q(0),v.value===!0){const k=o.instances[V.value];k!==void 0&&k.belowBreakpoint===!0&&k.hide(!1),M(1),o.isContainer.value!==!0&&d(!0)}else M(0),a!==!1&&J(!1);f(()=>{a!==!1&&J(!0),l!==!0&&u("show",a)},Be)}function ve(a,l){Le(),a!==!1&&o.animate(),M(0),q(E.value*_.value),G(),l!==!0?f(()=>{u("hide",a)},Be):s()}const{show:X,hide:T}=ot({showing:m,hideOnRouteChange:de,handleShow:ce,handleHide:ve}),{addToHistory:Ee,removeFromHistory:Le}=it(m,T,de),F={belowBreakpoint:v,hide:T},x=p(()=>n.side==="right"),E=p(()=>(t.lang.rtl===!0?-1:1)*(x.value===!0?1:-1)),fe=S(0),L=S(!1),Y=S(!1),me=S(_.value*E.value),V=p(()=>x.value===!0?"left":"right"),U=p(()=>m.value===!0&&v.value===!1&&n.overlay===!1?n.miniToOverlay===!0?n.miniWidth:_.value:0),R=p(()=>n.overlay===!0||n.miniToOverlay===!0||o.view.value.indexOf(x.value?"R":"L")!==-1||t.platform.is.ios===!0&&o.isContainer.value===!0),Q=p(()=>n.overlay===!1&&m.value===!0&&v.value===!1),Te=p(()=>n.overlay===!0&&m.value===!0&&v.value===!1),Qe=p(()=>"fullscreen q-drawer__backdrop"+(m.value===!1&&L.value===!1?" hidden":"")),Oe=p(()=>({backgroundColor:`rgba(0,0,0,${fe.value*.4})`})),pe=p(()=>x.value===!0?o.rows.value.top[2]==="r":o.rows.value.top[0]==="l"),Pe=p(()=>x.value===!0?o.rows.value.bottom[2]==="r":o.rows.value.bottom[0]==="l"),$e=p(()=>{const a={};return o.header.space===!0&&pe.value===!1&&(R.value===!0?a.top=`${o.header.offset}px`:o.header.space===!0&&(a.top=`${o.header.size}px`)),o.footer.space===!0&&Pe.value===!1&&(R.value===!0?a.bottom=`${o.footer.offset}px`:o.footer.space===!0&&(a.bottom=`${o.footer.size}px`)),a}),Fe=p(()=>{const a={width:`${_.value}px`,transform:`translateX(${me.value}px)`};return v.value===!0?a:Object.assign(a,$e.value)}),Ve=p(()=>"q-drawer__content fit "+(o.isContainer.value!==!0?"scroll":"overflow-auto")),Ie=p(()=>`q-drawer q-drawer--${n.side}`+(Y.value===!0?" q-drawer--mini-animate":"")+(n.bordered===!0?" q-drawer--bordered":"")+(r.value===!0?" q-drawer--dark q-dark":"")+(L.value===!0?" no-transition":m.value===!0?"":" q-layout--prevent-focus")+(v.value===!0?" fixed q-drawer--on-top q-drawer--mobile q-drawer--top-padding":` q-drawer--${B.value===!0?"mini":"standard"}`+(R.value===!0||Q.value!==!0?" fixed":"")+(n.overlay===!0||n.miniToOverlay===!0?" q-drawer--on-top":"")+(pe.value===!0?" q-drawer--top-padding":""))),We=p(()=>{const a=t.lang.rtl===!0?n.side:V.value;return[[re,je,void 0,{[a]:!0,mouse:!0}]]}),ze=p(()=>{const a=t.lang.rtl===!0?V.value:n.side;return[[re,he,void 0,{[a]:!0,mouse:!0}]]}),Ne=p(()=>{const a=t.lang.rtl===!0?V.value:n.side;return[[re,he,void 0,{[a]:!0,mouse:!0,mouseAllDir:!0}]]});function K(){Xe(v,n.behavior==="mobile"||n.behavior!=="desktop"&&o.totalWidth.value<=n.breakpoint)}g(v,a=>{a===!0?(C=m.value,m.value===!0&&T(!1)):n.overlay===!1&&n.behavior!=="mobile"&&C!==!1&&(m.value===!0?(q(0),M(0),G()):X(!1))}),g(()=>n.side,(a,l)=>{o.instances[l]===F&&(o.instances[l]=void 0,o[l].space=!1,o[l].offset=0),o.instances[a]=F,o[a].size=_.value,o[a].space=Q.value,o[a].offset=U.value}),g(o.totalWidth,()=>{(o.isContainer.value===!0||document.qScrollPrevented!==!0)&&K()}),g(()=>n.behavior+n.breakpoint,K),g(o.isContainer,a=>{m.value===!0&&d(a!==!0),a===!0&&K()}),g(o.scrollbarWidth,()=>{q(m.value===!0?0:void 0)}),g(U,a=>{D("offset",a)}),g(Q,a=>{u("onLayout",a),D("space",a)}),g(x,()=>{q()}),g(_,a=>{q(),Z(n.miniToOverlay,a)}),g(()=>n.miniToOverlay,a=>{Z(a,_.value)}),g(()=>t.lang.rtl,()=>{q()}),g(()=>n.mini,()=>{n.noMiniAnimation||n.modelValue===!0&&(He(),o.animate())}),g(B,a=>{u("miniState",a)});function q(a){a===void 0?be(()=>{a=m.value===!0?0:_.value,q(E.value*a)}):(o.isContainer.value===!0&&x.value===!0&&(v.value===!0||Math.abs(a)===_.value)&&(a+=E.value*o.scrollbarWidth.value),me.value=a)}function M(a){fe.value=a}function J(a){const l=a===!0?"remove":o.isContainer.value!==!0?"add":"";l!==""&&document.body.classList[l]("q-body--drawer-toggle")}function He(){c!==null&&clearTimeout(c),e.proxy&&e.proxy.$el&&e.proxy.$el.classList.add("q-drawer--mini-animate"),Y.value=!0,c=setTimeout(()=>{c=null,Y.value=!1,e&&e.proxy&&e.proxy.$el&&e.proxy.$el.classList.remove("q-drawer--mini-animate")},150)}function je(a){if(m.value!==!1)return;const l=_.value,k=H(a.distance.x,0,l);if(a.isFinal===!0){k>=Math.min(75,l)===!0?X():(o.animate(),M(0),q(E.value*l)),L.value=!1;return}q((t.lang.rtl===!0?x.value!==!0:x.value)?Math.max(l-k,0):Math.min(0,k-l)),M(H(k/l,0,1)),a.isFirst===!0&&(L.value=!0)}function he(a){if(m.value!==!0)return;const l=_.value,k=a.direction===n.side,I=(t.lang.rtl===!0?k!==!0:k)?H(a.distance.x,0,l):0;if(a.isFinal===!0){Math.abs(I)<Math.min(75,l)===!0?(o.animate(),M(1),q(0)):T(),L.value=!1;return}q(E.value*I),M(H(1-I/l,0,1)),a.isFirst===!0&&(L.value=!0)}function G(){d(!1),J(!0)}function D(a,l){o.update(n.side,a,l)}function Xe(a,l){a.value!==l&&(a.value=l)}function Z(a,l){D("size",a===!0?n.miniWidth:l)}return o.instances[n.side]=F,Z(n.miniToOverlay,_.value),D("space",Q.value),D("offset",U.value),n.showIfAbove===!0&&n.modelValue!==!0&&m.value===!0&&n["onUpdate:modelValue"]!==void 0&&u("update:modelValue",!0),rt(()=>{u("onLayout",Q.value),u("miniState",B.value),C=n.showIfAbove===!0;const a=()=>{(m.value===!0?ce:ve)(!1,!0)};if(o.totalWidth.value!==0){be(a);return}b=g(o.totalWidth,()=>{b(),b=void 0,m.value===!1&&n.showIfAbove===!0&&v.value===!1?X(!1):a()})}),ut(()=>{b!==void 0&&b(),c!==null&&(clearTimeout(c),c=null),m.value===!0&&G(),o.instances[n.side]===F&&(o.instances[n.side]=void 0,D("size",0),D("offset",0),D("space",!1))}),()=>{const a=[];v.value===!0&&(n.noSwipeOpen===!1&&a.push(lt(z("div",{key:"open",class:`q-drawer__opener fixed-${n.side}`,"aria-hidden":"true"}),We.value)),a.push(ge("div",{ref:"backdrop",class:Qe.value,style:Oe.value,"aria-hidden":"true",onClick:T},void 0,"backdrop",n.noSwipeBackdrop!==!0&&m.value===!0,()=>Ne.value)));const l=B.value===!0&&i.mini!==void 0,k=[z("div",{...y,key:""+l,class:[Ve.value,y.class]},l===!0?i.mini():st(i.default))];return n.elevated===!0&&m.value===!0&&k.push(z("div",{class:"q-layout__shadow absolute-full overflow-hidden no-pointer-events"})),a.push(ge("aside",{ref:"content",class:Ie.value,style:Fe.value},k,"contentclose",n.noSwipeClose!==!0&&v.value===!0,()=>ze.value)),z("div",{class:"q-drawer-container"},a)}}});const At=Me({name:"MenuLink",__name:"Menu",props:{title:{},link:{default:""},onClick:{},icon:{default:""},requireAdmin:{type:Boolean,default:!1}},emits:["toggleMenu"],setup(n,{emit:i}){const u=De(),y=i,e=()=>{y("toggleMenu")};return(t,r)=>!t.requireAdmin||le(u).isAdmin()?(A(),Se(Ae,{key:0},[t.link?(A(),P(qe,{key:0,clickable:"",to:t.link,onClick:e},{default:w(()=>[t.icon?(A(),P(j,{key:0,avatar:""},{default:w(()=>[h(we,{name:t.icon},null,8,["name"])]),_:1})):oe("",!0),h(j,null,{default:w(()=>[h(xe,null,{default:w(()=>[se($(t.title),1)]),_:1})]),_:1})]),_:1},8,["to"])):(A(),P(qe,{key:1,clickable:"",onClick:t.onClick},{default:w(()=>[t.icon?(A(),P(j,{key:0,avatar:""},{default:w(()=>[h(we,{name:t.icon},null,8,["name"])]),_:1})):oe("",!0),h(j,null,{default:w(()=>[h(xe,null,{default:w(()=>[se($(t.title),1)]),_:1})]),_:1})]),_:1},8,["onClick"]))],64)):oe("",!0)}}),Et="lotto",Lt="1.2.4",Tt="A lotto app",Qt="\u6A02\u900F\u5373\u6642\u5831",Ot="Yuue <<EMAIL>>",Pt={lint:"eslint --ext .js,.ts,.vue ./",format:'prettier --write "**/*.{js,ts,vue,scss,html,md,json}" --ignore-path .gitignore',test:'echo "No test specified" && exit 0',dev:"quasar dev -m pwa",build:"quasar build -m pwa && node scripts/generate-version.js","generate-version":"node scripts/generate-version.js"},$t={"@quasar/extras":"^1.16.4","@vee-validate/rules":"^4.13.2",axios:"^1.2.1",bootstrap:"^5.3.3",pinia:"^2.0.11","pinia-plugin-persistedstate":"^4.2.0",quasar:"^2.16.0","vee-validate":"^4.13.2",vue:"^3.4.18","vue-router":"^4.0.12","worker-loader":"^3.0.8",xlsx:"^0.18.5"},Ft={"@quasar/app-vite":"^1.9.0","@types/node":"^12.20.21","@typescript-eslint/eslint-plugin":"^5.10.0","@typescript-eslint/parser":"^5.10.0",autoprefixer:"^10.4.2",eslint:"^8.57.0","eslint-config-prettier":"^8.1.0","eslint-plugin-vue":"^9.0.0",prettier:"^2.5.1",typescript:"^4.5.4","vite-plugin-checker":"^0.6.4","vue-tsc":"^1.8.22","workbox-build":"^7.1.1","workbox-cacheable-response":"^7.1.0","workbox-core":"^7.1.0","workbox-expiration":"^7.1.0","workbox-precaching":"^7.1.0","workbox-routing":"^7.1.0","workbox-strategies":"^7.1.0"},Vt={node:"^20 || ^18 || ^16",npm:">= 6.13.4",yarn:">= 1.21.1"};var It={name:Et,version:Lt,description:Tt,productName:Qt,author:Ot,private:!0,scripts:Pt,dependencies:$t,devDependencies:Ft,engines:Vt};const Wt={class:"q-mr-md"},zt={class:"drawer-content"},Nt={class:"version-footer"},Ht={class:"text-caption text-center text-grey-6 q-pa-md"},Zt=Me({name:"MainLayout",__name:"MainLayout",setup(n){const i=S(It.version),u=ft(),y=gt(),e=De(),t=[{title:"\u6703\u54E1\u7BA1\u7406",link:"/admin/dashboard/user",requireAdmin:!0},{title:"\u500B\u4EBA\u8CC7\u6599",link:"/profile"},{title:"\u958B\u734E\u7D50\u679C",link:"/index"},{title:"\u7248\u8DEF\u5206\u6790",link:"/rd1"},{title:"\u5C3E\u6578\u5206\u6790",link:"/tail"},{title:"\u7D9C\u5408\u5206\u6790",link:"/pattern"},{title:"\u5206\u6790\u5831\u8868",link:"/admin/dashboard/batch-analysis",requireAdmin:!0},{title:"\u5B89\u88DD\u7A0B\u5F0F",link:"/install"},{title:"\u514D\u8CAC\u8072\u660E",link:"/disclaimer"}],r=S(!1);function d(){r.value=!r.value}const f=async()=>{await yt.logout(),e.logout(),u.push("/login")};return(s,o)=>{const C=mt("router-view");return A(),P(wt,{view:"hHh Lpr lFf"},{default:w(()=>[h(_t,{elevated:""},{default:w(()=>[h(kt,null,{default:w(()=>{var c;return[h(ke,{flat:"",dense:"",round:"",icon:"menu","aria-label":"Menu",onClick:d}),h(Ct,null,{default:w(()=>[se($(le(y).val),1)]),_:1}),h(xt),N("div",Wt,$((c=le(e).userInfo)==null?void 0:c.name),1),h(ke,{flat:"",dense:"",round:"",icon:"logout","aria-label":"Logout",onClick:f})]}),_:1})]),_:1}),h(St,{modelValue:r.value,"onUpdate:modelValue":o[0]||(o[0]=c=>r.value=c),bordered:"",class:"drawer-container"},{default:w(()=>[N("div",zt,[h(Bt,{separator:""},{default:w(()=>[(A(),Se(Ae,null,pt(t,c=>h(At,bt({key:c.title,ref_for:!0},c,{onToggleMenu:d}),null,16)),64))]),_:1})]),N("div",Nt,[h(ht),N("div",Ht," \u7248\u672C:"+$(i.value),1)])]),_:1},8,["modelValue"]),h(qt,null,{default:w(()=>[h(C)]),_:1})]),_:1})}}});export{Zt as default};
