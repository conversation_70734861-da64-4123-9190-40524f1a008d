import{r as O,o as Yt,a as Ne,n as Qe,b as ft,w as ne,c as s,h as r,T as _t,d as Da,s as yc,e as pc,f as bn,g as zo,i as Et,j as ta,m as Oo,k as wc,l as _c,p as Ut,q as Sc,t as ve,u as an,v as yn,x as Ro,K as Gi,y as xc,z as Cc,A as kc,B as Zi}from"./index.5cb20bf8.js";/*!
 * Quasar Framework v2.16.8
 * (c) 2015-present <PERSON><PERSON><PERSON>
 * Released under the MIT License.
 */function St(e,t,n,l){return Object.defineProperty(e,t,{get:n,set:l,enumerable:!0}),e}function Ji(e,t){for(const n in t)St(e,n,t[n]);return e}var Pt=O(!1),go;function qc(e,t){const n=/(edg|edge|edga|edgios)\/([\w.]+)/.exec(e)||/(opr)[\/]([\w.]+)/.exec(e)||/(vivaldi)[\/]([\w.]+)/.exec(e)||/(chrome|crios)[\/]([\w.]+)/.exec(e)||/(version)(applewebkit)[\/]([\w.]+).*(safari)[\/]([\w.]+)/.exec(e)||/(webkit)[\/]([\w.]+).*(version)[\/]([\w.]+).*(safari)[\/]([\w.]+)/.exec(e)||/(firefox|fxios)[\/]([\w.]+)/.exec(e)||/(webkit)[\/]([\w.]+)/.exec(e)||/(opera)(?:.*version|)[\/]([\w.]+)/.exec(e)||[];return{browser:n[5]||n[3]||n[1]||"",version:n[4]||n[2]||"0",platform:t[0]||""}}function Tc(e){return/(ipad)/.exec(e)||/(ipod)/.exec(e)||/(windows phone)/.exec(e)||/(iphone)/.exec(e)||/(kindle)/.exec(e)||/(silk)/.exec(e)||/(android)/.exec(e)||/(win)/.exec(e)||/(mac)/.exec(e)||/(linux)/.exec(e)||/(cros)/.exec(e)||/(playbook)/.exec(e)||/(bb)/.exec(e)||/(blackberry)/.exec(e)||[]}var eu="ontouchstart"in window||window.navigator.maxTouchPoints>0;function Mc(e){const t=e.toLowerCase(),n=Tc(t),l=qc(t,n),a={};l.browser&&(a[l.browser]=!0,a.version=l.version,a.versionNumber=parseInt(l.version,10)),l.platform&&(a[l.platform]=!0);const o=a.android||a.ios||a.bb||a.blackberry||a.ipad||a.iphone||a.ipod||a.kindle||a.playbook||a.silk||a["windows phone"];if(o===!0||t.indexOf("mobile")!==-1?a.mobile=!0:a.desktop=!0,a["windows phone"]&&(a.winphone=!0,delete a["windows phone"]),a.edga||a.edgios||a.edg?(a.edge=!0,l.browser="edge"):a.crios?(a.chrome=!0,l.browser="chrome"):a.fxios&&(a.firefox=!0,l.browser="firefox"),(a.ipod||a.ipad||a.iphone)&&(a.ios=!0),a.vivaldi&&(l.browser="vivaldi",a.vivaldi=!0),(a.chrome||a.opr||a.safari||a.vivaldi||a.mobile===!0&&a.ios!==!0&&o!==!0)&&(a.webkit=!0),a.opr&&(l.browser="opera",a.opera=!0),a.safari&&(a.blackberry||a.bb?(l.browser="blackberry",a.blackberry=!0):a.playbook?(l.browser="playbook",a.playbook=!0):a.android?(l.browser="android",a.android=!0):a.kindle?(l.browser="kindle",a.kindle=!0):a.silk&&(l.browser="silk",a.silk=!0)),a.name=l.browser,a.platform=l.platform,t.indexOf("electron")!==-1)a.electron=!0;else if(document.location.href.indexOf("-extension://")!==-1)a.bex=!0;else{if(window.Capacitor!==void 0?(a.capacitor=!0,a.nativeMobile=!0,a.nativeMobileWrapper="capacitor"):(window._cordovaNative!==void 0||window.cordova!==void 0)&&(a.cordova=!0,a.nativeMobile=!0,a.nativeMobileWrapper="cordova"),Pt.value===!0&&(go={is:{...a}}),eu===!0&&a.mac===!0&&(a.desktop===!0&&a.safari===!0||a.nativeMobile===!0&&a.android!==!0&&a.ios!==!0&&a.ipad!==!0)){delete a.mac,delete a.desktop;const i=Math.min(window.innerHeight,window.innerWidth)>414?"ipad":"iphone";Object.assign(a,{mobile:!0,ios:!0,platform:i,[i]:!0})}a.mobile!==!0&&window.navigator.userAgentData&&window.navigator.userAgentData.mobile&&(delete a.desktop,a.mobile=!0)}return a}var qr=navigator.userAgent||navigator.vendor||window.opera,$c={has:{touch:!1,webStorage:!1},within:{iframe:!1}},Ae={userAgent:qr,is:Mc(qr),has:{touch:eu},within:{iframe:window.self!==window.top}},ho={install(e){const{$q:t}=e;Pt.value===!0?(e.onSSRHydrated.push(()=>{Object.assign(t.platform,Ae),Pt.value=!1}),t.platform=ta(this)):t.platform=this}};{let e;St(Ae.has,"webStorage",()=>{if(e!==void 0)return e;try{if(window.localStorage)return e=!0,!0}catch{}return e=!1,!1}),Object.assign(ho,Ae),Pt.value===!0&&(Object.assign(ho,go,$c),go=null)}var Ea=ho;function te(e){return Oo(wc(e))}function Xt(e){return Oo(e)}var pn=(e,t)=>{const n=ta(e);for(const l in e)St(t,l,()=>n[l],a=>{n[l]=a});return t},et={hasPassive:!1,passiveCapture:!0,notPassiveCapture:!0};try{const e=Object.defineProperty({},"passive",{get(){Object.assign(et,{hasPassive:!0,passive:{passive:!0},notPassive:{passive:!1},passiveCapture:{passive:!0,capture:!0},notPassiveCapture:{passive:!1,capture:!0}})}});window.addEventListener("qtest",null,e),window.removeEventListener("qtest",null,e)}catch{}function it(){}function Ia(e){return e.button===0}function Bc(e){return e.button===1}function Pc(e){return e.button===2}function $t(e){return e.touches&&e.touches[0]?e=e.touches[0]:e.changedTouches&&e.changedTouches[0]?e=e.changedTouches[0]:e.targetTouches&&e.targetTouches[0]&&(e=e.targetTouches[0]),{top:e.clientY,left:e.clientX}}function tu(e){if(e.path)return e.path;if(e.composedPath)return e.composedPath();const t=[];let n=e.target;for(;n;){if(t.push(n),n.tagName==="HTML")return t.push(document),t.push(window),t;n=n.parentElement}}var Ec=40,Lc=800;function Ac(e){let t=e.deltaX,n=e.deltaY;if((t||n)&&e.deltaMode){const l=e.deltaMode===1?Ec:Lc;t*=l,n*=l}return e.shiftKey&&!t&&([n,t]=[t,n]),{x:t,y:n}}function dt(e){e.stopPropagation()}function wt(e){e.cancelable!==!1&&e.preventDefault()}function Fe(e){e.cancelable!==!1&&e.preventDefault(),e.stopPropagation()}function Pn(e,t){if(e===void 0||t===!0&&e.__dragPrevented===!0)return;const n=t===!0?l=>{l.__dragPrevented=!0,l.addEventListener("dragstart",wt,et.notPassiveCapture)}:l=>{delete l.__dragPrevented,l.removeEventListener("dragstart",wt,et.notPassiveCapture)};e.querySelectorAll("a, img").forEach(n)}function vt(e,t,n){const l=`__q_${t}_evt`;e[l]=e[l]!==void 0?e[l].concat(n):n,n.forEach(a=>{a[0].addEventListener(a[1],e[a[2]],et[a[3]])})}function Mt(e,t){const n=`__q_${t}_evt`;e[n]!==void 0&&(e[n].forEach(l=>{l[0].removeEventListener(l[1],e[l[2]],et[l[3]])}),e[n]=void 0)}var Ng={listenOpts:et,leftClick:Ia,middleClick:Bc,rightClick:Pc,position:$t,getEventPath:tu,getMouseWheelDistance:Ac,stop:dt,prevent:wt,stopAndPrevent:Fe,preventDraggable:Pn};function fa(e,t=250,n){let l=null;function a(){const o=arguments,i=()=>{l=null,n!==!0&&e.apply(this,o)};l!==null?clearTimeout(l):n===!0&&e.apply(this,o),l=setTimeout(i,t)}return a.cancel=()=>{l!==null&&clearTimeout(l)},a}var Ul=["sm","md","lg","xl"],{passive:Tr}=et,zc=pn({width:0,height:0,name:"xs",sizes:{sm:600,md:1024,lg:1440,xl:1920},lt:{sm:!0,md:!0,lg:!0,xl:!0},gt:{xs:!1,sm:!1,md:!1,lg:!1},xs:!0,sm:!1,md:!1,lg:!1,xl:!1},{setSizes:it,setDebounce:it,install({$q:e,onSSRHydrated:t}){if(e.screen=this,this.__installed===!0){e.config.screen!==void 0&&(e.config.screen.bodyClasses===!1?document.body.classList.remove(`screen--${this.name}`):this.__update(!0));return}const{visualViewport:n}=window,l=n||window,a=document.scrollingElement||document.documentElement,o=n===void 0||Ae.is.mobile===!0?()=>[Math.max(window.innerWidth,a.clientWidth),Math.max(window.innerHeight,a.clientHeight)]:()=>[n.width*n.scale+window.innerWidth-a.clientWidth,n.height*n.scale+window.innerHeight-a.clientHeight],i=e.config.screen!==void 0&&e.config.screen.bodyClasses===!0;this.__update=h=>{const[m,v]=o();if(v!==this.height&&(this.height=v),m!==this.width)this.width=m;else if(h!==!0)return;let g=this.sizes;this.gt.xs=m>=g.sm,this.gt.sm=m>=g.md,this.gt.md=m>=g.lg,this.gt.lg=m>=g.xl,this.lt.sm=m<g.sm,this.lt.md=m<g.md,this.lt.lg=m<g.lg,this.lt.xl=m<g.xl,this.xs=this.lt.sm,this.sm=this.gt.xs===!0&&this.lt.md===!0,this.md=this.gt.sm===!0&&this.lt.lg===!0,this.lg=this.gt.md===!0&&this.lt.xl===!0,this.xl=this.gt.lg,g=this.xs===!0&&"xs"||this.sm===!0&&"sm"||this.md===!0&&"md"||this.lg===!0&&"lg"||"xl",g!==this.name&&(i===!0&&(document.body.classList.remove(`screen--${this.name}`),document.body.classList.add(`screen--${g}`)),this.name=g)};let u,d={},f=16;this.setSizes=h=>{Ul.forEach(m=>{h[m]!==void 0&&(d[m]=h[m])})},this.setDebounce=h=>{f=h};const c=()=>{const h=getComputedStyle(document.body);h.getPropertyValue("--q-size-sm")&&Ul.forEach(m=>{this.sizes[m]=parseInt(h.getPropertyValue(`--q-size-${m}`),10)}),this.setSizes=m=>{Ul.forEach(v=>{m[v]&&(this.sizes[v]=m[v])}),this.__update(!0)},this.setDebounce=m=>{u!==void 0&&l.removeEventListener("resize",u,Tr),u=m>0?fa(this.__update,m):this.__update,l.addEventListener("resize",u,Tr)},this.setDebounce(f),Object.keys(d).length!==0?(this.setSizes(d),d=void 0):this.__update(),i===!0&&this.name==="xs"&&document.body.classList.add("screen--xs")};Pt.value===!0?t.push(c):c()}}),Tt=pn({isActive:!1,mode:!1},{__media:void 0,set(e){Tt.mode=e,e==="auto"?(Tt.__media===void 0&&(Tt.__media=window.matchMedia("(prefers-color-scheme: dark)"),Tt.__updateMedia=()=>{Tt.set("auto")},Tt.__media.addListener(Tt.__updateMedia)),e=Tt.__media.matches):Tt.__media!==void 0&&(Tt.__media.removeListener(Tt.__updateMedia),Tt.__media=void 0),Tt.isActive=e===!0,document.body.classList.remove(`body--${e===!0?"light":"dark"}`),document.body.classList.add(`body--${e===!0?"dark":"light"}`)},toggle(){Tt.set(Tt.isActive===!1)},install({$q:e,ssrContext:t}){const{dark:n}=e.config;e.dark=this,this.__installed!==!0&&this.set(n!==void 0?n:!1)}}),Oc=Tt;function Rc(e,t,n=document.body){if(typeof e!="string")throw new TypeError("Expected a string as propName");if(typeof t!="string")throw new TypeError("Expected a string as value");if(!(n instanceof Element))throw new TypeError("Expected a DOM element");n.style.setProperty(`--q-${e}`,t)}var nu=!1;function Fc(e){nu=e.isComposing===!0}function Hn(e){return nu===!0||e!==Object(e)||e.isComposing===!0||e.qKeyEvent===!0}function Vt(e,t){return Hn(e)===!0?!1:[].concat(t).includes(e.keyCode)}function au(e){if(e.ios===!0)return"ios";if(e.android===!0)return"android"}function Vc({is:e,has:t,within:n},l){const a=[e.desktop===!0?"desktop":"mobile",`${t.touch===!1?"no-":""}touch`];if(e.mobile===!0){const o=au(e);o!==void 0&&a.push("platform-"+o)}if(e.nativeMobile===!0){const o=e.nativeMobileWrapper;a.push(o),a.push("native-mobile"),e.ios===!0&&(l[o]===void 0||l[o].iosStatusBarPadding!==!1)&&a.push("q-ios-padding")}else e.electron===!0?a.push("electron"):e.bex===!0&&a.push("bex");return n.iframe===!0&&a.push("within-iframe"),a}function Dc(){const{is:e}=Ae,t=document.body.className,n=new Set(t.replace(/ {2}/g," ").split(" "));if(e.nativeMobile!==!0&&e.electron!==!0&&e.bex!==!0){if(e.desktop===!0)n.delete("mobile"),n.delete("platform-ios"),n.delete("platform-android"),n.add("desktop");else if(e.mobile===!0){n.delete("desktop"),n.add("mobile"),n.delete("platform-ios"),n.delete("platform-android");const a=au(e);a!==void 0&&n.add(`platform-${a}`)}}Ae.has.touch===!0&&(n.delete("no-touch"),n.add("touch")),Ae.within.iframe===!0&&n.add("within-iframe");const l=Array.from(n).join(" ");t!==l&&(document.body.className=l)}function Ic(e){for(const t in e)Rc(t,e[t])}var Hc={install(e){if(this.__installed!==!0){if(Pt.value===!0)Dc();else{const{$q:t}=e;t.config.brand!==void 0&&Ic(t.config.brand);const n=Vc(Ae,t.config);document.body.classList.add.apply(document.body.classList,n)}Ae.is.ios===!0&&document.body.addEventListener("touchstart",it),window.addEventListener("keydown",Fc,!0)}}},lu=()=>!0;function Nc(e){return typeof e=="string"&&e!==""&&e!=="/"&&e!=="#/"}function Qc(e){return e.startsWith("#")===!0&&(e=e.substring(1)),e.startsWith("/")===!1&&(e="/"+e),e.endsWith("/")===!0&&(e=e.substring(0,e.length-1)),"#"+e}function jc(e){if(e.backButtonExit===!1)return()=>!1;if(e.backButtonExit==="*")return lu;const t=["#/"];return Array.isArray(e.backButtonExit)===!0&&t.push(...e.backButtonExit.filter(Nc).map(Qc)),()=>t.includes(window.location.hash)}var La={__history:[],add:it,remove:it,install({$q:e}){if(this.__installed===!0)return;const{cordova:t,capacitor:n}=Ae.is;if(t!==!0&&n!==!0)return;const l=e.config[t===!0?"cordova":"capacitor"];if(l!==void 0&&l.backButton===!1||n===!0&&(window.Capacitor===void 0||window.Capacitor.Plugins.App===void 0))return;this.add=i=>{i.condition===void 0&&(i.condition=lu),this.__history.push(i)},this.remove=i=>{const u=this.__history.indexOf(i);u>=0&&this.__history.splice(u,1)};const a=jc(Object.assign({backButtonExit:!0},l)),o=()=>{if(this.__history.length){const i=this.__history[this.__history.length-1];i.condition()===!0&&(this.__history.pop(),i.handler())}else a()===!0?navigator.app.exitApp():window.history.back()};t===!0?document.addEventListener("deviceready",()=>{document.addEventListener("backbutton",o,!1)}):window.Capacitor.Plugins.App.addListener("backButton",o)}},bo={isoName:"en-US",nativeName:"English (US)",label:{clear:"Clear",ok:"OK",cancel:"Cancel",close:"Close",set:"Set",select:"Select",reset:"Reset",remove:"Remove",update:"Update",create:"Create",search:"Search",filter:"Filter",refresh:"Refresh",expand:e=>e?`Expand "${e}"`:"Expand",collapse:e=>e?`Collapse "${e}"`:"Collapse"},date:{days:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),daysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),firstDayOfWeek:0,format24h:!1,pluralDay:"days"},table:{noData:"No data available",noResults:"No matching records found",loading:"Loading...",selectedRecords:e=>e===1?"1 record selected.":(e===0?"No":e)+" records selected.",recordsPerPage:"Records per page:",allRows:"All",pagination:(e,t,n)=>e+"-"+t+" of "+n,columns:"Columns"},editor:{url:"URL",bold:"Bold",italic:"Italic",strikethrough:"Strikethrough",underline:"Underline",unorderedList:"Unordered List",orderedList:"Ordered List",subscript:"Subscript",superscript:"Superscript",hyperlink:"Hyperlink",toggleFullscreen:"Toggle Fullscreen",quote:"Quote",left:"Left align",center:"Center align",right:"Right align",justify:"Justify align",print:"Print",outdent:"Decrease indentation",indent:"Increase indentation",removeFormat:"Remove formatting",formatting:"Formatting",fontSize:"Font Size",align:"Align",hr:"Insert Horizontal Rule",undo:"Undo",redo:"Redo",heading1:"Heading 1",heading2:"Heading 2",heading3:"Heading 3",heading4:"Heading 4",heading5:"Heading 5",heading6:"Heading 6",paragraph:"Paragraph",code:"Code",size1:"Very small",size2:"A bit small",size3:"Normal",size4:"Medium-large",size5:"Big",size6:"Very big",size7:"Maximum",defaultFont:"Default Font",viewSource:"View Source"},tree:{noNodes:"No nodes available",noResults:"No matching nodes found"}};function Mr(){const e=Array.isArray(navigator.languages)===!0&&navigator.languages.length!==0?navigator.languages[0]:navigator.language;if(typeof e=="string")return e.split(/[-_]/).map((t,n)=>n===0?t.toLowerCase():n>1||t.length<4?t.toUpperCase():t[0].toUpperCase()+t.slice(1).toLowerCase()).join("-")}var Tn=pn({__qLang:{}},{getLocale:Mr,set(e=bo,t){const n={...e,rtl:e.rtl===!0,getLocale:Mr};{if(n.set=Tn.set,Tn.__langConfig===void 0||Tn.__langConfig.noHtmlAttrs!==!0){const l=document.documentElement;l.setAttribute("dir",n.rtl===!0?"rtl":"ltr"),l.setAttribute("lang",n.isoName)}Object.assign(Tn.__qLang,n)}},install({$q:e,lang:t,ssrContext:n}){e.lang=Tn.__qLang,Tn.__langConfig=e.config.lang,this.__installed===!0?t!==void 0&&this.set(t):(this.props=new Proxy(this.__qLang,{get(){return Reflect.get(...arguments)},ownKeys(l){return Reflect.ownKeys(l).filter(a=>a!=="set"&&a!=="getLocale")}}),this.set(t||bo))}}),Ll=Tn,Kc={name:"material-icons",type:{positive:"check_circle",negative:"warning",info:"info",warning:"priority_high"},arrow:{up:"arrow_upward",right:"arrow_forward",down:"arrow_downward",left:"arrow_back",dropdown:"arrow_drop_down"},chevron:{left:"chevron_left",right:"chevron_right"},colorPicker:{spectrum:"gradient",tune:"tune",palette:"style"},pullToRefresh:{icon:"refresh"},carousel:{left:"chevron_left",right:"chevron_right",up:"keyboard_arrow_up",down:"keyboard_arrow_down",navigationIcon:"lens"},chip:{remove:"cancel",selected:"check"},datetime:{arrowLeft:"chevron_left",arrowRight:"chevron_right",now:"access_time",today:"today"},editor:{bold:"format_bold",italic:"format_italic",strikethrough:"strikethrough_s",underline:"format_underlined",unorderedList:"format_list_bulleted",orderedList:"format_list_numbered",subscript:"vertical_align_bottom",superscript:"vertical_align_top",hyperlink:"link",toggleFullscreen:"fullscreen",quote:"format_quote",left:"format_align_left",center:"format_align_center",right:"format_align_right",justify:"format_align_justify",print:"print",outdent:"format_indent_decrease",indent:"format_indent_increase",removeFormat:"format_clear",formatting:"text_format",fontSize:"format_size",align:"format_align_left",hr:"remove",undo:"undo",redo:"redo",heading:"format_size",code:"code",size:"format_size",font:"font_download",viewSource:"code"},expansionItem:{icon:"keyboard_arrow_down",denseIcon:"arrow_drop_down"},fab:{icon:"add",activeIcon:"close"},field:{clear:"cancel",error:"error"},pagination:{first:"first_page",prev:"keyboard_arrow_left",next:"keyboard_arrow_right",last:"last_page"},rating:{icon:"grade"},stepper:{done:"check",active:"edit",error:"warning"},tabs:{left:"chevron_left",right:"chevron_right",up:"keyboard_arrow_up",down:"keyboard_arrow_down"},table:{arrowUp:"arrow_upward",warning:"warning",firstPage:"first_page",prevPage:"chevron_left",nextPage:"chevron_right",lastPage:"last_page"},tree:{icon:"play_arrow"},uploader:{done:"done",clear:"clear",add:"add_box",upload:"cloud_upload",removeQueue:"clear_all",removeUploaded:"done_all"}},yo=pn({iconMapFn:null,__qIconSet:{}},{set(e,t){const n={...e};n.set=yo.set,Object.assign(yo.__qIconSet,n)},install({$q:e,iconSet:t,ssrContext:n}){e.config.iconMapFn!==void 0&&(this.iconMapFn=e.config.iconMapFn),e.iconSet=this.__qIconSet,St(e,"iconMapFn",()=>this.iconMapFn,l=>{this.iconMapFn=l}),this.__installed===!0?t!==void 0&&this.set(t):(this.props=new Proxy(this.__qIconSet,{get(){return Reflect.get(...arguments)},ownKeys(l){return Reflect.ownKeys(l).filter(a=>a!=="set")}}),this.set(t||Kc))}}),ou=yo,ru="_q_",iu="_q_t_",uu="_q_s_",Nn="_q_l_",su="_q_pc_",cu="_q_f_",na="_q_fo_",du="_q_tabs_",fu="_q_u_";function Je(){}var gl={},vu=!1;function Wc(){vu=!0}function Rt(e,t){if(e===t)return!0;if(e!==null&&t!==null&&typeof e=="object"&&typeof t=="object"){if(e.constructor!==t.constructor)return!1;let n,l;if(e.constructor===Array){if(n=e.length,n!==t.length)return!1;for(l=n;l--!==0;)if(Rt(e[l],t[l])!==!0)return!1;return!0}if(e.constructor===Map){if(e.size!==t.size)return!1;let o=e.entries();for(l=o.next();l.done!==!0;){if(t.has(l.value[0])!==!0)return!1;l=o.next()}for(o=e.entries(),l=o.next();l.done!==!0;){if(Rt(l.value[1],t.get(l.value[0]))!==!0)return!1;l=o.next()}return!0}if(e.constructor===Set){if(e.size!==t.size)return!1;const o=e.entries();for(l=o.next();l.done!==!0;){if(t.has(l.value[0])!==!0)return!1;l=o.next()}return!0}if(e.buffer!=null&&e.buffer.constructor===ArrayBuffer){if(n=e.length,n!==t.length)return!1;for(l=n;l--!==0;)if(e[l]!==t[l])return!1;return!0}if(e.constructor===RegExp)return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();const a=Object.keys(e).filter(o=>e[o]!==void 0);if(n=a.length,n!==Object.keys(t).filter(o=>t[o]!==void 0).length)return!1;for(l=n;l--!==0;){const o=a[l];if(Rt(e[o],t[o])!==!0)return!1}return!0}return e!==e&&t!==t}function yt(e){return e!==null&&typeof e=="object"&&Array.isArray(e)!==!0}function sa(e){return Object.prototype.toString.call(e)==="[object Date]"}function mu(e){return Object.prototype.toString.call(e)==="[object RegExp]"}function Rn(e){return typeof e=="number"&&isFinite(e)}var Qg={deepEqual:Rt,object:yt,date:sa,regexp:mu,number:Rn},$r=[Ea,Hc,Oc,zc,La,Ll,ou];function Al(e,t){const n=_c(e);n.config.globalProperties=t.config.globalProperties;const{reload:l,...a}=t._context;return Object.assign(n._context,a),n}function Br(e,t){t.forEach(n=>{n.install(e),n.__installed=!0})}function Uc(e,t,n){e.config.globalProperties.$q=n.$q,e.provide(ru,n.$q),Br(n,$r),t.components!==void 0&&Object.values(t.components).forEach(l=>{yt(l)===!0&&l.name!==void 0&&e.component(l.name,l)}),t.directives!==void 0&&Object.values(t.directives).forEach(l=>{yt(l)===!0&&l.name!==void 0&&e.directive(l.name,l)}),t.plugins!==void 0&&Br(n,Object.values(t.plugins).filter(l=>typeof l.install=="function"&&$r.includes(l)===!1)),Pt.value===!0&&(n.$q.onSSRHydrated=()=>{n.onSSRHydrated.forEach(l=>{l()}),n.$q.onSSRHydrated=()=>{}})}var Yc=function(e,t={}){const n={version:"2.16.8"};vu===!1?(t.config!==void 0&&Object.assign(gl,t.config),n.config={...gl},Wc()):n.config=t.config||{},Uc(e,t,{parentApp:e,$q:n,lang:t.lang,iconSet:t.iconSet,onSSRHydrated:[]})},Pr=["B","KB","MB","GB","TB","PB"];function hl(e,t=1){let n=0;for(;parseInt(e,10)>=1024&&n<Pr.length-1;)e/=1024,++n;return`${e.toFixed(t)}${Pr[n]}`}function gu(e){return e.charAt(0).toUpperCase()+e.slice(1)}function tt(e,t,n){return n<=t?t:Math.min(n,Math.max(t,e))}function Aa(e,t,n){if(n<=t)return t;const l=n-t+1;let a=t+(e-t)%l;return a<t&&(a=l+a),a===0?0:a}function Ye(e,t=2,n="0"){if(e==null)return e;const l=""+e;return l.length>=t?l:new Array(t-l.length+1).join(n)+l}var jg={humanStorageSize:hl,capitalize:gu,between:tt,normalizeToInterval:Aa,pad:Ye},Fo=XMLHttpRequest,hu=Fo.prototype.open,Xc=["top","right","bottom","left"],bl=[],qa=0;function Gc({p:e,pos:t,active:n,horiz:l,reverse:a,dir:o}){let i=1,u=1;return l===!0?(a===!0&&(i=-1),t==="bottom"&&(u=-1),{transform:`translate3d(${i*(e-100)}%,${n?0:u*-200}%,0)`}):(a===!0&&(u=-1),t==="right"&&(i=-1),{transform:`translate3d(${n?0:o*i*-200}%,${u*(e-100)}%,0)`})}function Zc(e,t){return typeof t!="number"&&(e<25?t=Math.random()*3+3:e<65?t=Math.random()*3:e<85?t=Math.random()*2:e<99?t=.6:t=0),tt(e+t,0,100)}function Jc(e){qa++,bl.push(e),!(qa>1)&&(Fo.prototype.open=function(t,n){const l=[],a=()=>{bl.forEach(i=>{(i.hijackFilter.value===null||i.hijackFilter.value(n)===!0)&&(i.start(),l.push(i.stop))})},o=()=>{l.forEach(i=>{i()})};this.addEventListener("loadstart",a,{once:!0}),this.addEventListener("loadend",o,{once:!0}),hu.apply(this,arguments)})}function ed(e){bl=bl.filter(t=>t.start!==e),qa=Math.max(0,qa-1),qa===0&&(Fo.prototype.open=hu)}var td=te({name:"QAjaxBar",props:{position:{type:String,default:"top",validator:e=>Xc.includes(e)},size:{type:String,default:"2px"},color:String,skipHijack:Boolean,reverse:Boolean,hijackFilter:Function},emits:["start","stop"],setup(e,{emit:t}){const{proxy:n}=ve(),l=O(0),a=O(!1),o=O(!0);let i=0,u=null,d;const f=s(()=>`q-loading-bar q-loading-bar--${e.position}`+(e.color!==void 0?` bg-${e.color}`:"")+(o.value===!0?"":" no-transition")),c=s(()=>e.position==="top"||e.position==="bottom"),h=s(()=>c.value===!0?"height":"width"),m=s(()=>{const y=a.value,b=Gc({p:l.value,pos:e.position,active:y,horiz:c.value,reverse:n.$q.lang.rtl===!0&&["top","bottom"].includes(e.position)?e.reverse===!1:e.reverse,dir:n.$q.lang.rtl===!0?-1:1});return b[h.value]=e.size,b.opacity=y?1:0,b}),v=s(()=>a.value===!0?{role:"progressbar","aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":l.value}:{"aria-hidden":"true"});function g(y=300){const b=d;return d=Math.max(0,y)||0,i++,i>1?(b===0&&y>0?k():u!==null&&b>0&&y<=0&&(clearTimeout(u),u=null),i):(u!==null&&clearTimeout(u),t("start"),l.value=0,u=setTimeout(()=>{u=null,o.value=!0,y>0&&k()},a._value===!0?500:1),a._value!==!0&&(a.value=!0,o.value=!1),i)}function _(y){return i>0&&(l.value=Zc(l.value,y)),i}function p(){if(i=Math.max(0,i-1),i>0)return i;u!==null&&(clearTimeout(u),u=null),t("stop");const y=()=>{o.value=!0,l.value=100,u=setTimeout(()=>{u=null,a.value=!1},1e3)};return l.value===0?u=setTimeout(y,1):y(),i}function k(){l.value<100&&(u=setTimeout(()=>{u=null,_(),k()},d))}let w;return ft(()=>{e.skipHijack!==!0&&(w=!0,Jc({start:g,stop:p,hijackFilter:s(()=>e.hijackFilter||null)}))}),Ne(()=>{u!==null&&clearTimeout(u),w===!0&&ed(g)}),Object.assign(n,{start:g,stop:p,increment:_}),()=>r("div",{class:f.value,style:m.value,...v.value})}}),po={xs:18,sm:24,md:32,lg:38,xl:46},sn={size:String};function cn(e,t=po){return s(()=>e.size!==void 0?{fontSize:e.size in t?`${t[e.size]}px`:e.size}:null)}function Ce(e,t){return e!==void 0&&e()||t}function Ha(e,t){if(e!==void 0){const n=e();if(n!=null)return n.slice()}return t}function mt(e,t){return e!==void 0?t.concat(e()):t}function Vo(e,t){return e===void 0?t:t!==void 0?t.concat(e()):e()}function Dt(e,t,n,l,a,o){t.key=l+a;const i=r(e,t,n);return a===!0?Ut(i,o()):i}var Er="0 0 24 24",Lr=e=>e,Yl=e=>`ionicons ${e}`,bu={"mdi-":e=>`mdi ${e}`,"icon-":Lr,"bt-":e=>`bt ${e}`,"eva-":e=>`eva ${e}`,"ion-md":Yl,"ion-ios":Yl,"ion-logo":Yl,"iconfont ":Lr,"ti-":e=>`themify-icon ${e}`,"bi-":e=>`bootstrap-icons ${e}`},yu={o_:"-outlined",r_:"-round",s_:"-sharp"},pu={sym_o_:"-outlined",sym_r_:"-rounded",sym_s_:"-sharp"},nd=new RegExp("^("+Object.keys(bu).join("|")+")"),ad=new RegExp("^("+Object.keys(yu).join("|")+")"),Ar=new RegExp("^("+Object.keys(pu).join("|")+")"),ld=/^[Mm]\s?[-+]?\.?\d/,od=/^img:/,rd=/^svguse:/,id=/^ion-/,ud=/^(fa-(sharp|solid|regular|light|brands|duotone|thin)|[lf]a[srlbdk]?) /,je=te({name:"QIcon",props:{...sn,tag:{type:String,default:"i"},name:String,color:String,left:Boolean,right:Boolean},setup(e,{slots:t}){const{proxy:{$q:n}}=ve(),l=cn(e),a=s(()=>"q-icon"+(e.left===!0?" on-left":"")+(e.right===!0?" on-right":"")+(e.color!==void 0?` text-${e.color}`:"")),o=s(()=>{let i,u=e.name;if(u==="none"||!u)return{none:!0};if(n.iconMapFn!==null){const c=n.iconMapFn(u);if(c!==void 0)if(c.icon!==void 0){if(u=c.icon,u==="none"||!u)return{none:!0}}else return{cls:c.cls,content:c.content!==void 0?c.content:" "}}if(ld.test(u)===!0){const[c,h=Er]=u.split("|");return{svg:!0,viewBox:h,nodes:c.split("&&").map(m=>{const[v,g,_]=m.split("@@");return r("path",{style:g,d:v,transform:_})})}}if(od.test(u)===!0)return{img:!0,src:u.substring(4)};if(rd.test(u)===!0){const[c,h=Er]=u.split("|");return{svguse:!0,src:c.substring(7),viewBox:h}}let d=" ";const f=u.match(nd);if(f!==null)i=bu[f[1]](u);else if(ud.test(u)===!0)i=u;else if(id.test(u)===!0)i=`ionicons ion-${n.platform.is.ios===!0?"ios":"md"}${u.substring(3)}`;else if(Ar.test(u)===!0){i="notranslate material-symbols";const c=u.match(Ar);c!==null&&(u=u.substring(6),i+=pu[c[1]]),d=u}else{i="notranslate material-icons";const c=u.match(ad);c!==null&&(u=u.substring(2),i+=yu[c[1]]),d=u}return{cls:i,content:d}});return()=>{const i={class:a.value,style:l.value,"aria-hidden":"true",role:"presentation"};return o.value.none===!0?r(e.tag,i,Ce(t.default)):o.value.img===!0?r(e.tag,i,mt(t.default,[r("img",{src:o.value.src})])):o.value.svg===!0?r(e.tag,i,mt(t.default,[r("svg",{viewBox:o.value.viewBox||"0 0 24 24"},o.value.nodes)])):o.value.svguse===!0?r(e.tag,i,mt(t.default,[r("svg",{viewBox:o.value.viewBox},[r("use",{"xlink:href":o.value.src})])])):(o.value.cls!==void 0&&(i.class+=" "+o.value.cls),r(e.tag,i,mt(t.default,[o.value.content])))}}}),sd=te({name:"QAvatar",props:{...sn,fontSize:String,color:String,textColor:String,icon:String,square:Boolean,rounded:Boolean},setup(e,{slots:t}){const n=cn(e),l=s(()=>"q-avatar"+(e.color?` bg-${e.color}`:"")+(e.textColor?` text-${e.textColor} q-chip--colored`:"")+(e.square===!0?" q-avatar--square":e.rounded===!0?" rounded-borders":"")),a=s(()=>e.fontSize?{fontSize:e.fontSize}:null);return()=>{const o=e.icon!==void 0?[r(je,{name:e.icon})]:void 0;return r("div",{class:l.value,style:n.value},[r("div",{class:"q-avatar__content row flex-center overflow-hidden",style:a.value},Vo(t.default,o))])}}}),cd=["top","middle","bottom"],Kg=te({name:"QBadge",props:{color:String,textColor:String,floating:Boolean,transparent:Boolean,multiLine:Boolean,outline:Boolean,rounded:Boolean,label:[Number,String],align:{type:String,validator:e=>cd.includes(e)}},setup(e,{slots:t}){const n=s(()=>e.align!==void 0?{verticalAlign:e.align}:null),l=s(()=>{const a=e.outline===!0&&e.color||e.textColor;return`q-badge flex inline items-center no-wrap q-badge--${e.multiLine===!0?"multi":"single"}-line`+(e.outline===!0?" q-badge--outline":e.color!==void 0?` bg-${e.color}`:"")+(a!==void 0?` text-${a}`:"")+(e.floating===!0?" q-badge--floating":"")+(e.rounded===!0?" q-badge--rounded":"")+(e.transparent===!0?" q-badge--transparent":"")});return()=>r("div",{class:l.value,style:n.value,role:"status","aria-label":e.label},mt(t.default,e.label!==void 0?[e.label]:[]))}}),Ke={dark:{type:Boolean,default:null}};function We(e,t){return s(()=>e.dark===null?t.dark.isActive:e.dark)}var Wg=te({name:"QBanner",props:{...Ke,inlineActions:Boolean,dense:Boolean,rounded:Boolean},setup(e,{slots:t}){const{proxy:{$q:n}}=ve(),l=We(e,n),a=s(()=>"q-banner row items-center"+(e.dense===!0?" q-banner--dense":"")+(l.value===!0?" q-banner--dark q-dark":"")+(e.rounded===!0?" rounded-borders":"")),o=s(()=>`q-banner__actions row items-center justify-end col-${e.inlineActions===!0?"auto":"all"}`);return()=>{const i=[r("div",{class:"q-banner__avatar col-auto row items-center self-start"},Ce(t.avatar)),r("div",{class:"q-banner__content col text-body2"},Ce(t.default))],u=Ce(t.action);return u!==void 0&&i.push(r("div",{class:o.value},u)),r("div",{class:a.value+(e.inlineActions===!1&&u!==void 0?" q-banner--top-padding":""),role:"alert"},i)}}}),Ug=te({name:"QBar",props:{...Ke,dense:Boolean},setup(e,{slots:t}){const{proxy:{$q:n}}=ve(),l=We(e,n),a=s(()=>`q-bar row no-wrap items-center q-bar--${e.dense===!0?"dense":"standard"}  q-bar--${l.value===!0?"dark":"light"}`);return()=>r("div",{class:a.value,role:"toolbar"},Ce(t.default))}}),wu={left:"start",center:"center",right:"end",between:"between",around:"around",evenly:"evenly",stretch:"stretch"},dd=Object.keys(wu),Do={align:{type:String,validator:e=>dd.includes(e)}};function Io(e){return s(()=>{const t=e.align===void 0?e.vertical===!0?"stretch":"left":e.align;return`${e.vertical===!0?"items":"justify"}-${wu[t]}`})}function ul(e){if(Object(e.$parent)===e.$parent)return e.$parent;let{parent:t}=e.$;for(;Object(t)===t;){if(Object(t.proxy)===t.proxy)return t.proxy;t=t.parent}}function _u(e,t){typeof t.type=="symbol"?Array.isArray(t.children)===!0&&t.children.forEach(n=>{_u(e,n)}):e.add(t)}function Ho(e){const t=new Set;return e.forEach(n=>{_u(t,n)}),Array.from(t)}function No(e){return e.appContext.config.globalProperties.$router!==void 0}function on(e){return e.isUnmounted===!0||e.isDeactivated===!0}var fd=["",!0],Yg=te({name:"QBreadcrumbs",props:{...Do,separator:{type:String,default:"/"},separatorColor:String,activeColor:{type:String,default:"primary"},gutter:{type:String,validator:e=>["none","xs","sm","md","lg","xl"].includes(e),default:"sm"}},setup(e,{slots:t}){const n=Io(e),l=s(()=>`flex items-center ${n.value}${e.gutter==="none"?"":` q-gutter-${e.gutter}`}`),a=s(()=>e.separatorColor?` text-${e.separatorColor}`:""),o=s(()=>` text-${e.activeColor}`);return()=>{if(t.default===void 0)return;const i=Ho(Ce(t.default));if(i.length===0)return;let u=1;const d=[],f=i.filter(h=>h.type!==void 0&&h.type.name==="QBreadcrumbsEl").length,c=t.separator!==void 0?t.separator:()=>e.separator;return i.forEach(h=>{if(h.type!==void 0&&h.type.name==="QBreadcrumbsEl"){const m=u<f,v=h.props!==null&&fd.includes(h.props.disable),g=(m===!0?"":" q-breadcrumbs--last")+(v!==!0&&m===!0?o.value:"");u++,d.push(r("div",{class:`flex items-center${g}`},[h])),m===!0&&d.push(r("div",{class:"q-breadcrumbs__separator"+a.value},c()))}else d.push(h)}),r("div",{class:"q-breadcrumbs"},[r("div",{class:l.value},d)])}}});function zr(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}function Or(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function vd(e,t){for(const n in t){const l=t[n],a=e[n];if(typeof l=="string"){if(l!==a)return!1}else if(Array.isArray(a)===!1||a.length!==l.length||l.some((o,i)=>o!==a[i]))return!1}return!0}function Rr(e,t){return Array.isArray(t)===!0?e.length===t.length&&e.every((n,l)=>n===t[l]):e.length===1&&e[0]===t}function md(e,t){return Array.isArray(e)===!0?Rr(e,t):Array.isArray(t)===!0?Rr(t,e):e===t}function gd(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(md(e[n],t[n])===!1)return!1;return!0}var Su={to:[String,Object],replace:Boolean,href:String,target:String,disable:Boolean},Na={...Su,exact:Boolean,activeClass:{type:String,default:"q-router-link--active"},exactActiveClass:{type:String,default:"q-router-link--exact-active"}};function zl({fallbackTag:e,useDisableForRouterLinkProps:t=!0}={}){const n=ve(),{props:l,proxy:a,emit:o}=n,i=No(n),u=s(()=>l.disable!==!0&&l.href!==void 0),d=t===!0?s(()=>i===!0&&l.disable!==!0&&u.value!==!0&&l.to!==void 0&&l.to!==null&&l.to!==""):s(()=>i===!0&&u.value!==!0&&l.to!==void 0&&l.to!==null&&l.to!==""),f=s(()=>d.value===!0?w(l.to):null),c=s(()=>f.value!==null),h=s(()=>u.value===!0||c.value===!0),m=s(()=>l.type==="a"||h.value===!0?"a":l.tag||e||"div"),v=s(()=>u.value===!0?{href:l.href,target:l.target}:c.value===!0?{href:f.value.href,target:l.target}:{}),g=s(()=>{if(c.value===!1)return-1;const{matched:S}=f.value,{length:B}=S,L=S[B-1];if(L===void 0)return-1;const R=a.$route.matched;if(R.length===0)return-1;const A=R.findIndex(Or.bind(null,L));if(A!==-1)return A;const P=zr(S[B-2]);return B>1&&zr(L)===P&&R[R.length-1].path!==P?R.findIndex(Or.bind(null,S[B-2])):A}),_=s(()=>c.value===!0&&g.value!==-1&&vd(a.$route.params,f.value.params)),p=s(()=>_.value===!0&&g.value===a.$route.matched.length-1&&gd(a.$route.params,f.value.params)),k=s(()=>c.value===!0?p.value===!0?` ${l.exactActiveClass} ${l.activeClass}`:l.exact===!0?"":_.value===!0?` ${l.activeClass}`:"":"");function w(S){try{return a.$router.resolve(S)}catch{}return null}function y(S,{returnRouterError:B,to:L=l.to,replace:R=l.replace}={}){if(l.disable===!0)return S.preventDefault(),Promise.resolve(!1);if(S.metaKey||S.altKey||S.ctrlKey||S.shiftKey||S.button!==void 0&&S.button!==0||l.target==="_blank")return Promise.resolve(!1);S.preventDefault();const A=a.$router[R===!0?"replace":"push"](L);return B===!0?A:A.then(()=>{}).catch(()=>{})}function b(S){if(c.value===!0){const B=L=>y(S,L);o("click",S,B),S.defaultPrevented!==!0&&B()}else o("click",S)}return{hasRouterLink:c,hasHrefLink:u,hasLink:h,linkTag:m,resolvedLink:f,linkIsActive:_,linkIsExactActive:p,linkClass:k,linkAttrs:v,getLink:w,navigateToRouterLink:y,navigateOnClick:b}}var Xg=te({name:"QBreadcrumbsEl",props:{...Na,label:String,icon:String,tag:{type:String,default:"span"}},emits:["click"],setup(e,{slots:t}){const{linkTag:n,linkAttrs:l,linkClass:a,navigateOnClick:o}=zl(),i=s(()=>({class:"q-breadcrumbs__el q-link flex inline items-center relative-position "+(e.disable!==!0?"q-link--focusable"+a.value:"q-breadcrumbs__el--disable"),...l.value,onClick:o})),u=s(()=>"q-breadcrumbs__el-icon"+(e.label!==void 0?" q-breadcrumbs__el-icon--with-label":""));return()=>{const d=[];return e.icon!==void 0&&d.push(r(je,{class:u.value,name:e.icon})),e.label!==void 0&&d.push(e.label),r(n.value,{...i.value},mt(t.default,d))}}}),gt={size:{type:[String,Number],default:"1em"},color:String};function ht(e){return{cSize:s(()=>e.size in po?`${po[e.size]}px`:e.size),classes:s(()=>"q-spinner"+(e.color?` text-${e.color}`:""))}}var It=te({name:"QSpinner",props:{...gt,thickness:{type:Number,default:5}},setup(e){const{cSize:t,classes:n}=ht(e);return()=>r("svg",{class:n.value+" q-spinner-mat",width:t.value,height:t.value,viewBox:"25 25 50 50"},[r("circle",{class:"path",cx:"50",cy:"50",r:"20",fill:"none",stroke:"currentColor","stroke-width":e.thickness,"stroke-miterlimit":"10"})])}});function za(e){if(e===window)return{top:0,left:0};const{top:t,left:n}=e.getBoundingClientRect();return{top:t,left:n}}function hd(e,t){return window.getComputedStyle(e).getPropertyValue(t)}function Fn(e){return e===window?window.innerHeight:e.getBoundingClientRect().height}function bd(e){return e===window?window.innerWidth:e.getBoundingClientRect().width}function Oa(e,t){const n=e.style;for(const l in t)n[l]=t[l]}function yd(e,t){e.forEach(n=>Oa(n,t))}function pd(e){if(typeof e=="function"){if(document.readyState!=="loading")return e();document.addEventListener("DOMContentLoaded",e,!1)}}function wd(e){if(e==null)return;if(typeof e=="string")try{return document.querySelector(e)||void 0}catch{return}const t=kc(e);if(t)return t.$el||t}function xu(e,t){if(e==null||e.contains(t)===!0)return!0;for(let n=e.nextElementSibling;n!==null;n=n.nextElementSibling)if(n.contains(t))return!0;return!1}var Gg={offset:za,style:hd,height:Fn,width:bd,css:Oa,cssBatch:yd,ready:pd};function Cu(e,t=250){let n=!1,l;return function(){return n===!1&&(n=!0,setTimeout(()=>{n=!1},t),l=e.apply(this,arguments)),l}}function Fr(e,t,n,l){n.modifiers.stop===!0&&dt(e);const a=n.modifiers.color;let o=n.modifiers.center;o=o===!0||l===!0;const i=document.createElement("span"),u=document.createElement("span"),d=$t(e),{left:f,top:c,width:h,height:m}=t.getBoundingClientRect(),v=Math.sqrt(h*h+m*m),g=v/2,_=`${(h-v)/2}px`,p=o?_:`${d.left-f-g}px`,k=`${(m-v)/2}px`,w=o?k:`${d.top-c-g}px`;u.className="q-ripple__inner",Oa(u,{height:`${v}px`,width:`${v}px`,transform:`translate3d(${p},${w},0) scale3d(.2,.2,1)`,opacity:0}),i.className=`q-ripple${a?" text-"+a:""}`,i.setAttribute("dir","ltr"),i.appendChild(u),t.appendChild(i);const y=()=>{i.remove(),clearTimeout(b)};n.abort.push(y);let b=setTimeout(()=>{u.classList.add("q-ripple__inner--enter"),u.style.transform=`translate3d(${_},${k},0) scale3d(1,1,1)`,u.style.opacity=.2,b=setTimeout(()=>{u.classList.remove("q-ripple__inner--enter"),u.classList.add("q-ripple__inner--leave"),u.style.opacity=0,b=setTimeout(()=>{i.remove(),n.abort.splice(n.abort.indexOf(y),1)},275)},250)},50)}function Vr(e,{modifiers:t,value:n,arg:l}){const a=Object.assign({},e.cfg.ripple,t,n);e.modifiers={early:a.early===!0,stop:a.stop===!0,center:a.center===!0,color:a.color||l,keyCodes:[].concat(a.keyCodes||13)}}var Ol=Xt({name:"ripple",beforeMount(e,t){const n=t.instance.$.appContext.config.globalProperties.$q.config||{};if(n.ripple===!1)return;const l={cfg:n,enabled:t.value!==!1,modifiers:{},abort:[],start(a){l.enabled===!0&&a.qSkipRipple!==!0&&a.type===(l.modifiers.early===!0?"pointerdown":"click")&&Fr(a,e,l,a.qKeyEvent===!0)},keystart:Cu(a=>{l.enabled===!0&&a.qSkipRipple!==!0&&Vt(a,l.modifiers.keyCodes)===!0&&a.type===`key${l.modifiers.early===!0?"down":"up"}`&&Fr(a,e,l,!0)},300)};Vr(l,t),e.__qripple=l,vt(l,"main",[[e,"pointerdown","start","passive"],[e,"click","start","passive"],[e,"keydown","keystart","passive"],[e,"keyup","keystart","passive"]])},updated(e,t){if(t.oldValue!==t.value){const n=e.__qripple;n!==void 0&&(n.enabled=t.value!==!1,n.enabled===!0&&Object(t.value)===t.value&&Vr(n,t))}},beforeUnmount(e){const t=e.__qripple;t!==void 0&&(t.abort.forEach(n=>{n()}),Mt(t,"main"),delete e._qripple)}}),yl={none:0,xs:4,sm:8,md:16,lg:24,xl:32},_d={xs:8,sm:10,md:14,lg:20,xl:24},Sd=["button","submit","reset"],xd=/[^\s]\/[^\s]/,ku=["flat","outline","push","unelevated"];function Qo(e,t){return e.flat===!0?"flat":e.outline===!0?"outline":e.push===!0?"push":e.unelevated===!0?"unelevated":t}function qu(e){const t=Qo(e);return t!==void 0?{[t]:!0}:{}}var jo={...sn,...Su,type:{type:String,default:"button"},label:[Number,String],icon:String,iconRight:String,...ku.reduce((e,t)=>(e[t]=Boolean)&&e,{}),square:Boolean,rounded:Boolean,glossy:Boolean,size:String,fab:Boolean,fabMini:Boolean,padding:String,color:String,textColor:String,noCaps:Boolean,noWrap:Boolean,dense:Boolean,tabindex:[Number,String],ripple:{type:[Boolean,Object],default:!0},align:{...Do.align,default:"center"},stack:Boolean,stretch:Boolean,loading:{type:Boolean,default:null},disable:Boolean},Cd={...jo,round:Boolean};function kd(e){const t=cn(e,_d),n=Io(e),{hasRouterLink:l,hasLink:a,linkTag:o,linkAttrs:i,navigateOnClick:u}=zl({fallbackTag:"button"}),d=s(()=>{const p=e.fab===!1&&e.fabMini===!1?t.value:{};return e.padding!==void 0?Object.assign({},p,{padding:e.padding.split(/\s+/).map(k=>k in yl?yl[k]+"px":k).join(" "),minWidth:"0",minHeight:"0"}):p}),f=s(()=>e.rounded===!0||e.fab===!0||e.fabMini===!0),c=s(()=>e.disable!==!0&&e.loading!==!0),h=s(()=>c.value===!0?e.tabindex||0:-1),m=s(()=>Qo(e,"standard")),v=s(()=>{const p={tabindex:h.value};return a.value===!0?Object.assign(p,i.value):Sd.includes(e.type)===!0&&(p.type=e.type),o.value==="a"?(e.disable===!0?p["aria-disabled"]="true":p.href===void 0&&(p.role="button"),l.value!==!0&&xd.test(e.type)===!0&&(p.type=e.type)):e.disable===!0&&(p.disabled="",p["aria-disabled"]="true"),e.loading===!0&&e.percentage!==void 0&&Object.assign(p,{role:"progressbar","aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":e.percentage}),p}),g=s(()=>{let p;e.color!==void 0?e.flat===!0||e.outline===!0?p=`text-${e.textColor||e.color}`:p=`bg-${e.color} text-${e.textColor||"white"}`:e.textColor&&(p=`text-${e.textColor}`);const k=e.round===!0?"round":`rectangle${f.value===!0?" q-btn--rounded":e.square===!0?" q-btn--square":""}`;return`q-btn--${m.value} q-btn--${k}`+(p!==void 0?" "+p:"")+(c.value===!0?" q-btn--actionable q-focusable q-hoverable":e.disable===!0?" disabled":"")+(e.fab===!0?" q-btn--fab":e.fabMini===!0?" q-btn--fab-mini":"")+(e.noCaps===!0?" q-btn--no-uppercase":"")+(e.dense===!0?" q-btn--dense":"")+(e.stretch===!0?" no-border-radius self-stretch":"")+(e.glossy===!0?" glossy":"")+(e.square?" q-btn--square":"")}),_=s(()=>n.value+(e.stack===!0?" column":" row")+(e.noWrap===!0?" no-wrap text-no-wrap":"")+(e.loading===!0?" q-btn__content--hidden":""));return{classes:g,style:d,innerClasses:_,attributes:v,hasLink:a,linkTag:o,navigateOnClick:u,isActionable:c}}var{passiveCapture:Ot}=et,Kn=null,Wn=null,Un=null,Xe=te({name:"QBtn",props:{...Cd,percentage:Number,darkPercentage:Boolean,onTouchstart:[Function,Array]},emits:["click","keydown","mousedown","keyup"],setup(e,{slots:t,emit:n}){const{proxy:l}=ve(),{classes:a,style:o,innerClasses:i,attributes:u,hasLink:d,linkTag:f,navigateOnClick:c,isActionable:h}=kd(e),m=O(null),v=O(null);let g=null,_,p=null;const k=s(()=>e.label!==void 0&&e.label!==null&&e.label!==""),w=s(()=>e.disable===!0||e.ripple===!1?!1:{keyCodes:d.value===!0?[13,32]:[13],...e.ripple===!0?{}:e.ripple}),y=s(()=>({center:e.round})),b=s(()=>{const q=Math.max(0,Math.min(100,e.percentage));return q>0?{transition:"transform 0.6s",transform:`translateX(${q-100}%)`}:{}}),S=s(()=>{if(e.loading===!0)return{onMousedown:x,onTouchstart:x,onClick:x,onKeydown:x,onKeyup:x};if(h.value===!0){const q={onClick:L,onKeydown:R,onMousedown:P};if(l.$q.platform.has.touch===!0){const U=e.onTouchstart!==void 0?"":"Passive";q[`onTouchstart${U}`]=A}return q}return{onClick:Fe}}),B=s(()=>({ref:m,class:"q-btn q-btn-item non-selectable no-outline "+a.value,style:o.value,...u.value,...S.value}));function L(q){if(m.value!==null){if(q!==void 0){if(q.defaultPrevented===!0)return;const U=document.activeElement;if(e.type==="submit"&&U!==document.body&&m.value.contains(U)===!1&&U.contains(m.value)===!1){m.value.focus();const W=()=>{document.removeEventListener("keydown",Fe,!0),document.removeEventListener("keyup",W,Ot),m.value!==null&&m.value.removeEventListener("blur",W,Ot)};document.addEventListener("keydown",Fe,!0),document.addEventListener("keyup",W,Ot),m.value.addEventListener("blur",W,Ot)}}c(q)}}function R(q){m.value!==null&&(n("keydown",q),Vt(q,[13,32])===!0&&Wn!==m.value&&(Wn!==null&&T(),q.defaultPrevented!==!0&&(m.value.focus(),Wn=m.value,m.value.classList.add("q-btn--active"),document.addEventListener("keyup",z,!0),m.value.addEventListener("blur",z,Ot)),Fe(q)))}function A(q){m.value!==null&&(n("touchstart",q),q.defaultPrevented!==!0&&(Kn!==m.value&&(Kn!==null&&T(),Kn=m.value,g=q.target,g.addEventListener("touchcancel",z,Ot),g.addEventListener("touchend",z,Ot)),_=!0,p!==null&&clearTimeout(p),p=setTimeout(()=>{p=null,_=!1},200)))}function P(q){m.value!==null&&(q.qSkipRipple=_===!0,n("mousedown",q),q.defaultPrevented!==!0&&Un!==m.value&&(Un!==null&&T(),Un=m.value,m.value.classList.add("q-btn--active"),document.addEventListener("mouseup",z,Ot)))}function z(q){if(m.value!==null&&!(q!==void 0&&q.type==="blur"&&document.activeElement===m.value)){if(q!==void 0&&q.type==="keyup"){if(Wn===m.value&&Vt(q,[13,32])===!0){const U=new MouseEvent("click",q);U.qKeyEvent=!0,q.defaultPrevented===!0&&wt(U),q.cancelBubble===!0&&dt(U),m.value.dispatchEvent(U),Fe(q),q.qKeyEvent=!0}n("keyup",q)}T()}}function T(q){const U=v.value;q!==!0&&(Kn===m.value||Un===m.value)&&U!==null&&U!==document.activeElement&&(U.setAttribute("tabindex",-1),U.focus()),Kn===m.value&&(g!==null&&(g.removeEventListener("touchcancel",z,Ot),g.removeEventListener("touchend",z,Ot)),Kn=g=null),Un===m.value&&(document.removeEventListener("mouseup",z,Ot),Un=null),Wn===m.value&&(document.removeEventListener("keyup",z,!0),m.value!==null&&m.value.removeEventListener("blur",z,Ot),Wn=null),m.value!==null&&m.value.classList.remove("q-btn--active")}function x(q){Fe(q),q.qSkipRipple=!0}return Ne(()=>{T(!0)}),Object.assign(l,{click:q=>{h.value===!0&&L(q)}}),()=>{let q=[];e.icon!==void 0&&q.push(r(je,{name:e.icon,left:e.stack!==!0&&k.value===!0,role:"img"})),k.value===!0&&q.push(r("span",{class:"block"},[e.label])),q=mt(t.default,q),e.iconRight!==void 0&&e.round===!1&&q.push(r(je,{name:e.iconRight,right:e.stack!==!0&&k.value===!0,role:"img"}));const U=[r("span",{class:"q-focus-helper",ref:v})];return e.loading===!0&&e.percentage!==void 0&&U.push(r("span",{class:"q-btn__progress absolute-full overflow-hidden"+(e.darkPercentage===!0?" q-btn__progress--dark":"")},[r("span",{class:"q-btn__progress-indicator fit block",style:b.value})])),U.push(r("span",{class:"q-btn__content text-center col items-center q-anchor--skip "+i.value},q)),e.loading!==null&&U.push(r(_t,{name:"q-transition--fade"},()=>e.loading===!0?[r("span",{key:"loading",class:"absolute-full flex flex-center"},t.loading!==void 0?t.loading():[r(It)])]:null)),Ut(r(f.value,B.value,U),[[Ol,w.value,void 0,y.value]])}}}),Tu=te({name:"QBtnGroup",props:{unelevated:Boolean,outline:Boolean,flat:Boolean,rounded:Boolean,square:Boolean,push:Boolean,stretch:Boolean,glossy:Boolean,spread:Boolean},setup(e,{slots:t}){const n=s(()=>{const l=["unelevated","outline","flat","rounded","square","push","stretch","glossy"].filter(a=>e[a]===!0).map(a=>`q-btn-group--${a}`).join(" ");return`q-btn-group row no-wrap${l.length!==0?" "+l:""}`+(e.spread===!0?" q-btn-group--spread":" inline")});return()=>r("div",{class:n.value},Ce(t.default))}});function Wt(){if(window.getSelection!==void 0){const e=window.getSelection();e.empty!==void 0?e.empty():e.removeAllRanges!==void 0&&(e.removeAllRanges(),Ea.is.mobile!==!0&&e.addRange(document.createRange()))}else document.selection!==void 0&&document.selection.empty()}var Mu={target:{type:[Boolean,String,Element],default:!0},noParentEvent:Boolean},$u={...Mu,contextMenu:Boolean};function Ko({showing:e,avoidEmit:t,configureAnchorEl:n}){const{props:l,proxy:a,emit:o}=ve(),i=O(null);let u=null;function d(v){return i.value===null?!1:v===void 0||v.touches===void 0||v.touches.length<=1}const f={};n===void 0&&(Object.assign(f,{hide(v){a.hide(v)},toggle(v){a.toggle(v),v.qAnchorHandled=!0},toggleKey(v){Vt(v,13)===!0&&f.toggle(v)},contextClick(v){a.hide(v),wt(v),Qe(()=>{a.show(v),v.qAnchorHandled=!0})},prevent:wt,mobileTouch(v){if(f.mobileCleanup(v),d(v)!==!0)return;a.hide(v),i.value.classList.add("non-selectable");const g=v.target;vt(f,"anchor",[[g,"touchmove","mobileCleanup","passive"],[g,"touchend","mobileCleanup","passive"],[g,"touchcancel","mobileCleanup","passive"],[i.value,"contextmenu","prevent","notPassive"]]),u=setTimeout(()=>{u=null,a.show(v),v.qAnchorHandled=!0},300)},mobileCleanup(v){i.value.classList.remove("non-selectable"),u!==null&&(clearTimeout(u),u=null),e.value===!0&&v!==void 0&&Wt()}}),n=function(v=l.contextMenu){if(l.noParentEvent===!0||i.value===null)return;let g;v===!0?a.$q.platform.is.mobile===!0?g=[[i.value,"touchstart","mobileTouch","passive"]]:g=[[i.value,"mousedown","hide","passive"],[i.value,"contextmenu","contextClick","notPassive"]]:g=[[i.value,"click","toggle","passive"],[i.value,"keyup","toggleKey","passive"]],vt(f,"anchor",g)});function c(){Mt(f,"anchor")}function h(v){for(i.value=v;i.value.classList.contains("q-anchor--skip");)i.value=i.value.parentNode;n()}function m(){if(l.target===!1||l.target===""||a.$el.parentNode===null)i.value=null;else if(l.target===!0)h(a.$el.parentNode);else{let v=l.target;if(typeof l.target=="string")try{v=document.querySelector(l.target)}catch{v=void 0}v!=null?(i.value=v.$el||v,n()):(i.value=null,console.error(`Anchor: target "${l.target}" not found`))}}return ne(()=>l.contextMenu,v=>{i.value!==null&&(c(),n(v))}),ne(()=>l.target,()=>{i.value!==null&&c(),m()}),ne(()=>l.noParentEvent,v=>{i.value!==null&&(v===!0?c():n())}),ft(()=>{m(),t!==!0&&l.modelValue===!0&&i.value===null&&o("update:modelValue",!1)}),Ne(()=>{u!==null&&clearTimeout(u),c()}),{anchorEl:i,canShow:d,anchorEvents:f}}function Bu(e,t){const n=O(null);let l;function a(u,d){const f=`${d!==void 0?"add":"remove"}EventListener`,c=d!==void 0?d:l;u!==window&&u[f]("scroll",c,et.passive),window[f]("scroll",c,et.passive),l=d}function o(){n.value!==null&&(a(n.value),n.value=null)}const i=ne(()=>e.noParentEvent,()=>{n.value!==null&&(o(),t())});return Ne(i),{localScrollTarget:n,unconfigureScrollTarget:o,changeScrollEvent:a}}var va={modelValue:{type:Boolean,default:null},"onUpdate:modelValue":[Function,Array]},ma=["beforeShow","show","beforeHide","hide"];function ga({showing:e,canShow:t,hideOnRouteChange:n,handleShow:l,handleHide:a,processOnMount:o}){const i=ve(),{props:u,emit:d,proxy:f}=i;let c;function h(w){e.value===!0?g(w):m(w)}function m(w){if(u.disable===!0||w!==void 0&&w.qAnchorHandled===!0||t!==void 0&&t(w)!==!0)return;const y=u["onUpdate:modelValue"]!==void 0;y===!0&&(d("update:modelValue",!0),c=w,Qe(()=>{c===w&&(c=void 0)})),(u.modelValue===null||y===!1)&&v(w)}function v(w){e.value!==!0&&(e.value=!0,d("beforeShow",w),l!==void 0?l(w):d("show",w))}function g(w){if(u.disable===!0)return;const y=u["onUpdate:modelValue"]!==void 0;y===!0&&(d("update:modelValue",!1),c=w,Qe(()=>{c===w&&(c=void 0)})),(u.modelValue===null||y===!1)&&_(w)}function _(w){e.value!==!1&&(e.value=!1,d("beforeHide",w),a!==void 0?a(w):d("hide",w))}function p(w){u.disable===!0&&w===!0?u["onUpdate:modelValue"]!==void 0&&d("update:modelValue",!1):w===!0!==e.value&&(w===!0?v:_)(c)}ne(()=>u.modelValue,p),n!==void 0&&No(i)===!0&&ne(()=>f.$route.fullPath,()=>{n.value===!0&&e.value===!0&&g()}),o===!0&&ft(()=>{p(u.modelValue)});const k={show:m,hide:g,toggle:h};return Object.assign(f,k),k}var $n=[],Ra=[];function Pu(e){Ra=Ra.filter(t=>t!==e)}function qd(e){Pu(e),Ra.push(e)}function Dr(e){Pu(e),Ra.length===0&&$n.length!==0&&($n[$n.length-1](),$n=[])}function ha(e){Ra.length===0?e():$n.push(e)}function Td(e){$n=$n.filter(t=>t!==e)}var oa=[],Ta=[],Md=1,vn=document.body;function Qa(e,t){const n=document.createElement("div");if(n.id=t!==void 0?`q-portal--${t}--${Md++}`:e,gl.globalNodes!==void 0){const l=gl.globalNodes.class;l!==void 0&&(n.className=l)}return vn.appendChild(n),oa.push(n),Ta.push(t),n}function Wo(e){const t=oa.indexOf(e);oa.splice(t,1),Ta.splice(t,1),e.remove()}function $d(e){if(e===vn)return;if(vn=e,vn===document.body||Ta.reduce((n,l)=>l==="dialog"?n+1:n,0)<2){oa.forEach(n=>{n.contains(vn)===!1&&vn.appendChild(n)});return}const t=Ta.lastIndexOf("dialog");for(let n=0;n<oa.length;n++){const l=oa[n];(n===t||Ta[n]!=="dialog")&&l.contains(vn)===!1&&vn.appendChild(l)}}var ra=[];function Bd(e){return ra.find(t=>t.contentEl!==null&&t.contentEl.contains(e))}function Eu(e,t){do{if(e.$options.name==="QMenu"){if(e.hide(t),e.$props.separateClosePopup===!0)return ul(e)}else if(e.__qPortal===!0){const n=ul(e);return n!==void 0&&n.$options.name==="QPopupProxy"?(e.hide(t),n):e}e=ul(e)}while(e!=null)}function Pd(e,t,n){for(;n!==0&&e!==void 0&&e!==null;){if(e.__qPortal===!0){if(n--,e.$options.name==="QMenu"){e=Eu(e,t);continue}e.hide(t)}e=ul(e)}}var Ed=te({name:"QPortal",setup(e,{slots:t}){return()=>t.default()}});function Ld(e){for(e=e.parent;e!=null;){if(e.type.name==="QGlobalDialog")return!0;if(e.type.name==="QDialog"||e.type.name==="QMenu")return!1;e=e.parent}return!1}function Uo(e,t,n,l){const a=O(!1),o=O(!1);let i=null;const u={},d=l==="dialog"&&Ld(e);function f(h){if(h===!0){Dr(u),o.value=!0;return}o.value=!1,a.value===!1&&(d===!1&&i===null&&(i=Qa(!1,l)),a.value=!0,ra.push(e.proxy),qd(u))}function c(h){if(o.value=!1,h!==!0)return;Dr(u),a.value=!1;const m=ra.indexOf(e.proxy);m!==-1&&ra.splice(m,1),i!==null&&(Wo(i),i=null)}return zo(()=>{c(!0)}),e.proxy.__qPortal=!0,St(e.proxy,"contentEl",()=>t.value),{showPortal:f,hidePortal:c,portalIsActive:a,portalIsAccessible:o,renderPortal:()=>d===!0?n():a.value===!0?[r(Sc,{to:i},r(Ed,n))]:void 0}}var En={transitionShow:{type:String,default:"fade"},transitionHide:{type:String,default:"fade"},transitionDuration:{type:[String,Number],default:300}};function Rl(e,t=()=>{},n=()=>{}){return{transitionProps:s(()=>{const l=`q-transition--${e.transitionShow||t()}`,a=`q-transition--${e.transitionHide||n()}`;return{appear:!0,enterFromClass:`${l}-enter-from`,enterActiveClass:`${l}-enter-active`,enterToClass:`${l}-enter-to`,leaveFromClass:`${a}-leave-from`,leaveActiveClass:`${a}-leave-active`,leaveToClass:`${a}-leave-to`}}),transitionStyle:s(()=>`--q-transition-duration: ${e.transitionDuration}ms`)}}function ia(){let e;const t=ve();function n(){e=void 0}return Yt(n),Ne(n),{removeTick:n,registerTick(l){e=l,Qe(()=>{e===l&&(on(t)===!1&&e(),e=void 0)})}}}function hn(){let e=null;const t=ve();function n(){e!==null&&(clearTimeout(e),e=null)}return Yt(n),Ne(n),{removeTimeout:n,registerTimeout(l,a){n(),on(t)===!1&&(e=setTimeout(()=>{e=null,l()},a))}}}var Qn=[Element,String],Ad=[null,document,document.body,document.scrollingElement,document.documentElement];function Gt(e,t){let n=wd(t);if(n===void 0){if(e==null)return window;n=e.closest(".scroll,.scroll-y,.overflow-auto")}return Ad.includes(n)?window:n}function Ca(e){return(e===window?document.body:e).scrollHeight}function zd(e){return(e===window?document.body:e).scrollWidth}function rn(e){return e===window?window.pageYOffset||window.scrollY||document.body.scrollTop||0:e.scrollTop}function ja(e){return e===window?window.pageXOffset||window.scrollX||document.body.scrollLeft||0:e.scrollLeft}function Yo(e,t,n=0){const l=arguments[3]===void 0?performance.now():arguments[3],a=rn(e);if(n<=0){a!==t&&wo(e,t);return}requestAnimationFrame(o=>{const i=o-l,u=a+(t-a)/Math.max(i,n)*i;wo(e,u),u!==t&&Yo(e,t,n-i,o)})}function Xo(e,t,n=0){const l=arguments[3]===void 0?performance.now():arguments[3],a=ja(e);if(n<=0){a!==t&&_o(e,t);return}requestAnimationFrame(o=>{const i=o-l,u=a+(t-a)/Math.max(i,n)*i;_o(e,u),u!==t&&Xo(e,t,n-i,o)})}function wo(e,t){if(e===window){window.scrollTo(window.pageXOffset||window.scrollX||document.body.scrollLeft||0,t);return}e.scrollTop=t}function _o(e,t){if(e===window){window.scrollTo(t,window.pageYOffset||window.scrollY||document.body.scrollTop||0);return}e.scrollLeft=t}function Ln(e,t,n){if(n){Yo(e,t,n);return}wo(e,t)}function sl(e,t,n){if(n){Xo(e,t,n);return}_o(e,t)}var Ua;function Ma(){if(Ua!==void 0)return Ua;const e=document.createElement("p"),t=document.createElement("div");Oa(e,{width:"100%",height:"200px"}),Oa(t,{position:"absolute",top:"0px",left:"0px",visibility:"hidden",width:"200px",height:"150px",overflow:"hidden"}),t.appendChild(e),document.body.appendChild(t);const n=e.offsetWidth;t.style.overflow="scroll";let l=e.offsetWidth;return n===l&&(l=t.clientWidth),t.remove(),Ua=n-l,Ua}function Lu(e,t=!0){return!e||e.nodeType!==Node.ELEMENT_NODE?!1:t?e.scrollHeight>e.clientHeight&&(e.classList.contains("scroll")||e.classList.contains("overflow-auto")||["auto","scroll"].includes(window.getComputedStyle(e)["overflow-y"])):e.scrollWidth>e.clientWidth&&(e.classList.contains("scroll")||e.classList.contains("overflow-auto")||["auto","scroll"].includes(window.getComputedStyle(e)["overflow-x"]))}var Zg={getScrollTarget:Gt,getScrollHeight:Ca,getScrollWidth:zd,getVerticalScrollPosition:rn,getHorizontalScrollPosition:ja,animVerticalScrollTo:Yo,animHorizontalScrollTo:Xo,setVerticalScrollPosition:Ln,setHorizontalScrollPosition:sl,getScrollbarWidth:Ma,hasScrollbar:Lu},An=[],ca;function Od(e){ca=e.keyCode===27}function Rd(){ca===!0&&(ca=!1)}function Fd(e){ca===!0&&(ca=!1,Vt(e,27)===!0&&An[An.length-1](e))}function Au(e){window[e]("keydown",Od),window[e]("blur",Rd),window[e]("keyup",Fd),ca=!1}function zu(e){Ae.is.desktop===!0&&(An.push(e),An.length===1&&Au("addEventListener"))}function pl(e){const t=An.indexOf(e);t!==-1&&(An.splice(t,1),An.length===0&&Au("removeEventListener"))}var zn=[];function Ou(e){zn[zn.length-1](e)}function Go(e){Ae.is.desktop===!0&&(zn.push(e),zn.length===1&&document.body.addEventListener("focusin",Ou))}function wl(e){const t=zn.indexOf(e);t!==-1&&(zn.splice(t,1),zn.length===0&&document.body.removeEventListener("focusin",Ou))}var{notPassiveCapture:_l}=et,On=[];function Sl(e){const t=e.target;if(t===void 0||t.nodeType===8||t.classList.contains("no-pointer-events")===!0)return;let n=ra.length-1;for(;n>=0;){const l=ra[n].$;if(l.type.name==="QTooltip"){n--;continue}if(l.type.name!=="QDialog")break;if(l.props.seamless!==!0)return;n--}for(let l=On.length-1;l>=0;l--){const a=On[l];if((a.anchorEl.value===null||a.anchorEl.value.contains(t)===!1)&&(t===document.body||a.innerRef.value!==null&&a.innerRef.value.contains(t)===!1))e.qClickOutside=!0,a.onClickOutside(e);else return}}function Ru(e){On.push(e),On.length===1&&(document.addEventListener("mousedown",Sl,_l),document.addEventListener("touchstart",Sl,_l))}function xl(e){const t=On.findIndex(n=>n===e);t!==-1&&(On.splice(t,1),On.length===0&&(document.removeEventListener("mousedown",Sl,_l),document.removeEventListener("touchstart",Sl,_l)))}var Ir,Hr;function Cl(e){const t=e.split(" ");return t.length!==2?!1:["top","center","bottom"].includes(t[0])!==!0?(console.error("Anchor/Self position must start with one of top/center/bottom"),!1):["left","middle","right","start","end"].includes(t[1])!==!0?(console.error("Anchor/Self position must end with one of left/middle/right/start/end"),!1):!0}function Fu(e){return e?!(e.length!==2||typeof e[0]!="number"||typeof e[1]!="number"):!0}var So={"start#ltr":"left","start#rtl":"right","end#ltr":"right","end#rtl":"left"};["left","middle","right"].forEach(e=>{So[`${e}#ltr`]=e,So[`${e}#rtl`]=e});function kl(e,t){const n=e.split(" ");return{vertical:n[0],horizontal:So[`${n[1]}#${t===!0?"rtl":"ltr"}`]}}function Vd(e,t){let{top:n,left:l,right:a,bottom:o,width:i,height:u}=e.getBoundingClientRect();return t!==void 0&&(n-=t[1],l-=t[0],o+=t[1],a+=t[0],i+=t[0],u+=t[1]),{top:n,bottom:o,height:u,left:l,right:a,width:i,middle:l+(a-l)/2,center:n+(o-n)/2}}function Dd(e,t,n){let{top:l,left:a}=e.getBoundingClientRect();return l+=t.top,a+=t.left,n!==void 0&&(l+=n[1],a+=n[0]),{top:l,bottom:l+1,height:1,left:a,right:a+1,width:1,middle:a,center:l}}function Id(e,t){return{top:0,center:t/2,bottom:t,left:0,middle:e/2,right:e}}function Nr(e,t,n,l){return{top:e[n.vertical]-t[l.vertical],left:e[n.horizontal]-t[l.horizontal]}}function Zo(e,t=0){if(e.targetEl===null||e.anchorEl===null||t>5)return;if(e.targetEl.offsetHeight===0||e.targetEl.offsetWidth===0){setTimeout(()=>{Zo(e,t+1)},10);return}const{targetEl:n,offset:l,anchorEl:a,anchorOrigin:o,selfOrigin:i,absoluteOffset:u,fit:d,cover:f,maxHeight:c,maxWidth:h}=e;if(Ae.is.ios===!0&&window.visualViewport!==void 0){const B=document.body.style,{offsetLeft:L,offsetTop:R}=window.visualViewport;L!==Ir&&(B.setProperty("--q-pe-left",L+"px"),Ir=L),R!==Hr&&(B.setProperty("--q-pe-top",R+"px"),Hr=R)}const{scrollLeft:m,scrollTop:v}=n,g=u===void 0?Vd(a,f===!0?[0,0]:l):Dd(a,u,l);Object.assign(n.style,{top:0,left:0,minWidth:null,minHeight:null,maxWidth:h||"100vw",maxHeight:c||"100vh",visibility:"visible"});const{offsetWidth:_,offsetHeight:p}=n,{elWidth:k,elHeight:w}=d===!0||f===!0?{elWidth:Math.max(g.width,_),elHeight:f===!0?Math.max(g.height,p):p}:{elWidth:_,elHeight:p};let y={maxWidth:h,maxHeight:c};(d===!0||f===!0)&&(y.minWidth=g.width+"px",f===!0&&(y.minHeight=g.height+"px")),Object.assign(n.style,y);const b=Id(k,w);let S=Nr(g,b,o,i);if(u===void 0||l===void 0)Xl(S,g,b,o,i);else{const{top:B,left:L}=S;Xl(S,g,b,o,i);let R=!1;if(S.top!==B){R=!0;const A=2*l[1];g.center=g.top-=A,g.bottom-=A+2}if(S.left!==L){R=!0;const A=2*l[0];g.middle=g.left-=A,g.right-=A+2}R===!0&&(S=Nr(g,b,o,i),Xl(S,g,b,o,i))}y={top:S.top+"px",left:S.left+"px"},S.maxHeight!==void 0&&(y.maxHeight=S.maxHeight+"px",g.height>S.maxHeight&&(y.minHeight=y.maxHeight)),S.maxWidth!==void 0&&(y.maxWidth=S.maxWidth+"px",g.width>S.maxWidth&&(y.minWidth=y.maxWidth)),Object.assign(n.style,y),n.scrollTop!==v&&(n.scrollTop=v),n.scrollLeft!==m&&(n.scrollLeft=m)}function Xl(e,t,n,l,a){const o=n.bottom,i=n.right,u=Ma(),d=window.innerHeight-u,f=document.body.clientWidth;if(e.top<0||e.top+o>d)if(a.vertical==="center")e.top=t[l.vertical]>d/2?Math.max(0,d-o):0,e.maxHeight=Math.min(o,d);else if(t[l.vertical]>d/2){const c=Math.min(d,l.vertical==="center"?t.center:l.vertical===a.vertical?t.bottom:t.top);e.maxHeight=Math.min(o,c),e.top=Math.max(0,c-o)}else e.top=Math.max(0,l.vertical==="center"?t.center:l.vertical===a.vertical?t.top:t.bottom),e.maxHeight=Math.min(o,d-e.top);if(e.left<0||e.left+i>f)if(e.maxWidth=Math.min(i,f),a.horizontal==="middle")e.left=t[l.horizontal]>f/2?Math.max(0,f-i):0;else if(t[l.horizontal]>f/2){const c=Math.min(f,l.horizontal==="middle"?t.middle:l.horizontal===a.horizontal?t.right:t.left);e.maxWidth=Math.min(i,c),e.left=Math.max(0,c-e.maxWidth)}else e.left=Math.max(0,l.horizontal==="middle"?t.middle:l.horizontal===a.horizontal?t.left:t.right),e.maxWidth=Math.min(i,f-e.left)}var Fl=te({name:"QMenu",inheritAttrs:!1,props:{...$u,...va,...Ke,...En,persistent:Boolean,autoClose:Boolean,separateClosePopup:Boolean,noRouteDismiss:Boolean,noRefocus:Boolean,noFocus:Boolean,fit:Boolean,cover:Boolean,square:Boolean,anchor:{type:String,validator:Cl},self:{type:String,validator:Cl},offset:{type:Array,validator:Fu},scrollTarget:Qn,touchPosition:Boolean,maxHeight:{type:String,default:null},maxWidth:{type:String,default:null}},emits:[...ma,"click","escapeKey"],setup(e,{slots:t,emit:n,attrs:l}){let a=null,o,i,u;const d=ve(),{proxy:f}=d,{$q:c}=f,h=O(null),m=O(!1),v=s(()=>e.persistent!==!0&&e.noRouteDismiss!==!0),g=We(e,c),{registerTick:_,removeTick:p}=ia(),{registerTimeout:k}=hn(),{transitionProps:w,transitionStyle:y}=Rl(e),{localScrollTarget:b,changeScrollEvent:S,unconfigureScrollTarget:B}=Bu(e,K),{anchorEl:L,canShow:R}=Ko({showing:m}),{hide:A}=ga({showing:m,canShow:R,handleShow:ue,handleHide:I,hideOnRouteChange:v,processOnMount:!0}),{showPortal:P,hidePortal:z,renderPortal:T}=Uo(d,h,le,"menu"),x={anchorEl:L,innerRef:h,onClickOutside(H){if(e.persistent!==!0&&m.value===!0)return A(H),(H.type==="touchstart"||H.target.classList.contains("q-dialog__backdrop"))&&Fe(H),!0}},q=s(()=>kl(e.anchor||(e.cover===!0?"center middle":"bottom start"),c.lang.rtl)),U=s(()=>e.cover===!0?q.value:kl(e.self||"top start",c.lang.rtl)),W=s(()=>(e.square===!0?" q-menu--square":"")+(g.value===!0?" q-menu--dark q-dark":"")),E=s(()=>e.autoClose===!0?{onClick:Q}:{}),V=s(()=>m.value===!0&&e.persistent!==!0);ne(V,H=>{H===!0?(zu(M),Ru(x)):(pl(M),xl(x))});function j(){ha(()=>{let H=h.value;H&&H.contains(document.activeElement)!==!0&&(H=H.querySelector("[autofocus][tabindex], [data-autofocus][tabindex]")||H.querySelector("[autofocus] [tabindex], [data-autofocus] [tabindex]")||H.querySelector("[autofocus], [data-autofocus]")||H,H.focus({preventScroll:!0}))})}function ue(H){if(a=e.noRefocus===!1?document.activeElement:null,Go(oe),P(),K(),o=void 0,H!==void 0&&(e.touchPosition||e.contextMenu)){const de=$t(H);if(de.left!==void 0){const{top:xe,left:Y}=L.value.getBoundingClientRect();o={left:de.left-Y,top:de.top-xe}}}i===void 0&&(i=ne(()=>c.screen.width+"|"+c.screen.height+"|"+e.self+"|"+e.anchor+"|"+c.lang.rtl,D)),e.noFocus!==!0&&document.activeElement.blur(),_(()=>{D(),e.noFocus!==!0&&j()}),k(()=>{c.platform.is.ios===!0&&(u=e.autoClose,h.value.click()),D(),P(!0),n("show",H)},e.transitionDuration)}function I(H){p(),z(),C(!0),a!==null&&(H===void 0||H.qClickOutside!==!0)&&(((H&&H.type.indexOf("key")===0?a.closest('[tabindex]:not([tabindex^="-"])'):void 0)||a).focus(),a=null),k(()=>{z(!0),n("hide",H)},e.transitionDuration)}function C(H){o=void 0,i!==void 0&&(i(),i=void 0),(H===!0||m.value===!0)&&(wl(oe),B(),xl(x),pl(M)),H!==!0&&(a=null)}function K(){(L.value!==null||e.scrollTarget!==void 0)&&(b.value=Gt(L.value,e.scrollTarget),S(b.value,D))}function Q(H){u!==!0?(Eu(f,H),n("click",H)):u=!1}function oe(H){V.value===!0&&e.noFocus!==!0&&xu(h.value,H.target)!==!0&&j()}function M(H){n("escapeKey"),A(H)}function D(){Zo({targetEl:h.value,offset:e.offset,anchorEl:L.value,anchorOrigin:q.value,selfOrigin:U.value,absoluteOffset:o,fit:e.fit,cover:e.cover,maxHeight:e.maxHeight,maxWidth:e.maxWidth})}function le(){return r(_t,w.value,()=>m.value===!0?r("div",{role:"menu",...l,ref:h,tabindex:-1,class:["q-menu q-position-engine scroll"+W.value,l.class],style:[l.style,y.value],...E.value},Ce(t.default)):null)}return Ne(C),Object.assign(f,{focus:j,updatePosition:D}),T}}),Gl,Ya=0,kt=new Array(256);for(let e=0;e<256;e++)kt[e]=(e+256).toString(16).substring(1);var Hd=(()=>{const e=typeof crypto!="undefined"?crypto:typeof window!="undefined"?window.crypto||window.msCrypto:void 0;if(e!==void 0){if(e.randomBytes!==void 0)return e.randomBytes;if(e.getRandomValues!==void 0)return t=>{const n=new Uint8Array(t);return e.getRandomValues(n),n}}return t=>{const n=[];for(let l=t;l>0;l--)n.push(Math.floor(Math.random()*256));return n}})(),Qr=4096;function Fa(){(Gl===void 0||Ya+16>Qr)&&(Ya=0,Gl=Hd(Qr));const e=Array.prototype.slice.call(Gl,Ya,Ya+=16);return e[6]=e[6]&15|64,e[8]=e[8]&63|128,kt[e[0]]+kt[e[1]]+kt[e[2]]+kt[e[3]]+"-"+kt[e[4]]+kt[e[5]]+"-"+kt[e[6]]+kt[e[7]]+"-"+kt[e[8]]+kt[e[9]]+"-"+kt[e[10]]+kt[e[11]]+kt[e[12]]+kt[e[13]]+kt[e[14]]+kt[e[15]]}function Nd(e){return e==null?null:e}function jr(e,t){return e==null?t===!0?`f_${Fa()}`:null:e}function Vl({getValue:e,required:t=!0}={}){if(Pt.value===!0){const n=e!==void 0?O(Nd(e())):O(null);return t===!0&&n.value===null&&ft(()=>{n.value=`f_${Fa()}`}),e!==void 0&&ne(e,l=>{n.value=jr(l,t)}),n}return e!==void 0?s(()=>jr(e(),t)):O(`f_${Fa()}`)}var Qd=Object.keys(jo);function jd(e){return Qd.reduce((t,n)=>{const l=e[n];return l!==void 0&&(t[n]=l),t},{})}var Kd=te({name:"QBtnDropdown",props:{...jo,...En,modelValue:Boolean,split:Boolean,dropdownIcon:String,contentClass:[Array,String,Object],contentStyle:[Array,String,Object],cover:Boolean,persistent:Boolean,noRouteDismiss:Boolean,autoClose:Boolean,menuAnchor:{type:String,default:"bottom end"},menuSelf:{type:String,default:"top end"},menuOffset:Array,disableMainBtn:Boolean,disableDropdown:Boolean,noIconAnimation:Boolean,toggleAriaLabel:String},emits:["update:modelValue","click","beforeShow","show","beforeHide","hide"],setup(e,{slots:t,emit:n}){const{proxy:l}=ve(),a=O(e.modelValue),o=O(null),i=Vl(),u=s(()=>{const b={"aria-expanded":a.value===!0?"true":"false","aria-haspopup":"true","aria-controls":i.value,"aria-label":e.toggleAriaLabel||l.$q.lang.label[a.value===!0?"collapse":"expand"](e.label)};return(e.disable===!0||e.split===!1&&e.disableMainBtn===!0||e.disableDropdown===!0)&&(b["aria-disabled"]="true"),b}),d=s(()=>"q-btn-dropdown__arrow"+(a.value===!0&&e.noIconAnimation===!1?" rotate-180":"")+(e.split===!1?" q-btn-dropdown__arrow-container":"")),f=s(()=>qu(e)),c=s(()=>jd(e));ne(()=>e.modelValue,b=>{o.value!==null&&o.value[b?"show":"hide"]()}),ne(()=>e.split,y);function h(b){a.value=!0,n("beforeShow",b)}function m(b){n("show",b),n("update:modelValue",!0)}function v(b){a.value=!1,n("beforeHide",b)}function g(b){n("hide",b),n("update:modelValue",!1)}function _(b){n("click",b)}function p(b){dt(b),y(),n("click",b)}function k(b){o.value!==null&&o.value.toggle(b)}function w(b){o.value!==null&&o.value.show(b)}function y(b){o.value!==null&&o.value.hide(b)}return Object.assign(l,{show:w,hide:y,toggle:k}),ft(()=>{e.modelValue===!0&&w()}),()=>{const b=[r(je,{class:d.value,name:e.dropdownIcon||l.$q.iconSet.arrow.dropdown})];return e.disableDropdown!==!0&&b.push(r(Fl,{ref:o,id:i.value,class:e.contentClass,style:e.contentStyle,cover:e.cover,fit:!0,persistent:e.persistent,noRouteDismiss:e.noRouteDismiss,autoClose:e.autoClose,anchor:e.menuAnchor,self:e.menuSelf,offset:e.menuOffset,separateClosePopup:!0,transitionShow:e.transitionShow,transitionHide:e.transitionHide,transitionDuration:e.transitionDuration,onBeforeShow:h,onShow:m,onBeforeHide:v,onHide:g},t.default)),e.split===!1?r(Xe,{class:"q-btn-dropdown q-btn-dropdown--simple",...c.value,...u.value,disable:e.disable===!0||e.disableMainBtn===!0,noWrap:!0,round:!1,onClick:_},{default:()=>Ce(t.label,[]).concat(b),loading:t.loading}):r(Tu,{class:"q-btn-dropdown q-btn-dropdown--split no-wrap q-btn-item",rounded:e.rounded,square:e.square,...f.value,glossy:e.glossy,stretch:e.stretch},()=>[r(Xe,{class:"q-btn-dropdown--current",...c.value,disable:e.disable===!0||e.disableMainBtn===!0,noWrap:!0,round:!1,onClick:p},{default:t.label,loading:t.loading}),r(Xe,{class:"q-btn-dropdown__arrow-container q-anchor--skip",...u.value,...f.value,disable:e.disable===!0||e.disableDropdown===!0,rounded:e.rounded,color:e.color,textColor:e.textColor,dense:e.dense,size:e.size,padding:e.padding,ripple:e.ripple},()=>b)])}}}),Ht={name:String};function Ka(e){return s(()=>({type:"hidden",name:e.name,value:e.modelValue}))}function wn(e={}){return(t,n,l)=>{t[n](r("input",{class:"hidden"+(l||""),...e.value}))}}function Jo(e){return s(()=>e.name||e.for)}var Jg=te({name:"QBtnToggle",props:{...Ht,modelValue:{required:!0},options:{type:Array,required:!0,validator:e=>e.every(t=>("label"in t||"icon"in t||"slot"in t)&&"value"in t)},color:String,textColor:String,toggleColor:{type:String,default:"primary"},toggleTextColor:String,outline:Boolean,flat:Boolean,unelevated:Boolean,rounded:Boolean,push:Boolean,glossy:Boolean,size:String,padding:String,noCaps:Boolean,noWrap:Boolean,dense:Boolean,readonly:Boolean,disable:Boolean,stack:Boolean,stretch:Boolean,spread:Boolean,clearable:Boolean,ripple:{type:[Boolean,Object],default:!0}},emits:["update:modelValue","clear","click"],setup(e,{slots:t,emit:n}){const l=s(()=>e.options.find(m=>m.value===e.modelValue)!==void 0),a=s(()=>({type:"hidden",name:e.name,value:e.modelValue})),o=wn(a),i=s(()=>qu(e)),u=s(()=>({rounded:e.rounded,dense:e.dense,...i.value})),d=s(()=>e.options.map((m,v)=>{const{attrs:g,value:_,slot:p,...k}=m;return{slot:p,props:{key:v,"aria-pressed":_===e.modelValue?"true":"false",...g,...k,...u.value,disable:e.disable===!0||k.disable===!0,color:_===e.modelValue?c(k,"toggleColor"):c(k,"color"),textColor:_===e.modelValue?c(k,"toggleTextColor"):c(k,"textColor"),noCaps:c(k,"noCaps")===!0,noWrap:c(k,"noWrap")===!0,size:c(k,"size"),padding:c(k,"padding"),ripple:c(k,"ripple"),stack:c(k,"stack")===!0,stretch:c(k,"stretch")===!0,onClick(w){f(_,m,w)}}}}));function f(m,v,g){e.readonly!==!0&&(e.modelValue===m?e.clearable===!0&&(n("update:modelValue",null,null),n("clear")):n("update:modelValue",m,v),n("click",g))}function c(m,v){return m[v]===void 0?e[v]:m[v]}function h(){const m=d.value.map(v=>r(Xe,v.props,v.slot!==void 0?t[v.slot]:void 0));return e.name!==void 0&&e.disable!==!0&&l.value===!0&&o(m,"push"),mt(t.default,m)}return()=>r(Tu,{class:"q-btn-toggle",...i.value,rounded:e.rounded,stretch:e.stretch,glossy:e.glossy,spread:e.spread},h)}}),Vu=te({name:"QCard",props:{...Ke,tag:{type:String,default:"div"},square:Boolean,flat:Boolean,bordered:Boolean},setup(e,{slots:t}){const{proxy:{$q:n}}=ve(),l=We(e,n),a=s(()=>"q-card"+(l.value===!0?" q-card--dark q-dark":"")+(e.bordered===!0?" q-card--bordered":"")+(e.square===!0?" q-card--square no-border-radius":"")+(e.flat===!0?" q-card--flat no-shadow":""));return()=>r(e.tag,{class:a.value},Ce(t.default))}}),Mn=te({name:"QCardSection",props:{tag:{type:String,default:"div"},horizontal:Boolean},setup(e,{slots:t}){const n=s(()=>`q-card__section q-card__section--${e.horizontal===!0?"horiz row no-wrap":"vert"}`);return()=>r(e.tag,{class:n.value},Ce(t.default))}}),Wd=te({name:"QCardActions",props:{...Do,vertical:Boolean},setup(e,{slots:t}){const n=Io(e),l=s(()=>`q-card__actions ${n.value} q-card__actions--${e.vertical===!0?"vert column":"horiz row"}`);return()=>r("div",{class:l.value},Ce(t.default))}}),er={left:!0,right:!0,up:!0,down:!0,horizontal:!0,vertical:!0},Ud=Object.keys(er);er.all=!0;function ql(e){const t={};for(const n of Ud)e[n]===!0&&(t[n]=!0);return Object.keys(t).length===0?er:(t.horizontal===!0?t.left=t.right=!0:t.left===!0&&t.right===!0&&(t.horizontal=!0),t.vertical===!0?t.up=t.down=!0:t.up===!0&&t.down===!0&&(t.vertical=!0),t.horizontal===!0&&t.vertical===!0&&(t.all=!0),t)}var Yd=["INPUT","TEXTAREA"];function Tl(e,t){return t.event===void 0&&e.target!==void 0&&e.target.draggable!==!0&&typeof t.handler=="function"&&Yd.includes(e.target.nodeName.toUpperCase())===!1&&(e.qClonedBy===void 0||e.qClonedBy.indexOf(t.uid)===-1)}function Xd(e){const t=[.06,6,50];return typeof e=="string"&&e.length&&e.split(":").forEach((n,l)=>{const a=parseFloat(n);a&&(t[l]=a)}),t}var Gd=Xt({name:"touch-swipe",beforeMount(e,{value:t,arg:n,modifiers:l}){if(l.mouse!==!0&&Ae.has.touch!==!0)return;const a=l.mouseCapture===!0?"Capture":"",o={handler:t,sensitivity:Xd(n),direction:ql(l),noop:it,mouseStart(i){Tl(i,o)&&Ia(i)&&(vt(o,"temp",[[document,"mousemove","move",`notPassive${a}`],[document,"mouseup","end","notPassiveCapture"]]),o.start(i,!0))},touchStart(i){if(Tl(i,o)){const u=i.target;vt(o,"temp",[[u,"touchmove","move","notPassiveCapture"],[u,"touchcancel","end","notPassiveCapture"],[u,"touchend","end","notPassiveCapture"]]),o.start(i)}},start(i,u){Ae.is.firefox===!0&&Pn(e,!0);const d=$t(i);o.event={x:d.left,y:d.top,time:Date.now(),mouse:u===!0,dir:!1}},move(i){if(o.event===void 0)return;if(o.event.dir!==!1){Fe(i);return}const u=Date.now()-o.event.time;if(u===0)return;const d=$t(i),f=d.left-o.event.x,c=Math.abs(f),h=d.top-o.event.y,m=Math.abs(h);if(o.event.mouse!==!0){if(c<o.sensitivity[1]&&m<o.sensitivity[1]){o.end(i);return}}else if(window.getSelection().toString()!==""){o.end(i);return}else if(c<o.sensitivity[2]&&m<o.sensitivity[2])return;const v=c/u,g=m/u;o.direction.vertical===!0&&c<m&&c<100&&g>o.sensitivity[0]&&(o.event.dir=h<0?"up":"down"),o.direction.horizontal===!0&&c>m&&m<100&&v>o.sensitivity[0]&&(o.event.dir=f<0?"left":"right"),o.direction.up===!0&&c<m&&h<0&&c<100&&g>o.sensitivity[0]&&(o.event.dir="up"),o.direction.down===!0&&c<m&&h>0&&c<100&&g>o.sensitivity[0]&&(o.event.dir="down"),o.direction.left===!0&&c>m&&f<0&&m<100&&v>o.sensitivity[0]&&(o.event.dir="left"),o.direction.right===!0&&c>m&&f>0&&m<100&&v>o.sensitivity[0]&&(o.event.dir="right"),o.event.dir!==!1?(Fe(i),o.event.mouse===!0&&(document.body.classList.add("no-pointer-events--children"),document.body.classList.add("non-selectable"),Wt(),o.styleCleanup=_=>{o.styleCleanup=void 0,document.body.classList.remove("non-selectable");const p=()=>{document.body.classList.remove("no-pointer-events--children")};_===!0?setTimeout(p,50):p()}),o.handler({evt:i,touch:o.event.mouse!==!0,mouse:o.event.mouse,direction:o.event.dir,duration:u,distance:{x:c,y:m}})):o.end(i)},end(i){o.event!==void 0&&(Mt(o,"temp"),Ae.is.firefox===!0&&Pn(e,!1),o.styleCleanup!==void 0&&o.styleCleanup(!0),i!==void 0&&o.event.dir!==!1&&Fe(i),o.event=void 0)}};if(e.__qtouchswipe=o,l.mouse===!0){const i=l.mouseCapture===!0||l.mousecapture===!0?"Capture":"";vt(o,"main",[[e,"mousedown","mouseStart",`passive${i}`]])}Ae.has.touch===!0&&vt(o,"main",[[e,"touchstart","touchStart",`passive${l.capture===!0?"Capture":""}`],[e,"touchmove","noop","notPassiveCapture"]])},updated(e,t){const n=e.__qtouchswipe;n!==void 0&&(t.oldValue!==t.value&&(typeof t.value!="function"&&n.end(),n.handler=t.value),n.direction=ql(t.modifiers))},beforeUnmount(e){const t=e.__qtouchswipe;t!==void 0&&(Mt(t,"main"),Mt(t,"temp"),Ae.is.firefox===!0&&Pn(e,!1),t.styleCleanup!==void 0&&t.styleCleanup(),delete e.__qtouchswipe)}});function Wa(){let e=Object.create(null);return{getCache:(t,n)=>e[t]===void 0?e[t]=typeof n=="function"?n():n:e[t],setCache(t,n){e[t]=n},hasCache(t){return Object.hasOwnProperty.call(e,t)},clearCache(t){t!==void 0?delete e[t]:e=Object.create(null)}}}var tr={name:{required:!0},disable:Boolean},Kr={setup(e,{slots:t}){return()=>r("div",{class:"q-panel scroll",role:"tabpanel"},Ce(t.default))}},nr={modelValue:{required:!0},animated:Boolean,infinite:Boolean,swipeable:Boolean,vertical:Boolean,transitionPrev:String,transitionNext:String,transitionDuration:{type:[String,Number],default:300},keepAlive:Boolean,keepAliveInclude:[String,Array,RegExp],keepAliveExclude:[String,Array,RegExp],keepAliveMax:Number},ar=["update:modelValue","beforeTransition","transition"];function lr(){const{props:e,emit:t,proxy:n}=ve(),{getCache:l}=Wa();let a,o;const i=O(null),u=O(null);function d(x){const q=e.vertical===!0?"up":"left";L((n.$q.lang.rtl===!0?-1:1)*(x.direction===q?1:-1))}const f=s(()=>[[Gd,d,void 0,{horizontal:e.vertical!==!0,vertical:e.vertical,mouse:!0}]]),c=s(()=>e.transitionPrev||`slide-${e.vertical===!0?"down":"right"}`),h=s(()=>e.transitionNext||`slide-${e.vertical===!0?"up":"left"}`),m=s(()=>`--q-transition-duration: ${e.transitionDuration}ms`),v=s(()=>typeof e.modelValue=="string"||typeof e.modelValue=="number"?e.modelValue:String(e.modelValue)),g=s(()=>({include:e.keepAliveInclude,exclude:e.keepAliveExclude,max:e.keepAliveMax})),_=s(()=>e.keepAliveInclude!==void 0||e.keepAliveExclude!==void 0);ne(()=>e.modelValue,(x,q)=>{const U=y(x)===!0?b(x):-1;o!==!0&&B(U===-1?0:U<b(q)?-1:1),i.value!==U&&(i.value=U,t("beforeTransition",x,q),Qe(()=>{t("transition",x,q)}))});function p(){L(1)}function k(){L(-1)}function w(x){t("update:modelValue",x)}function y(x){return x!=null&&x!==""}function b(x){return a.findIndex(q=>q.props.name===x&&q.props.disable!==""&&q.props.disable!==!0)}function S(){return a.filter(x=>x.props.disable!==""&&x.props.disable!==!0)}function B(x){const q=x!==0&&e.animated===!0&&i.value!==-1?"q-transition--"+(x===-1?c.value:h.value):null;u.value!==q&&(u.value=q)}function L(x,q=i.value){let U=q+x;for(;U!==-1&&U<a.length;){const W=a[U];if(W!==void 0&&W.props.disable!==""&&W.props.disable!==!0){B(x),o=!0,t("update:modelValue",W.props.name),setTimeout(()=>{o=!1});return}U+=x}e.infinite===!0&&a.length!==0&&q!==-1&&q!==a.length&&L(x,x===-1?a.length:-1)}function R(){const x=b(e.modelValue);return i.value!==x&&(i.value=x),!0}function A(){const x=y(e.modelValue)===!0&&R()&&a[i.value];return e.keepAlive===!0?[r(Gi,g.value,[r(_.value===!0?l(v.value,()=>({...Kr,name:v.value})):Kr,{key:v.value,style:m.value},()=>x)])]:[r("div",{class:"q-panel scroll",style:m.value,key:v.value,role:"tabpanel"},[x])]}function P(){if(a.length!==0)return e.animated===!0?[r(_t,{name:u.value},A)]:A()}function z(x){return a=Ho(Ce(x.default,[])).filter(q=>q.props!==null&&q.props.slot===void 0&&y(q.props.name)===!0),a.length}function T(){return a}return Object.assign(n,{next:p,previous:k,goTo:w}),{panelIndex:i,panelDirectives:f,updatePanelsList:z,updatePanelIndex:R,getPanelContent:P,getEnabledPanels:S,getPanels:T,isValidPanelName:y,keepAliveProps:g,needsUniqueKeepAliveWrapper:_,goToPanelByOffset:L,goToPanel:w,nextPanel:p,previousPanel:k}}var ba=0,or={fullscreen:Boolean,noRouteFullscreenExit:Boolean},rr=["update:fullscreen","fullscreen"];function ir(){const e=ve(),{props:t,emit:n,proxy:l}=e;let a,o,i;const u=O(!1);No(e)===!0&&ne(()=>l.$route.fullPath,()=>{t.noRouteFullscreenExit!==!0&&c()}),ne(()=>t.fullscreen,h=>{u.value!==h&&d()}),ne(u,h=>{n("update:fullscreen",h),n("fullscreen",h)});function d(){u.value===!0?c():f()}function f(){u.value!==!0&&(u.value=!0,i=l.$el.parentNode,i.replaceChild(o,l.$el),document.body.appendChild(l.$el),ba++,ba===1&&document.body.classList.add("q-body--fullscreen-mixin"),a={handler:c},La.add(a))}function c(){u.value===!0&&(a!==void 0&&(La.remove(a),a=void 0),i.replaceChild(l.$el,o),u.value=!1,ba=Math.max(0,ba-1),ba===0&&(document.body.classList.remove("q-body--fullscreen-mixin"),l.$el.scrollIntoView!==void 0&&setTimeout(()=>{l.$el.scrollIntoView()})))}return Ro(()=>{o=document.createElement("span")}),ft(()=>{t.fullscreen===!0&&f()}),Ne(c),Object.assign(l,{toggleFullscreen:d,setFullscreen:f,exitFullscreen:c}),{inFullscreen:u,toggleFullscreen:d}}var Zd=["top","right","bottom","left"],Jd=["regular","flat","outline","push","unelevated"],eh=te({name:"QCarousel",props:{...Ke,...nr,...or,transitionPrev:{type:String,default:"fade"},transitionNext:{type:String,default:"fade"},height:String,padding:Boolean,controlColor:String,controlTextColor:String,controlType:{type:String,validator:e=>Jd.includes(e),default:"flat"},autoplay:[Number,Boolean],arrows:Boolean,prevIcon:String,nextIcon:String,navigation:Boolean,navigationPosition:{type:String,validator:e=>Zd.includes(e)},navigationIcon:String,navigationActiveIcon:String,thumbnails:Boolean},emits:[...rr,...ar],setup(e,{slots:t}){const{proxy:{$q:n}}=ve(),l=We(e,n);let a=null,o;const{updatePanelsList:i,getPanelContent:u,panelDirectives:d,goToPanel:f,previousPanel:c,nextPanel:h,getEnabledPanels:m,panelIndex:v}=lr(),{inFullscreen:g}=ir(),_=s(()=>g.value!==!0&&e.height!==void 0?{height:e.height}:{}),p=s(()=>e.vertical===!0?"vertical":"horizontal"),k=s(()=>e.navigationPosition||(e.vertical===!0?"right":"bottom")),w=s(()=>`q-carousel q-panel-parent q-carousel--with${e.padding===!0?"":"out"}-padding`+(g.value===!0?" fullscreen":"")+(l.value===!0?" q-carousel--dark q-dark":"")+(e.arrows===!0?` q-carousel--arrows-${p.value}`:"")+(e.navigation===!0?` q-carousel--navigation-${k.value}`:"")),y=s(()=>{const P=[e.prevIcon||n.iconSet.carousel[e.vertical===!0?"up":"left"],e.nextIcon||n.iconSet.carousel[e.vertical===!0?"down":"right"]];return e.vertical===!1&&n.lang.rtl===!0?P.reverse():P}),b=s(()=>e.navigationIcon||n.iconSet.carousel.navigationIcon),S=s(()=>e.navigationActiveIcon||b.value),B=s(()=>({color:e.controlColor,textColor:e.controlTextColor,round:!0,[e.controlType]:!0,dense:!0}));ne(()=>e.modelValue,()=>{e.autoplay&&L()}),ne(()=>e.autoplay,P=>{P?L():a!==null&&(clearTimeout(a),a=null)});function L(){const P=Rn(e.autoplay)===!0?Math.abs(e.autoplay):5e3;a!==null&&clearTimeout(a),a=setTimeout(()=>{a=null,P>=0?h():c()},P)}ft(()=>{e.autoplay&&L()}),Ne(()=>{a!==null&&clearTimeout(a)});function R(P,z){return r("div",{class:`q-carousel__control q-carousel__navigation no-wrap absolute flex q-carousel__navigation--${P} q-carousel__navigation--${k.value}`+(e.controlColor!==void 0?` text-${e.controlColor}`:"")},[r("div",{class:"q-carousel__navigation-inner flex flex-center no-wrap"},m().map(z))])}function A(){const P=[];if(e.navigation===!0){const z=t["navigation-icon"]!==void 0?t["navigation-icon"]:x=>r(Xe,{key:"nav"+x.name,class:`q-carousel__navigation-icon q-carousel__navigation-icon--${x.active===!0?"":"in"}active`,...x.btnProps,onClick:x.onClick}),T=o-1;P.push(R("buttons",(x,q)=>{const U=x.props.name,W=v.value===q;return z({index:q,maxIndex:T,name:U,active:W,btnProps:{icon:W===!0?S.value:b.value,size:"sm",...B.value},onClick:()=>{f(U)}})}))}else if(e.thumbnails===!0){const z=e.controlColor!==void 0?` text-${e.controlColor}`:"";P.push(R("thumbnails",T=>{const x=T.props;return r("img",{key:"tmb#"+x.name,class:`q-carousel__thumbnail q-carousel__thumbnail--${x.name===e.modelValue?"":"in"}active`+z,src:x.imgSrc||x["img-src"],onClick:()=>{f(x.name)}})}))}return e.arrows===!0&&v.value>=0&&((e.infinite===!0||v.value>0)&&P.push(r("div",{key:"prev",class:`q-carousel__control q-carousel__arrow q-carousel__prev-arrow q-carousel__prev-arrow--${p.value} absolute flex flex-center`},[r(Xe,{icon:y.value[0],...B.value,onClick:c})])),(e.infinite===!0||v.value<o-1)&&P.push(r("div",{key:"next",class:`q-carousel__control q-carousel__arrow q-carousel__next-arrow q-carousel__next-arrow--${p.value} absolute flex flex-center`},[r(Xe,{icon:y.value[1],...B.value,onClick:h})]))),mt(t.control,P)}return()=>(o=i(t),r("div",{class:w.value,style:_.value},[Dt("div",{class:"q-carousel__slides-container"},u(),"sl-cont",e.swipeable,()=>d.value)].concat(A())))}}),th=te({name:"QCarouselSlide",props:{...tr,imgSrc:String},setup(e,{slots:t}){const n=s(()=>e.imgSrc?{backgroundImage:`url("${e.imgSrc}")`}:{});return()=>r("div",{class:"q-carousel__slide",style:n.value},Ce(t.default))}}),nh=te({name:"QCarouselControl",props:{position:{type:String,default:"bottom-right",validator:e=>["top-right","top-left","bottom-right","bottom-left","top","right","bottom","left"].includes(e)},offset:{type:Array,default:()=>[18,18],validator:e=>e.length===2}},setup(e,{slots:t}){const n=s(()=>`q-carousel__control absolute absolute-${e.position}`),l=s(()=>({margin:`${e.offset[1]}px ${e.offset[0]}px`}));return()=>r("div",{class:n.value,style:l.value},Ce(t.default))}}),ah=te({name:"QChatMessage",props:{sent:Boolean,label:String,bgColor:String,textColor:String,name:String,avatar:String,text:Array,stamp:String,size:String,labelHtml:Boolean,nameHtml:Boolean,textHtml:Boolean,stampHtml:Boolean},setup(e,{slots:t}){const n=s(()=>e.sent===!0?"sent":"received"),l=s(()=>`q-message-text-content q-message-text-content--${n.value}`+(e.textColor!==void 0?` text-${e.textColor}`:"")),a=s(()=>`q-message-text q-message-text--${n.value}`+(e.bgColor!==void 0?` text-${e.bgColor}`:"")),o=s(()=>"q-message-container row items-end no-wrap"+(e.sent===!0?" reverse":"")),i=s(()=>e.size!==void 0?`col-${e.size}`:""),u=s(()=>({msg:e.textHtml===!0?"innerHTML":"textContent",stamp:e.stampHtml===!0?"innerHTML":"textContent",name:e.nameHtml===!0?"innerHTML":"textContent",label:e.labelHtml===!0?"innerHTML":"textContent"}));function d(c){return t.stamp!==void 0?[c,r("div",{class:"q-message-stamp"},t.stamp())]:e.stamp?[c,r("div",{class:"q-message-stamp",[u.value.stamp]:e.stamp})]:[c]}function f(c,h){const m=h===!0?c.length>1?v=>v:v=>r("div",[v]):v=>r("div",{[u.value.msg]:v});return c.map((v,g)=>r("div",{key:g,class:a.value},[r("div",{class:l.value},d(m(v)))]))}return()=>{const c=[];t.avatar!==void 0?c.push(t.avatar()):e.avatar!==void 0&&c.push(r("img",{class:`q-message-avatar q-message-avatar--${n.value}`,src:e.avatar,"aria-hidden":"true"}));const h=[];t.name!==void 0?h.push(r("div",{class:`q-message-name q-message-name--${n.value}`},t.name())):e.name!==void 0&&h.push(r("div",{class:`q-message-name q-message-name--${n.value}`,[u.value.name]:e.name})),t.default!==void 0?h.push(f(Ho(t.default()),!0)):e.text!==void 0&&h.push(f(e.text)),c.push(r("div",{class:i.value},h));const m=[];return t.label!==void 0?m.push(r("div",{class:"q-message-label"},t.label())):e.label!==void 0&&m.push(r("div",{class:"q-message-label",[u.value.label]:e.label})),m.push(r("div",{class:o.value},c)),r("div",{class:`q-message q-message-${n.value}`},m)}}});function Du(e,t){const n=O(null),l=s(()=>e.disable===!0?null:r("span",{ref:n,class:"no-outline",tabindex:-1}));function a(o){const i=t.value;o!==void 0&&o.type.indexOf("key")===0?i!==null&&document.activeElement!==i&&i.contains(document.activeElement)===!0&&i.focus():n.value!==null&&(o===void 0||i!==null&&i.contains(o.target)===!0)&&n.value.focus()}return{refocusTargetEl:l,refocusTarget:a}}var Iu={xs:30,sm:35,md:40,lg:50,xl:60},Hu={...Ke,...sn,...Ht,modelValue:{required:!0,default:null},val:{},trueValue:{default:!0},falseValue:{default:!1},indeterminateValue:{default:null},checkedIcon:String,uncheckedIcon:String,indeterminateIcon:String,toggleOrder:{type:String,validator:e=>e==="tf"||e==="ft"},toggleIndeterminate:Boolean,label:String,leftLabel:Boolean,color:String,keepColor:Boolean,dense:Boolean,disable:Boolean,tabindex:[String,Number]},Nu=["update:modelValue"];function Qu(e,t){const{props:n,slots:l,emit:a,proxy:o}=ve(),{$q:i}=o,u=We(n,i),d=O(null),{refocusTargetEl:f,refocusTarget:c}=Du(n,d),h=cn(n,Iu),m=s(()=>n.val!==void 0&&Array.isArray(n.modelValue)),v=s(()=>{const T=an(n.val);return m.value===!0?n.modelValue.findIndex(x=>an(x)===T):-1}),g=s(()=>m.value===!0?v.value!==-1:an(n.modelValue)===an(n.trueValue)),_=s(()=>m.value===!0?v.value===-1:an(n.modelValue)===an(n.falseValue)),p=s(()=>g.value===!1&&_.value===!1),k=s(()=>n.disable===!0?-1:n.tabindex||0),w=s(()=>`q-${e} cursor-pointer no-outline row inline no-wrap items-center`+(n.disable===!0?" disabled":"")+(u.value===!0?` q-${e}--dark`:"")+(n.dense===!0?` q-${e}--dense`:"")+(n.leftLabel===!0?" reverse":"")),y=s(()=>{const T=g.value===!0?"truthy":_.value===!0?"falsy":"indet",x=n.color!==void 0&&(n.keepColor===!0||(e==="toggle"?g.value===!0:_.value!==!0))?` text-${n.color}`:"";return`q-${e}__inner relative-position non-selectable q-${e}__inner--${T}${x}`}),b=s(()=>{const T={type:"checkbox"};return n.name!==void 0&&Object.assign(T,{".checked":g.value,"^checked":g.value===!0?"checked":void 0,name:n.name,value:m.value===!0?n.val:n.trueValue}),T}),S=wn(b),B=s(()=>{const T={tabindex:k.value,role:e==="toggle"?"switch":"checkbox","aria-label":n.label,"aria-checked":p.value===!0?"mixed":g.value===!0?"true":"false"};return n.disable===!0&&(T["aria-disabled"]="true"),T});function L(T){T!==void 0&&(Fe(T),c(T)),n.disable!==!0&&a("update:modelValue",R(),T)}function R(){if(m.value===!0){if(g.value===!0){const T=n.modelValue.slice();return T.splice(v.value,1),T}return n.modelValue.concat([n.val])}if(g.value===!0){if(n.toggleOrder!=="ft"||n.toggleIndeterminate===!1)return n.falseValue}else if(_.value===!0){if(n.toggleOrder==="ft"||n.toggleIndeterminate===!1)return n.trueValue}else return n.toggleOrder!=="ft"?n.trueValue:n.falseValue;return n.indeterminateValue}function A(T){(T.keyCode===13||T.keyCode===32)&&Fe(T)}function P(T){(T.keyCode===13||T.keyCode===32)&&L(T)}const z=t(g,p);return Object.assign(o,{toggle:L}),()=>{const T=z();n.disable!==!0&&S(T,"unshift",` q-${e}__native absolute q-ma-none q-pa-none`);const x=[r("div",{class:y.value,style:h.value,"aria-hidden":"true"},T)];f.value!==null&&x.push(f.value);const q=n.label!==void 0?mt(l.default,[n.label]):Ce(l.default);return q!==void 0&&x.push(r("div",{class:`q-${e}__label q-anchor--skip`},q)),r("div",{ref:d,class:w.value,...B.value,onClick:L,onKeydown:A,onKeyup:P},x)}}var ef=r("div",{key:"svg",class:"q-checkbox__bg absolute"},[r("svg",{class:"q-checkbox__svg fit absolute-full",viewBox:"0 0 24 24"},[r("path",{class:"q-checkbox__truthy",fill:"none",d:"M1.73,12.91 8.1,19.28 22.79,4.59"}),r("path",{class:"q-checkbox__indet",d:"M4,14H20V10H4"})])]),$a=te({name:"QCheckbox",props:Hu,emits:Nu,setup(e){function t(n,l){const a=s(()=>(n.value===!0?e.checkedIcon:l.value===!0?e.indeterminateIcon:e.uncheckedIcon)||null);return()=>a.value!==null?[r("div",{key:"icon",class:"q-checkbox__icon-container absolute-full flex flex-center no-wrap"},[r(je,{class:"q-checkbox__icon",name:a.value})])]:[ef]}return Qu("checkbox",t)}}),tf={xs:8,sm:10,md:14,lg:20,xl:24},ju=te({name:"QChip",props:{...Ke,...sn,dense:Boolean,icon:String,iconRight:String,iconRemove:String,iconSelected:String,label:[String,Number],color:String,textColor:String,modelValue:{type:Boolean,default:!0},selected:{type:Boolean,default:null},square:Boolean,outline:Boolean,clickable:Boolean,removable:Boolean,removeAriaLabel:String,tabindex:[String,Number],disable:Boolean,ripple:{type:[Boolean,Object],default:!0}},emits:["update:modelValue","update:selected","remove","click"],setup(e,{slots:t,emit:n}){const{proxy:{$q:l}}=ve(),a=We(e,l),o=cn(e,tf),i=s(()=>e.selected===!0||e.icon!==void 0),u=s(()=>e.selected===!0?e.iconSelected||l.iconSet.chip.selected:e.icon),d=s(()=>e.iconRemove||l.iconSet.chip.remove),f=s(()=>e.disable===!1&&(e.clickable===!0||e.selected!==null)),c=s(()=>{const p=e.outline===!0&&e.color||e.textColor;return"q-chip row inline no-wrap items-center"+(e.outline===!1&&e.color!==void 0?` bg-${e.color}`:"")+(p?` text-${p} q-chip--colored`:"")+(e.disable===!0?" disabled":"")+(e.dense===!0?" q-chip--dense":"")+(e.outline===!0?" q-chip--outline":"")+(e.selected===!0?" q-chip--selected":"")+(f.value===!0?" q-chip--clickable cursor-pointer non-selectable q-hoverable":"")+(e.square===!0?" q-chip--square":"")+(a.value===!0?" q-chip--dark q-dark":"")}),h=s(()=>{const p=e.disable===!0?{tabindex:-1,"aria-disabled":"true"}:{tabindex:e.tabindex||0},k={...p,role:"button","aria-hidden":"false","aria-label":e.removeAriaLabel||l.lang.label.remove};return{chip:p,remove:k}});function m(p){p.keyCode===13&&v(p)}function v(p){e.disable||(n("update:selected",!e.selected),n("click",p))}function g(p){(p.keyCode===void 0||p.keyCode===13)&&(Fe(p),e.disable===!1&&(n("update:modelValue",!1),n("remove")))}function _(){const p=[];f.value===!0&&p.push(r("div",{class:"q-focus-helper"})),i.value===!0&&p.push(r(je,{class:"q-chip__icon q-chip__icon--left",name:u.value}));const k=e.label!==void 0?[r("div",{class:"ellipsis"},[e.label])]:void 0;return p.push(r("div",{class:"q-chip__content col row no-wrap items-center q-anchor--skip"},Vo(t.default,k))),e.iconRight&&p.push(r(je,{class:"q-chip__icon q-chip__icon--right",name:e.iconRight})),e.removable===!0&&p.push(r(je,{class:"q-chip__icon q-chip__icon--remove cursor-pointer",name:d.value,...h.value.remove,onClick:g,onKeyup:g})),p}return()=>{if(e.modelValue===!1)return;const p={class:c.value,style:o.value};return f.value===!0&&Object.assign(p,h.value.chip,{onClick:v,onKeyup:m}),Dt("div",p,_(),"ripple",e.ripple!==!1&&e.disable!==!0,()=>[[Ol,e.ripple]])}}}),ur={...sn,min:{type:Number,default:0},max:{type:Number,default:100},color:String,centerColor:String,trackColor:String,fontSize:String,rounded:Boolean,thickness:{type:Number,default:.2,validator:e=>e>=0&&e<=1},angle:{type:Number,default:0},showValue:Boolean,reverse:Boolean,instantFeedback:Boolean},xo=50,Ku=2*xo,Wu=Ku*Math.PI,nf=Math.round(Wu*1e3)/1e3,Uu=te({name:"QCircularProgress",props:{...ur,value:{type:Number,default:0},animationSpeed:{type:[String,Number],default:600},indeterminate:Boolean},setup(e,{slots:t}){const{proxy:{$q:n}}=ve(),l=cn(e),a=s(()=>{const v=(n.lang.rtl===!0?-1:1)*e.angle;return{transform:e.reverse!==(n.lang.rtl===!0)?`scale3d(-1, 1, 1) rotate3d(0, 0, 1, ${-90-v}deg)`:`rotate3d(0, 0, 1, ${v-90}deg)`}}),o=s(()=>e.instantFeedback!==!0&&e.indeterminate!==!0?{transition:`stroke-dashoffset ${e.animationSpeed}ms ease 0s, stroke ${e.animationSpeed}ms ease`}:""),i=s(()=>Ku/(1-e.thickness/2)),u=s(()=>`${i.value/2} ${i.value/2} ${i.value} ${i.value}`),d=s(()=>tt(e.value,e.min,e.max)),f=s(()=>e.max-e.min),c=s(()=>e.thickness/2*i.value),h=s(()=>{const v=(e.max-d.value)/f.value,g=e.rounded===!0&&d.value<e.max&&v<.25?c.value/2*(1-v/.25):0;return Wu*v+g});function m({thickness:v,offset:g,color:_,cls:p,rounded:k}){return r("circle",{class:"q-circular-progress__"+p+(_!==void 0?` text-${_}`:""),style:o.value,fill:"transparent",stroke:"currentColor","stroke-width":v,"stroke-dasharray":nf,"stroke-dashoffset":g,"stroke-linecap":k,cx:i.value,cy:i.value,r:xo})}return()=>{const v=[];e.centerColor!==void 0&&e.centerColor!=="transparent"&&v.push(r("circle",{class:`q-circular-progress__center text-${e.centerColor}`,fill:"currentColor",r:xo-c.value/2,cx:i.value,cy:i.value})),e.trackColor!==void 0&&e.trackColor!=="transparent"&&v.push(m({cls:"track",thickness:c.value,offset:0,color:e.trackColor})),v.push(m({cls:"circle",thickness:c.value,offset:h.value,color:e.color,rounded:e.rounded===!0?"round":void 0}));const g=[r("svg",{class:"q-circular-progress__svg",style:a.value,viewBox:u.value,"aria-hidden":"true"},v)];return e.showValue===!0&&g.push(r("div",{class:"q-circular-progress__text absolute-full row flex-center content-center",style:{fontSize:e.fontSize}},t.default!==void 0?t.default():[r("div",d.value)])),r("div",{class:`q-circular-progress q-circular-progress--${e.indeterminate===!0?"in":""}determinate`,style:l.value,role:"progressbar","aria-valuemin":e.min,"aria-valuemax":e.max,"aria-valuenow":e.indeterminate===!0?void 0:d.value},Vo(t.internal,g))}}});function Zl(e,t,n){const l=$t(e);let a,o=l.left-t.event.x,i=l.top-t.event.y,u=Math.abs(o),d=Math.abs(i);const f=t.direction;f.horizontal===!0&&f.vertical!==!0?a=o<0?"left":"right":f.horizontal!==!0&&f.vertical===!0?a=i<0?"up":"down":f.up===!0&&i<0?(a="up",u>d&&(f.left===!0&&o<0?a="left":f.right===!0&&o>0&&(a="right"))):f.down===!0&&i>0?(a="down",u>d&&(f.left===!0&&o<0?a="left":f.right===!0&&o>0&&(a="right"))):f.left===!0&&o<0?(a="left",u<d&&(f.up===!0&&i<0?a="up":f.down===!0&&i>0&&(a="down"))):f.right===!0&&o>0&&(a="right",u<d&&(f.up===!0&&i<0?a="up":f.down===!0&&i>0&&(a="down")));let c=!1;if(a===void 0&&n===!1){if(t.event.isFirst===!0||t.event.lastDir===void 0)return{};a=t.event.lastDir,c=!0,a==="left"||a==="right"?(l.left-=o,u=0,o=0):(l.top-=i,d=0,i=0)}return{synthetic:c,payload:{evt:e,touch:t.event.mouse!==!0,mouse:t.event.mouse===!0,position:l,direction:a,isFirst:t.event.isFirst,isFinal:n===!0,duration:Date.now()-t.event.time,distance:{x:u,y:d},offset:{x:o,y:i},delta:{x:l.left-t.event.lastX,y:l.top-t.event.lastY}}}}var af=0,Ft=Xt({name:"touch-pan",beforeMount(e,{value:t,modifiers:n}){if(n.mouse!==!0&&Ae.has.touch!==!0)return;function l(o,i){n.mouse===!0&&i===!0?Fe(o):(n.stop===!0&&dt(o),n.prevent===!0&&wt(o))}const a={uid:"qvtp_"+af++,handler:t,modifiers:n,direction:ql(n),noop:it,mouseStart(o){Tl(o,a)&&Ia(o)&&(vt(a,"temp",[[document,"mousemove","move","notPassiveCapture"],[document,"mouseup","end","passiveCapture"]]),a.start(o,!0))},touchStart(o){if(Tl(o,a)){const i=o.target;vt(a,"temp",[[i,"touchmove","move","notPassiveCapture"],[i,"touchcancel","end","passiveCapture"],[i,"touchend","end","passiveCapture"]]),a.start(o)}},start(o,i){if(Ae.is.firefox===!0&&Pn(e,!0),a.lastEvt=o,i===!0||n.stop===!0){if(a.direction.all!==!0&&(i!==!0||a.modifiers.mouseAllDir!==!0&&a.modifiers.mousealldir!==!0)){const f=o.type.indexOf("mouse")!==-1?new MouseEvent(o.type,o):new TouchEvent(o.type,o);o.defaultPrevented===!0&&wt(f),o.cancelBubble===!0&&dt(f),Object.assign(f,{qKeyEvent:o.qKeyEvent,qClickOutside:o.qClickOutside,qAnchorHandled:o.qAnchorHandled,qClonedBy:o.qClonedBy===void 0?[a.uid]:o.qClonedBy.concat(a.uid)}),a.initialEvent={target:o.target,event:f}}dt(o)}const{left:u,top:d}=$t(o);a.event={x:u,y:d,time:Date.now(),mouse:i===!0,detected:!1,isFirst:!0,isFinal:!1,lastX:u,lastY:d}},move(o){if(a.event===void 0)return;const i=$t(o),u=i.left-a.event.x,d=i.top-a.event.y;if(u===0&&d===0)return;a.lastEvt=o;const f=a.event.mouse===!0,c=()=>{l(o,f);let v;n.preserveCursor!==!0&&n.preservecursor!==!0&&(v=document.documentElement.style.cursor||"",document.documentElement.style.cursor="grabbing"),f===!0&&document.body.classList.add("no-pointer-events--children"),document.body.classList.add("non-selectable"),Wt(),a.styleCleanup=g=>{if(a.styleCleanup=void 0,v!==void 0&&(document.documentElement.style.cursor=v),document.body.classList.remove("non-selectable"),f===!0){const _=()=>{document.body.classList.remove("no-pointer-events--children")};g!==void 0?setTimeout(()=>{_(),g()},50):_()}else g!==void 0&&g()}};if(a.event.detected===!0){a.event.isFirst!==!0&&l(o,a.event.mouse);const{payload:v,synthetic:g}=Zl(o,a,!1);v!==void 0&&(a.handler(v)===!1?a.end(o):(a.styleCleanup===void 0&&a.event.isFirst===!0&&c(),a.event.lastX=v.position.left,a.event.lastY=v.position.top,a.event.lastDir=g===!0?void 0:v.direction,a.event.isFirst=!1));return}if(a.direction.all===!0||f===!0&&(a.modifiers.mouseAllDir===!0||a.modifiers.mousealldir===!0)){c(),a.event.detected=!0,a.move(o);return}const h=Math.abs(u),m=Math.abs(d);h!==m&&(a.direction.horizontal===!0&&h>m||a.direction.vertical===!0&&h<m||a.direction.up===!0&&h<m&&d<0||a.direction.down===!0&&h<m&&d>0||a.direction.left===!0&&h>m&&u<0||a.direction.right===!0&&h>m&&u>0?(a.event.detected=!0,a.move(o)):a.end(o,!0))},end(o,i){if(a.event!==void 0){if(Mt(a,"temp"),Ae.is.firefox===!0&&Pn(e,!1),i===!0)a.styleCleanup!==void 0&&a.styleCleanup(),a.event.detected!==!0&&a.initialEvent!==void 0&&a.initialEvent.target.dispatchEvent(a.initialEvent.event);else if(a.event.detected===!0){a.event.isFirst===!0&&a.handler(Zl(o===void 0?a.lastEvt:o,a).payload);const{payload:u}=Zl(o===void 0?a.lastEvt:o,a,!0),d=()=>{a.handler(u)};a.styleCleanup!==void 0?a.styleCleanup(d):d()}a.event=void 0,a.initialEvent=void 0,a.lastEvt=void 0}}};if(e.__qtouchpan=a,n.mouse===!0){const o=n.mouseCapture===!0||n.mousecapture===!0?"Capture":"";vt(a,"main",[[e,"mousedown","mouseStart",`passive${o}`]])}Ae.has.touch===!0&&vt(a,"main",[[e,"touchstart","touchStart",`passive${n.capture===!0?"Capture":""}`],[e,"touchmove","noop","notPassiveCapture"]])},updated(e,t){const n=e.__qtouchpan;n!==void 0&&(t.oldValue!==t.value&&(typeof value!="function"&&n.end(),n.handler=t.value),n.direction=ql(t.modifiers))},beforeUnmount(e){const t=e.__qtouchpan;t!==void 0&&(t.event!==void 0&&t.end(),Mt(t,"main"),Mt(t,"temp"),Ae.is.firefox===!0&&Pn(e,!1),t.styleCleanup!==void 0&&t.styleCleanup(),delete e.__qtouchpan)}}),Wr="q-slider__marker-labels",lf=e=>({value:e}),of=({marker:e})=>r("div",{key:e.value,style:e.style,class:e.classes},e.label),sr=[34,37,40,33,39,38],Yu={...Ke,...Ht,min:{type:Number,default:0},max:{type:Number,default:100},innerMin:Number,innerMax:Number,step:{type:Number,default:1,validator:e=>e>=0},snap:Boolean,vertical:Boolean,reverse:Boolean,color:String,markerLabelsClass:String,label:Boolean,labelColor:String,labelTextColor:String,labelAlways:Boolean,switchLabelSide:Boolean,markers:[Boolean,Number],markerLabels:[Boolean,Array,Object,Function],switchMarkerLabelsSide:Boolean,trackImg:String,trackColor:String,innerTrackImg:String,innerTrackColor:String,selectionColor:String,selectionImg:String,thumbSize:{type:String,default:"20px"},trackSize:{type:String,default:"4px"},disable:Boolean,readonly:Boolean,dense:Boolean,tabindex:[String,Number],thumbColor:String,thumbPath:{type:String,default:"M 4, 10 a 6,6 0 1,0 12,0 a 6,6 0 1,0 -12,0"}},Xu=["pan","update:modelValue","change"];function Gu({updateValue:e,updatePosition:t,getDragging:n,formAttrs:l}){const{props:a,emit:o,slots:i,proxy:{$q:u}}=ve(),d=We(a,u),f=wn(l),c=O(!1),h=O(!1),m=O(!1),v=O(!1),g=s(()=>a.vertical===!0?"--v":"--h"),_=s(()=>"-"+(a.switchLabelSide===!0?"switched":"standard")),p=s(()=>a.vertical===!0?a.reverse===!0:a.reverse!==(u.lang.rtl===!0)),k=s(()=>isNaN(a.innerMin)===!0||a.innerMin<a.min?a.min:a.innerMin),w=s(()=>isNaN(a.innerMax)===!0||a.innerMax>a.max?a.max:a.innerMax),y=s(()=>a.disable!==!0&&a.readonly!==!0&&k.value<w.value),b=s(()=>{if(a.step===0)return ae=>ae;const X=(String(a.step).trim().split(".")[1]||"").length;return ae=>parseFloat(ae.toFixed(X))}),S=s(()=>a.step===0?1:a.step),B=s(()=>y.value===!0?a.tabindex||0:-1),L=s(()=>a.max-a.min),R=s(()=>w.value-k.value),A=s(()=>xe(k.value)),P=s(()=>xe(w.value)),z=s(()=>a.vertical===!0?p.value===!0?"bottom":"top":p.value===!0?"right":"left"),T=s(()=>a.vertical===!0?"height":"width"),x=s(()=>a.vertical===!0?"width":"height"),q=s(()=>a.vertical===!0?"vertical":"horizontal"),U=s(()=>{const X={role:"slider","aria-valuemin":k.value,"aria-valuemax":w.value,"aria-orientation":q.value,"data-step":a.step};return a.disable===!0?X["aria-disabled"]="true":a.readonly===!0&&(X["aria-readonly"]="true"),X}),W=s(()=>`q-slider q-slider${g.value} q-slider--${c.value===!0?"":"in"}active inline no-wrap `+(a.vertical===!0?"row":"column")+(a.disable===!0?" disabled":" q-slider--enabled"+(y.value===!0?" q-slider--editable":""))+(m.value==="both"?" q-slider--focus":"")+(a.label||a.labelAlways===!0?" q-slider--label":"")+(a.labelAlways===!0?" q-slider--label-always":"")+(d.value===!0?" q-slider--dark":"")+(a.dense===!0?" q-slider--dense q-slider--dense"+g.value:""));function E(X){const ae="q-slider__"+X;return`${ae} ${ae}${g.value} ${ae}${g.value}${_.value}`}function V(X){const ae="q-slider__"+X;return`${ae} ${ae}${g.value}`}const j=s(()=>{const X=a.selectionColor||a.color;return"q-slider__selection absolute"+(X!==void 0?` text-${X}`:"")}),ue=s(()=>V("markers")+" absolute overflow-hidden"),I=s(()=>V("track-container")),C=s(()=>E("pin")),K=s(()=>E("label")),Q=s(()=>E("text-container")),oe=s(()=>E("marker-labels-container")+(a.markerLabelsClass!==void 0?` ${a.markerLabelsClass}`:"")),M=s(()=>"q-slider__track relative-position no-outline"+(a.trackColor!==void 0?` bg-${a.trackColor}`:"")),D=s(()=>{const X={[x.value]:a.trackSize};return a.trackImg!==void 0&&(X.backgroundImage=`url(${a.trackImg}) !important`),X}),le=s(()=>"q-slider__inner absolute"+(a.innerTrackColor!==void 0?` bg-${a.innerTrackColor}`:"")),H=s(()=>{const X=P.value-A.value,ae={[z.value]:`${100*A.value}%`,[T.value]:X===0?"2px":`${100*X}%`};return a.innerTrackImg!==void 0&&(ae.backgroundImage=`url(${a.innerTrackImg}) !important`),ae});function de(X){const{min:ae,max:ke,step:Re}=a;let _e=ae+X*(ke-ae);if(Re>0){const He=(_e-k.value)%Re;_e+=(Math.abs(He)>=Re/2?(He<0?-1:1)*Re:0)-He}return _e=b.value(_e),tt(_e,k.value,w.value)}function xe(X){return L.value===0?0:(X-a.min)/L.value}function Y(X,ae){const ke=$t(X),Re=a.vertical===!0?tt((ke.top-ae.top)/ae.height,0,1):tt((ke.left-ae.left)/ae.width,0,1);return tt(p.value===!0?1-Re:Re,A.value,P.value)}const se=s(()=>Rn(a.markers)===!0?a.markers:S.value),me=s(()=>{const X=[],ae=se.value,ke=a.max;let Re=a.min;do X.push(Re),Re+=ae;while(Re<ke);return X.push(ke),X}),we=s(()=>{const X=` ${Wr}${g.value}-`;return Wr+`${X}${a.switchMarkerLabelsSide===!0?"switched":"standard"}${X}${p.value===!0?"rtl":"ltr"}`}),Me=s(()=>a.markerLabels===!1?null:ze(a.markerLabels).map((X,ae)=>({index:ae,value:X.value,label:X.label||X.value,classes:we.value+(X.classes!==void 0?" "+X.classes:""),style:{...Oe(X.value),...X.style||{}}}))),ce=s(()=>({markerList:Me.value,markerMap:re.value,classes:we.value,getStyle:Oe})),$e=s(()=>{const X=R.value===0?"2px":100*se.value/R.value;return{...H.value,backgroundSize:a.vertical===!0?`2px ${X}%`:`${X}% 2px`}});function ze(X){if(X===!1)return null;if(X===!0)return me.value.map(lf);if(typeof X=="function")return me.value.map(ke=>{const Re=X(ke);return yt(Re)===!0?{...Re,value:ke}:{value:ke,label:Re}});const ae=({value:ke})=>ke>=a.min&&ke<=a.max;return Array.isArray(X)===!0?X.map(ke=>yt(ke)===!0?ke:{value:ke}).filter(ae):Object.keys(X).map(ke=>{const Re=X[ke],_e=Number(ke);return yt(Re)===!0?{...Re,value:_e}:{value:_e,label:Re}}).filter(ae)}function Oe(X){return{[z.value]:`${100*(X-a.min)/L.value}%`}}const re=s(()=>{if(a.markerLabels===!1)return null;const X={};return Me.value.forEach(ae=>{X[ae.value]=ae}),X});function fe(){if(i["marker-label-group"]!==void 0)return i["marker-label-group"](ce.value);const X=i["marker-label"]||of;return Me.value.map(ae=>X({marker:ae,...ce.value}))}const G=s(()=>[[Ft,be,void 0,{[q.value]:!0,prevent:!0,stop:!0,mouse:!0,mouseAllDir:!0}]]);function be(X){X.isFinal===!0?(v.value!==void 0&&(t(X.evt),X.touch===!0&&e(!0),v.value=void 0,o("pan","end")),c.value=!1,m.value=!1):X.isFirst===!0?(v.value=n(X.evt),t(X.evt),e(),c.value=!0,o("pan","start")):(t(X.evt),e())}function Be(){m.value=!1}function Ee(X){t(X,n(X)),e(),h.value=!0,c.value=!0,document.addEventListener("mouseup",Pe,!0)}function Pe(){h.value=!1,c.value=!1,e(!0),Be(),document.removeEventListener("mouseup",Pe,!0)}function Ge(X){t(X,n(X)),e(!0)}function nt(X){sr.includes(X.keyCode)&&e(!0)}function at(X){if(a.vertical===!0)return null;const ae=u.lang.rtl!==a.reverse?1-X:X;return{transform:`translateX(calc(${2*ae-1} * ${a.thumbSize} / 2 + ${50-100*ae}%))`}}function J(X){const ae=s(()=>h.value===!1&&(m.value===X.focusValue||m.value==="both")?" q-slider--focus":""),ke=s(()=>`q-slider__thumb q-slider__thumb${g.value} q-slider__thumb${g.value}-${p.value===!0?"rtl":"ltr"} absolute non-selectable`+ae.value+(X.thumbColor.value!==void 0?` text-${X.thumbColor.value}`:"")),Re=s(()=>({width:a.thumbSize,height:a.thumbSize,[z.value]:`${100*X.ratio.value}%`,zIndex:m.value===X.focusValue?2:void 0})),_e=s(()=>X.labelColor.value!==void 0?` text-${X.labelColor.value}`:""),He=s(()=>at(X.ratio.value)),ot=s(()=>"q-slider__text"+(X.labelTextColor.value!==void 0?` text-${X.labelTextColor.value}`:""));return()=>{const ut=[r("svg",{class:"q-slider__thumb-shape absolute-full",viewBox:"0 0 20 20","aria-hidden":"true"},[r("path",{d:a.thumbPath})]),r("div",{class:"q-slider__focus-ring fit"})];return(a.label===!0||a.labelAlways===!0)&&(ut.push(r("div",{class:C.value+" absolute fit no-pointer-events"+_e.value},[r("div",{class:K.value,style:{minWidth:a.thumbSize}},[r("div",{class:Q.value,style:He.value},[r("span",{class:ot.value},X.label.value)])])])),a.name!==void 0&&a.disable!==!0&&f(ut,"push")),r("div",{class:ke.value,style:Re.value,...X.getNodeData()},ut)}}function ie(X,ae,ke,Re){const _e=[];a.innerTrackColor!=="transparent"&&_e.push(r("div",{key:"inner",class:le.value,style:H.value})),a.selectionColor!=="transparent"&&_e.push(r("div",{key:"selection",class:j.value,style:X.value})),a.markers!==!1&&_e.push(r("div",{key:"marker",class:ue.value,style:$e.value})),Re(_e);const He=[Dt("div",{key:"trackC",class:I.value,tabindex:ae.value,...ke.value},[r("div",{class:M.value,style:D.value},_e)],"slide",y.value,()=>G.value)];if(a.markerLabels!==!1){const ot=a.switchMarkerLabelsSide===!0?"unshift":"push";He[ot](r("div",{key:"markerL",class:oe.value},fe()))}return He}return Ne(()=>{document.removeEventListener("mouseup",Pe,!0)}),{state:{active:c,focus:m,preventFocus:h,dragging:v,editable:y,classes:W,tabindex:B,attributes:U,roundValueFn:b,keyStep:S,trackLen:L,innerMin:k,innerMinRatio:A,innerMax:w,innerMaxRatio:P,positionProp:z,sizeProp:T,isReversed:p},methods:{onActivate:Ee,onMobileClick:Ge,onBlur:Be,onKeyup:nt,getContent:ie,getThumbRenderFn:J,convertRatioToModel:de,convertModelToRatio:xe,getDraggingRatio:Y}}}var rf=()=>({}),Yn=te({name:"QSlider",props:{...Yu,modelValue:{required:!0,default:null,validator:e=>typeof e=="number"||e===null},labelValue:[String,Number]},emits:Xu,setup(e,{emit:t}){const{proxy:{$q:n}}=ve(),{state:l,methods:a}=Gu({updateValue:g,updatePosition:p,getDragging:_,formAttrs:Ka(e)}),o=O(null),i=O(0),u=O(0);function d(){u.value=e.modelValue===null?l.innerMin.value:tt(e.modelValue,l.innerMin.value,l.innerMax.value)}ne(()=>`${e.modelValue}|${l.innerMin.value}|${l.innerMax.value}`,d),d();const f=s(()=>a.convertModelToRatio(u.value)),c=s(()=>l.active.value===!0?i.value:f.value),h=s(()=>{const y={[l.positionProp.value]:`${100*l.innerMinRatio.value}%`,[l.sizeProp.value]:`${100*(c.value-l.innerMinRatio.value)}%`};return e.selectionImg!==void 0&&(y.backgroundImage=`url(${e.selectionImg}) !important`),y}),m=a.getThumbRenderFn({focusValue:!0,getNodeData:rf,ratio:c,label:s(()=>e.labelValue!==void 0?e.labelValue:u.value),thumbColor:s(()=>e.thumbColor||e.color),labelColor:s(()=>e.labelColor),labelTextColor:s(()=>e.labelTextColor)}),v=s(()=>l.editable.value!==!0?{}:n.platform.is.mobile===!0?{onClick:a.onMobileClick}:{onMousedown:a.onActivate,onFocus:k,onBlur:a.onBlur,onKeydown:w,onKeyup:a.onKeyup});function g(y){u.value!==e.modelValue&&t("update:modelValue",u.value),y===!0&&t("change",u.value)}function _(){return o.value.getBoundingClientRect()}function p(y,b=l.dragging.value){const S=a.getDraggingRatio(y,b);u.value=a.convertRatioToModel(S),i.value=e.snap!==!0||e.step===0?S:a.convertModelToRatio(u.value)}function k(){l.focus.value=!0}function w(y){if(!sr.includes(y.keyCode))return;Fe(y);const b=([34,33].includes(y.keyCode)?10:1)*l.keyStep.value,S=([34,37,40].includes(y.keyCode)?-1:1)*(l.isReversed.value===!0?-1:1)*(e.vertical===!0?-1:1)*b;u.value=tt(l.roundValueFn.value(u.value+S),l.innerMin.value,l.innerMax.value),g()}return()=>{const y=a.getContent(h,l.tabindex,v,b=>{b.push(m())});return r("div",{ref:o,class:l.classes.value+(e.modelValue===null?" q-slider--no-value":""),...l.attributes.value,"aria-valuenow":e.modelValue},y)}}});function Zu(){const e=O(!Pt.value);return e.value===!1&&ft(()=>{e.value=!0}),{isHydrated:e}}var Ju=typeof ResizeObserver!="undefined",Ur=Ju===!0?{}:{style:"display:block;position:absolute;top:0;left:0;right:0;bottom:0;height:100%;width:100%;overflow:hidden;pointer-events:none;z-index:-1;",url:"about:blank"},Vn=te({name:"QResizeObserver",props:{debounce:{type:[String,Number],default:100}},emits:["resize"],setup(e,{emit:t}){let n=null,l,a={width:-1,height:-1};function o(d){d===!0||e.debounce===0||e.debounce==="0"?i():n===null&&(n=setTimeout(i,e.debounce))}function i(){if(n!==null&&(clearTimeout(n),n=null),l){const{offsetWidth:d,offsetHeight:f}=l;(d!==a.width||f!==a.height)&&(a={width:d,height:f},t("resize",a))}}const{proxy:u}=ve();if(u.trigger=o,Ju===!0){let d;const f=c=>{l=u.$el.parentNode,l?(d=new ResizeObserver(o),d.observe(l),i()):c!==!0&&Qe(()=>{f(!0)})};return ft(()=>{f()}),Ne(()=>{n!==null&&clearTimeout(n),d!==void 0&&(d.disconnect!==void 0?d.disconnect():l&&d.unobserve(l))}),it}else{let d=function(){n!==null&&(clearTimeout(n),n=null),h!==void 0&&(h.removeEventListener!==void 0&&h.removeEventListener("resize",o,et.passive),h=void 0)},f=function(){d(),l&&l.contentDocument&&(h=l.contentDocument.defaultView,h.addEventListener("resize",o,et.passive),i())};const{isHydrated:c}=Zu();let h;return ft(()=>{Qe(()=>{l=u.$el,l&&f()})}),Ne(d),()=>{if(c.value===!0)return r("object",{class:"q--avoid-card-border",style:Ur.style,tabindex:-1,type:"text/html",data:Ur.url,"aria-hidden":"true",onLoad:f})}}}}),Va=!1;{const e=document.createElement("div");e.setAttribute("dir","rtl"),Object.assign(e.style,{width:"1px",height:"1px",overflow:"auto"});const t=document.createElement("div");Object.assign(t.style,{width:"1000px",height:"1px"}),document.body.appendChild(e),e.appendChild(t),e.scrollLeft=-1e3,Va=e.scrollLeft>=0,e.remove()}function uf(e,t,n){const l=n===!0?["left","right"]:["top","bottom"];return`absolute-${t===!0?l[0]:l[1]}${e?` text-${e}`:""}`}var sf=["left","center","right","justify"],Yr=te({name:"QTabs",props:{modelValue:[Number,String],align:{type:String,default:"center",validator:e=>sf.includes(e)},breakpoint:{type:[String,Number],default:600},vertical:Boolean,shrink:Boolean,stretch:Boolean,activeClass:String,activeColor:String,activeBgColor:String,indicatorColor:String,leftIcon:String,rightIcon:String,outsideArrows:Boolean,mobileArrows:Boolean,switchIndicator:Boolean,narrowIndicator:Boolean,inlineLabel:Boolean,noCaps:Boolean,dense:Boolean,contentClass:String,"onUpdate:modelValue":[Function,Array]},setup(e,{slots:t,emit:n}){const{proxy:l}=ve(),{$q:a}=l,{registerTick:o}=ia(),{registerTick:i}=ia(),{registerTick:u}=ia(),{registerTimeout:d,removeTimeout:f}=hn(),{registerTimeout:c,removeTimeout:h}=hn(),m=O(null),v=O(null),g=O(e.modelValue),_=O(!1),p=O(!0),k=O(!1),w=O(!1),y=[],b=O(0),S=O(!1);let B=null,L=null,R;const A=s(()=>({activeClass:e.activeClass,activeColor:e.activeColor,activeBgColor:e.activeBgColor,indicatorClass:uf(e.indicatorColor,e.switchIndicator,e.vertical),narrowIndicator:e.narrowIndicator,inlineLabel:e.inlineLabel,noCaps:e.noCaps})),P=s(()=>{const re=b.value,fe=g.value;for(let G=0;G<re;G++)if(y[G].name.value===fe)return!0;return!1}),z=s(()=>`q-tabs__content--align-${_.value===!0?"left":w.value===!0?"justify":e.align}`),T=s(()=>`q-tabs row no-wrap items-center q-tabs--${_.value===!0?"":"not-"}scrollable q-tabs--${e.vertical===!0?"vertical":"horizontal"} q-tabs__arrows--${e.outsideArrows===!0?"outside":"inside"} q-tabs--mobile-with${e.mobileArrows===!0?"":"out"}-arrows`+(e.dense===!0?" q-tabs--dense":"")+(e.shrink===!0?" col-shrink":"")+(e.stretch===!0?" self-stretch":"")),x=s(()=>"q-tabs__content scroll--mobile row no-wrap items-center self-stretch hide-scrollbar relative-position "+z.value+(e.contentClass!==void 0?` ${e.contentClass}`:"")),q=s(()=>e.vertical===!0?{container:"height",content:"offsetHeight",scroll:"scrollHeight"}:{container:"width",content:"offsetWidth",scroll:"scrollWidth"}),U=s(()=>e.vertical!==!0&&a.lang.rtl===!0),W=s(()=>Va===!1&&U.value===!0);ne(U,C),ne(()=>e.modelValue,re=>{E({name:re,setCurrent:!0,skipEmit:!0})}),ne(()=>e.outsideArrows,V);function E({name:re,setCurrent:fe,skipEmit:G}){g.value!==re&&(G!==!0&&e["onUpdate:modelValue"]!==void 0&&n("update:modelValue",re),(fe===!0||e["onUpdate:modelValue"]===void 0)&&(ue(g.value,re),g.value=re))}function V(){o(()=>{j({width:m.value.offsetWidth,height:m.value.offsetHeight})})}function j(re){if(q.value===void 0||v.value===null)return;const fe=re[q.value.container],G=Math.min(v.value[q.value.scroll],Array.prototype.reduce.call(v.value.children,(Be,Ee)=>Be+(Ee[q.value.content]||0),0)),be=fe>0&&G>fe;_.value=be,be===!0&&i(C),w.value=fe<parseInt(e.breakpoint,10)}function ue(re,fe){const G=re!=null&&re!==""?y.find(Be=>Be.name.value===re):null,be=fe!=null&&fe!==""?y.find(Be=>Be.name.value===fe):null;if(G&&be){const Be=G.tabIndicatorRef.value,Ee=be.tabIndicatorRef.value;B!==null&&(clearTimeout(B),B=null),Be.style.transition="none",Be.style.transform="none",Ee.style.transition="none",Ee.style.transform="none";const Pe=Be.getBoundingClientRect(),Ge=Ee.getBoundingClientRect();Ee.style.transform=e.vertical===!0?`translate3d(0,${Pe.top-Ge.top}px,0) scale3d(1,${Ge.height?Pe.height/Ge.height:1},1)`:`translate3d(${Pe.left-Ge.left}px,0,0) scale3d(${Ge.width?Pe.width/Ge.width:1},1,1)`,u(()=>{B=setTimeout(()=>{B=null,Ee.style.transition="transform .25s cubic-bezier(.4, 0, .2, 1)",Ee.style.transform="none"},70)})}be&&_.value===!0&&I(be.rootRef.value)}function I(re){const{left:fe,width:G,top:be,height:Be}=v.value.getBoundingClientRect(),Ee=re.getBoundingClientRect();let Pe=e.vertical===!0?Ee.top-be:Ee.left-fe;if(Pe<0){v.value[e.vertical===!0?"scrollTop":"scrollLeft"]+=Math.floor(Pe),C();return}Pe+=e.vertical===!0?Ee.height-Be:Ee.width-G,Pe>0&&(v.value[e.vertical===!0?"scrollTop":"scrollLeft"]+=Math.ceil(Pe),C())}function C(){const re=v.value;if(re===null)return;const fe=re.getBoundingClientRect(),G=e.vertical===!0?re.scrollTop:Math.abs(re.scrollLeft);U.value===!0?(p.value=Math.ceil(G+fe.width)<re.scrollWidth-1,k.value=G>0):(p.value=G>0,k.value=e.vertical===!0?Math.ceil(G+fe.height)<re.scrollHeight:Math.ceil(G+fe.width)<re.scrollWidth)}function K(re){L!==null&&clearInterval(L),L=setInterval(()=>{H(re)===!0&&M()},5)}function Q(){K(W.value===!0?Number.MAX_SAFE_INTEGER:0)}function oe(){K(W.value===!0?0:Number.MAX_SAFE_INTEGER)}function M(){L!==null&&(clearInterval(L),L=null)}function D(re,fe){const G=Array.prototype.filter.call(v.value.children,Ge=>Ge===fe||Ge.matches&&Ge.matches(".q-tab.q-focusable")===!0),be=G.length;if(be===0)return;if(re===36)return I(G[0]),G[0].focus(),!0;if(re===35)return I(G[be-1]),G[be-1].focus(),!0;const Be=re===(e.vertical===!0?38:37),Ee=re===(e.vertical===!0?40:39),Pe=Be===!0?-1:Ee===!0?1:void 0;if(Pe!==void 0){const Ge=U.value===!0?-1:1,nt=G.indexOf(fe)+Pe*Ge;return nt>=0&&nt<be&&(I(G[nt]),G[nt].focus({preventScroll:!0})),!0}}const le=s(()=>W.value===!0?{get:re=>Math.abs(re.scrollLeft),set:(re,fe)=>{re.scrollLeft=-fe}}:e.vertical===!0?{get:re=>re.scrollTop,set:(re,fe)=>{re.scrollTop=fe}}:{get:re=>re.scrollLeft,set:(re,fe)=>{re.scrollLeft=fe}});function H(re){const fe=v.value,{get:G,set:be}=le.value;let Be=!1,Ee=G(fe);const Pe=re<Ee?-1:1;return Ee+=Pe*5,Ee<0?(Be=!0,Ee=0):(Pe===-1&&Ee<=re||Pe===1&&Ee>=re)&&(Be=!0,Ee=re),be(fe,Ee),C(),Be}function de(re,fe){for(const G in re)if(re[G]!==fe[G])return!1;return!0}function xe(){let re=null,fe={matchedLen:0,queryDiff:9999,hrefLen:0};const G=y.filter(Pe=>Pe.routeData!==void 0&&Pe.routeData.hasRouterLink.value===!0),{hash:be,query:Be}=l.$route,Ee=Object.keys(Be).length;for(const Pe of G){const Ge=Pe.routeData.exact.value===!0;if(Pe.routeData[Ge===!0?"linkIsExactActive":"linkIsActive"].value!==!0)continue;const{hash:nt,query:at,matched:J,href:ie}=Pe.routeData.resolvedLink.value,X=Object.keys(at).length;if(Ge===!0){if(nt!==be||X!==Ee||de(Be,at)===!1)continue;re=Pe.name.value;break}if(nt!==""&&nt!==be||X!==0&&de(at,Be)===!1)continue;const ae={matchedLen:J.length,queryDiff:Ee-X,hrefLen:ie.length-nt.length};if(ae.matchedLen>fe.matchedLen){re=Pe.name.value,fe=ae;continue}else if(ae.matchedLen!==fe.matchedLen)continue;if(ae.queryDiff<fe.queryDiff)re=Pe.name.value,fe=ae;else if(ae.queryDiff!==fe.queryDiff)continue;ae.hrefLen>fe.hrefLen&&(re=Pe.name.value,fe=ae)}re===null&&y.some(Pe=>Pe.routeData===void 0&&Pe.name.value===g.value)===!0||E({name:re,setCurrent:!0})}function Y(re){if(f(),S.value!==!0&&m.value!==null&&re.target&&typeof re.target.closest=="function"){const fe=re.target.closest(".q-tab");fe&&m.value.contains(fe)===!0&&(S.value=!0,_.value===!0&&I(fe))}}function se(){d(()=>{S.value=!1},30)}function me(){$e.avoidRouteWatcher===!1?c(xe):h()}function we(){if(R===void 0){const re=ne(()=>l.$route.fullPath,me);R=()=>{re(),R=void 0}}}function Me(re){y.push(re),b.value++,V(),re.routeData===void 0||l.$route===void 0?c(()=>{if(_.value===!0){const fe=g.value,G=fe!=null&&fe!==""?y.find(be=>be.name.value===fe):null;G&&I(G.rootRef.value)}}):(we(),re.routeData.hasRouterLink.value===!0&&me())}function ce(re){y.splice(y.indexOf(re),1),b.value--,V(),R!==void 0&&re.routeData!==void 0&&(y.every(fe=>fe.routeData===void 0)===!0&&R(),me())}const $e={currentModel:g,tabProps:A,hasFocus:S,hasActiveTab:P,registerTab:Me,unregisterTab:ce,verifyRouteModel:me,updateModel:E,onKbdNavigate:D,avoidRouteWatcher:!1};yn(du,$e);function ze(){B!==null&&clearTimeout(B),M(),R!==void 0&&R()}let Oe;return Ne(ze),Yt(()=>{Oe=R!==void 0,ze()}),bn(()=>{Oe===!0&&we(),V()}),()=>r("div",{ref:m,class:T.value,role:"tablist",onFocusin:Y,onFocusout:se},[r(Vn,{onResize:j}),r("div",{ref:v,class:x.value,onScroll:C},Ce(t.default)),r(je,{class:"q-tabs__arrow q-tabs__arrow--left absolute q-tab__icon"+(p.value===!0?"":" q-tabs__arrow--faded"),name:e.leftIcon||a.iconSet.tabs[e.vertical===!0?"up":"left"],onMousedownPassive:Q,onTouchstartPassive:Q,onMouseupPassive:M,onMouseleavePassive:M,onTouchendPassive:M}),r(je,{class:"q-tabs__arrow q-tabs__arrow--right absolute q-tab__icon"+(k.value===!0?"":" q-tabs__arrow--faded"),name:e.rightIcon||a.iconSet.tabs[e.vertical===!0?"down":"right"],onMousedownPassive:oe,onTouchstartPassive:oe,onMouseupPassive:M,onMouseleavePassive:M,onTouchendPassive:M})])}}),cf=0,es=["click","keydown"],ts={icon:String,label:[Number,String],alert:[Boolean,String],alertIcon:String,name:{type:[Number,String],default:()=>`t_${cf++}`},noCaps:Boolean,tabindex:[String,Number],disable:Boolean,contentClass:String,ripple:{type:[Boolean,Object],default:!0}};function ns(e,t,n,l){const a=Et(du,Je);if(a===Je)return console.error("QTab/QRouteTab component needs to be child of QTabs"),Je;const{proxy:o}=ve(),i=O(null),u=O(null),d=O(null),f=s(()=>e.disable===!0||e.ripple===!1?!1:Object.assign({keyCodes:[13,32],early:!0},e.ripple===!0?{}:e.ripple)),c=s(()=>a.currentModel.value===e.name),h=s(()=>"q-tab relative-position self-stretch flex flex-center text-center"+(c.value===!0?" q-tab--active"+(a.tabProps.value.activeClass?" "+a.tabProps.value.activeClass:"")+(a.tabProps.value.activeColor?` text-${a.tabProps.value.activeColor}`:"")+(a.tabProps.value.activeBgColor?` bg-${a.tabProps.value.activeBgColor}`:""):" q-tab--inactive")+(e.icon&&e.label&&a.tabProps.value.inlineLabel===!1?" q-tab--full":"")+(e.noCaps===!0||a.tabProps.value.noCaps===!0?" q-tab--no-caps":"")+(e.disable===!0?" disabled":" q-focusable q-hoverable cursor-pointer")+(l!==void 0?l.linkClass.value:"")),m=s(()=>"q-tab__content self-stretch flex-center relative-position q-anchor--skip non-selectable "+(a.tabProps.value.inlineLabel===!0?"row no-wrap q-tab__content--inline":"column")+(e.contentClass!==void 0?` ${e.contentClass}`:"")),v=s(()=>e.disable===!0||a.hasFocus.value===!0||c.value===!1&&a.hasActiveTab.value===!0?-1:e.tabindex||0);function g(y,b){if(b!==!0&&i.value!==null&&i.value.focus(),e.disable===!0){l!==void 0&&l.hasRouterLink.value===!0&&Fe(y);return}if(l===void 0){a.updateModel({name:e.name}),n("click",y);return}if(l.hasRouterLink.value===!0){const S=(B={})=>{let L;const R=B.to===void 0||Rt(B.to,e.to)===!0?a.avoidRouteWatcher=Fa():null;return l.navigateToRouterLink(y,{...B,returnRouterError:!0}).catch(A=>{L=A}).then(A=>{if(R===a.avoidRouteWatcher&&(a.avoidRouteWatcher=!1,L===void 0&&(A===void 0||A.message!==void 0&&A.message.startsWith("Avoided redundant navigation")===!0)&&a.updateModel({name:e.name})),B.returnRouterError===!0)return L!==void 0?Promise.reject(L):A})};n("click",y,S),y.defaultPrevented!==!0&&S();return}n("click",y)}function _(y){Vt(y,[13,32])?g(y,!0):Hn(y)!==!0&&y.keyCode>=35&&y.keyCode<=40&&y.altKey!==!0&&y.metaKey!==!0&&a.onKbdNavigate(y.keyCode,o.$el)===!0&&Fe(y),n("keydown",y)}function p(){const y=a.tabProps.value.narrowIndicator,b=[],S=r("div",{ref:d,class:["q-tab__indicator",a.tabProps.value.indicatorClass]});e.icon!==void 0&&b.push(r(je,{class:"q-tab__icon",name:e.icon})),e.label!==void 0&&b.push(r("div",{class:"q-tab__label"},e.label)),e.alert!==!1&&b.push(e.alertIcon!==void 0?r(je,{class:"q-tab__alert-icon",color:e.alert!==!0?e.alert:void 0,name:e.alertIcon}):r("div",{class:"q-tab__alert"+(e.alert!==!0?` text-${e.alert}`:"")})),y===!0&&b.push(S);const B=[r("div",{class:"q-focus-helper",tabindex:-1,ref:i}),r("div",{class:m.value},mt(t.default,b))];return y===!1&&B.push(S),B}const k={name:s(()=>e.name),rootRef:u,tabIndicatorRef:d,routeData:l};Ne(()=>{a.unregisterTab(k)}),ft(()=>{a.registerTab(k)});function w(y,b){const S={ref:u,class:h.value,tabindex:v.value,role:"tab","aria-selected":c.value===!0?"true":"false","aria-disabled":e.disable===!0?"true":void 0,onClick:g,onKeydown:_,...b};return Ut(r(y,S,p()),[[Ol,f.value]])}return{renderTab:w,$tabs:a}}var ya=te({name:"QTab",props:ts,emits:es,setup(e,{slots:t,emit:n}){const{renderTab:l}=ns(e,t,n);return()=>l("div")}}),df=te({name:"QTabPanels",props:{...nr,...Ke},emits:ar,setup(e,{slots:t}){const n=ve(),l=We(e,n.proxy.$q),{updatePanelsList:a,getPanelContent:o,panelDirectives:i}=lr(),u=s(()=>"q-tab-panels q-panel-parent"+(l.value===!0?" q-tab-panels--dark q-dark":""));return()=>(a(t),Dt("div",{class:u.value},o(),"pan",e.swipeable,()=>i.value))}}),Jl=te({name:"QTabPanel",props:tr,setup(e,{slots:t}){return()=>r("div",{class:"q-tab-panel",role:"tabpanel"},Ce(t.default))}}),Xr=/^#[0-9a-fA-F]{3}([0-9a-fA-F]{3})?$/,Gr=/^#[0-9a-fA-F]{4}([0-9a-fA-F]{4})?$/,Zr=/^#([0-9a-fA-F]{3}|[0-9a-fA-F]{4}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})$/,Xa=/^rgb\(((0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),){2}(0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5])\)$/,Ga=/^rgba\(((0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),){2}(0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),(0|0\.[0-9]+[1-9]|0\.[1-9]+|1)\)$/,Ba={date:e=>/^-?[\d]+\/[0-1]\d\/[0-3]\d$/.test(e),time:e=>/^([0-1]?\d|2[0-3]):[0-5]\d$/.test(e),fulltime:e=>/^([0-1]?\d|2[0-3]):[0-5]\d:[0-5]\d$/.test(e),timeOrFulltime:e=>/^([0-1]?\d|2[0-3]):[0-5]\d(:[0-5]\d)?$/.test(e),email:e=>/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(e),hexColor:e=>Xr.test(e),hexaColor:e=>Gr.test(e),hexOrHexaColor:e=>Zr.test(e),rgbColor:e=>Xa.test(e),rgbaColor:e=>Ga.test(e),rgbOrRgbaColor:e=>Xa.test(e)||Ga.test(e),hexOrRgbColor:e=>Xr.test(e)||Xa.test(e),hexaOrRgbaColor:e=>Gr.test(e)||Ga.test(e),anyColor:e=>Zr.test(e)||Xa.test(e)||Ga.test(e)},lh={testPattern:Ba},ff=/^rgb(a)?\((\d{1,3}),(\d{1,3}),(\d{1,3}),?([01]?\.?\d*?)?\)$/;function da({r:e,g:t,b:n,a:l}){const a=l!==void 0;if(e=Math.round(e),t=Math.round(t),n=Math.round(n),e>255||t>255||n>255||a&&l>100)throw new TypeError("Expected 3 numbers below 256 (and optionally one below 100)");return l=a?(Math.round(255*l/100)|1<<8).toString(16).slice(1):"","#"+(n|t<<8|e<<16|1<<24).toString(16).slice(1)+l}function Jr({r:e,g:t,b:n,a:l}){return`rgb${l!==void 0?"a":""}(${e},${t},${n}${l!==void 0?","+l/100:""})`}function cr(e){if(typeof e!="string")throw new TypeError("Expected a string");e=e.replace(/^#/,""),e.length===3?e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]:e.length===4&&(e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]+e[3]+e[3]);const t=parseInt(e,16);return e.length>6?{r:t>>24&255,g:t>>16&255,b:t>>8&255,a:Math.round((t&255)/2.55)}:{r:t>>16,g:t>>8&255,b:t&255}}function Co({h:e,s:t,v:n,a:l}){let a,o,i;t=t/100,n=n/100,e=e/360;const u=Math.floor(e*6),d=e*6-u,f=n*(1-t),c=n*(1-d*t),h=n*(1-(1-d)*t);switch(u%6){case 0:a=n,o=h,i=f;break;case 1:a=c,o=n,i=f;break;case 2:a=f,o=n,i=h;break;case 3:a=f,o=c,i=n;break;case 4:a=h,o=f,i=n;break;case 5:a=n,o=f,i=c;break}return{r:Math.round(a*255),g:Math.round(o*255),b:Math.round(i*255),a:l}}function cl({r:e,g:t,b:n,a:l}){const a=Math.max(e,t,n),o=Math.min(e,t,n),i=a-o,u=a===0?0:i/a,d=a/255;let f;switch(a){case o:f=0;break;case e:f=t-n+i*(t<n?6:0),f/=6*i;break;case t:f=n-e+i*2,f/=6*i;break;case n:f=e-t+i*4,f/=6*i;break}return{h:Math.round(f*360),s:Math.round(u*100),v:Math.round(d*100),a:l}}function un(e){if(typeof e!="string")throw new TypeError("Expected a string");const t=e.replace(/ /g,""),n=ff.exec(t);if(n===null)return cr(t);const l={r:Math.min(255,parseInt(n[2],10)),g:Math.min(255,parseInt(n[3],10)),b:Math.min(255,parseInt(n[4],10))};if(n[1]){const a=parseFloat(n[5]);l.a=Math.min(1,isNaN(a)===!0?1:a)*100}return l}function vf(e,t){if(typeof e!="string")throw new TypeError("Expected a string as color");if(typeof t!="number")throw new TypeError("Expected a numeric percent");const n=un(e),l=t<0?0:255,a=Math.abs(t)/100,o=n.r,i=n.g,u=n.b;return"#"+(16777216+(Math.round((l-o)*a)+o)*65536+(Math.round((l-i)*a)+i)*256+(Math.round((l-u)*a)+u)).toString(16).slice(1)}function as(e){if(typeof e!="string"&&(!e||e.r===void 0))throw new TypeError("Expected a string or a {r, g, b} object as color");const t=typeof e=="string"?un(e):e,n=t.r/255,l=t.g/255,a=t.b/255,o=n<=.03928?n/12.92:Math.pow((n+.055)/1.055,2.4),i=l<=.03928?l/12.92:Math.pow((l+.055)/1.055,2.4),u=a<=.03928?a/12.92:Math.pow((a+.055)/1.055,2.4);return .2126*o+.7152*i+.0722*u}function mf(e){if(typeof e!="string"&&(!e||e.r===void 0))throw new TypeError("Expected a string or a {r, g, b} object as color");const t=typeof e=="string"?un(e):e;return(t.r*299+t.g*587+t.b*114)/1e3}function gf(e,t){if(typeof e!="string"&&(!e||e.r===void 0))throw new TypeError("Expected a string or a {r, g, b[, a]} object as fgColor");if(typeof t!="string"&&(!t||t.r===void 0))throw new TypeError("Expected a string or a {r, g, b[, a]} object as bgColor");const n=typeof e=="string"?un(e):e,l=n.r/255,a=n.g/255,o=n.b/255,i=n.a!==void 0?n.a/100:1,u=typeof t=="string"?un(t):t,d=u.r/255,f=u.g/255,c=u.b/255,h=u.a!==void 0?u.a/100:1,m=i+h*(1-i),v=Math.round((l*i+d*h*(1-i))/m*255),g=Math.round((a*i+f*h*(1-i))/m*255),_=Math.round((o*i+c*h*(1-i))/m*255),p={r:v,g,b:_,a:Math.round(m*100)};return typeof e=="string"?da(p):p}function hf(e,t){if(typeof e!="string")throw new TypeError("Expected a string as color");if(t===void 0||t<-1||t>1)throw new TypeError("Expected offset to be between -1 and 1");const{r:n,g:l,b:a,a:o}=un(e),i=o!==void 0?o/100:0;return da({r:n,g:l,b:a,a:Math.round(Math.min(1,Math.max(0,i+t))*100)})}function bf(e){if(typeof e!="string")throw new TypeError("Expected a string as color");const t=document.createElement("div");t.className=`text-${e} invisible fixed no-pointer-events`,document.body.appendChild(t);const n=getComputedStyle(t).getPropertyValue("color");return t.remove(),da(un(n))}var oh={rgbToHex:da,hexToRgb:cr,hsvToRgb:Co,rgbToHsv:cl,textToRgb:un,lighten:vf,luminosity:as,brightness:mf,blend:gf,changeAlpha:hf,getPaletteColor:bf},yf=["rgb(255,204,204)","rgb(255,230,204)","rgb(255,255,204)","rgb(204,255,204)","rgb(204,255,230)","rgb(204,255,255)","rgb(204,230,255)","rgb(204,204,255)","rgb(230,204,255)","rgb(255,204,255)","rgb(255,153,153)","rgb(255,204,153)","rgb(255,255,153)","rgb(153,255,153)","rgb(153,255,204)","rgb(153,255,255)","rgb(153,204,255)","rgb(153,153,255)","rgb(204,153,255)","rgb(255,153,255)","rgb(255,102,102)","rgb(255,179,102)","rgb(255,255,102)","rgb(102,255,102)","rgb(102,255,179)","rgb(102,255,255)","rgb(102,179,255)","rgb(102,102,255)","rgb(179,102,255)","rgb(255,102,255)","rgb(255,51,51)","rgb(255,153,51)","rgb(255,255,51)","rgb(51,255,51)","rgb(51,255,153)","rgb(51,255,255)","rgb(51,153,255)","rgb(51,51,255)","rgb(153,51,255)","rgb(255,51,255)","rgb(255,0,0)","rgb(255,128,0)","rgb(255,255,0)","rgb(0,255,0)","rgb(0,255,128)","rgb(0,255,255)","rgb(0,128,255)","rgb(0,0,255)","rgb(128,0,255)","rgb(255,0,255)","rgb(245,0,0)","rgb(245,123,0)","rgb(245,245,0)","rgb(0,245,0)","rgb(0,245,123)","rgb(0,245,245)","rgb(0,123,245)","rgb(0,0,245)","rgb(123,0,245)","rgb(245,0,245)","rgb(214,0,0)","rgb(214,108,0)","rgb(214,214,0)","rgb(0,214,0)","rgb(0,214,108)","rgb(0,214,214)","rgb(0,108,214)","rgb(0,0,214)","rgb(108,0,214)","rgb(214,0,214)","rgb(163,0,0)","rgb(163,82,0)","rgb(163,163,0)","rgb(0,163,0)","rgb(0,163,82)","rgb(0,163,163)","rgb(0,82,163)","rgb(0,0,163)","rgb(82,0,163)","rgb(163,0,163)","rgb(92,0,0)","rgb(92,46,0)","rgb(92,92,0)","rgb(0,92,0)","rgb(0,92,46)","rgb(0,92,92)","rgb(0,46,92)","rgb(0,0,92)","rgb(46,0,92)","rgb(92,0,92)","rgb(255,255,255)","rgb(205,205,205)","rgb(178,178,178)","rgb(153,153,153)","rgb(127,127,127)","rgb(102,102,102)","rgb(76,76,76)","rgb(51,51,51)","rgb(25,25,25)","rgb(0,0,0)"],ei="M5 5 h10 v10 h-10 v-10 z",pf="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAAH0lEQVQoU2NkYGAwZkAFZ5G5jPRRgOYEVDeB3EBjBQBOZwTVugIGyAAAAABJRU5ErkJggg==",rh=te({name:"QColor",props:{...Ke,...Ht,modelValue:String,defaultValue:String,defaultView:{type:String,default:"spectrum",validator:e=>["spectrum","tune","palette"].includes(e)},formatModel:{type:String,default:"auto",validator:e=>["auto","hex","rgb","hexa","rgba"].includes(e)},palette:Array,noHeader:Boolean,noHeaderTabs:Boolean,noFooter:Boolean,square:Boolean,flat:Boolean,bordered:Boolean,disable:Boolean,readonly:Boolean},emits:["update:modelValue","change"],setup(e,{emit:t}){const{proxy:n}=ve(),{$q:l}=n,a=We(e,l),{getCache:o}=Wa(),i=O(null),u=O(null),d=s(()=>e.formatModel==="auto"?null:e.formatModel.indexOf("hex")!==-1),f=s(()=>e.formatModel==="auto"?null:e.formatModel.indexOf("a")!==-1),c=O(e.formatModel==="auto"?e.modelValue===void 0||e.modelValue===null||e.modelValue===""||e.modelValue.startsWith("#")?"hex":"rgb":e.formatModel.startsWith("hex")?"hex":"rgb"),h=O(e.defaultView),m=O(T(e.modelValue||e.defaultValue)),v=s(()=>e.disable!==!0&&e.readonly!==!0),g=s(()=>e.modelValue===void 0||e.modelValue===null||e.modelValue===""||e.modelValue.startsWith("#")),_=s(()=>d.value!==null?d.value:g.value),p=s(()=>({type:"hidden",name:e.name,value:m.value[_.value===!0?"hex":"rgb"]})),k=wn(p),w=s(()=>f.value!==null?f.value:m.value.a!==void 0),y=s(()=>({backgroundColor:m.value.rgb||"#000"})),b=s(()=>`q-color-picker__header-content q-color-picker__header-content--${(m.value.a!==void 0&&m.value.a<65?!0:as(m.value)>.4)?"light":"dark"}`),S=s(()=>({background:`hsl(${m.value.h},100%,50%)`})),B=s(()=>({top:`${100-m.value.v}%`,[l.lang.rtl===!0?"right":"left"]:`${m.value.s}%`})),L=s(()=>e.palette!==void 0&&e.palette.length!==0?e.palette:yf),R=s(()=>"q-color-picker"+(e.bordered===!0?" q-color-picker--bordered":"")+(e.square===!0?" q-color-picker--square no-border-radius":"")+(e.flat===!0?" q-color-picker--flat no-shadow":"")+(e.disable===!0?" disabled":"")+(a.value===!0?" q-color-picker--dark q-dark":"")),A=s(()=>e.disable===!0?{"aria-disabled":"true"}:{}),P=s(()=>[[Ft,j,void 0,{prevent:!0,stop:!0,mouse:!0}]]);ne(()=>e.modelValue,Y=>{const se=T(Y||e.defaultValue);se.hex!==m.value.hex&&(m.value=se)}),ne(()=>e.defaultValue,Y=>{if(!e.modelValue&&Y){const se=T(Y);se.hex!==m.value.hex&&(m.value=se)}});function z(Y,se){m.value.hex=da(Y),m.value.rgb=Jr(Y),m.value.r=Y.r,m.value.g=Y.g,m.value.b=Y.b,m.value.a=Y.a;const me=m.value[_.value===!0?"hex":"rgb"];t("update:modelValue",me),se===!0&&t("change",me)}function T(Y){const se=f.value!==void 0?f.value:e.formatModel==="auto"?null:e.formatModel.indexOf("a")!==-1;if(typeof Y!="string"||Y.length===0||Ba.anyColor(Y.replace(/ /g,""))!==!0)return{h:0,s:0,v:0,r:0,g:0,b:0,a:se===!0?100:void 0,hex:void 0,rgb:void 0};const me=un(Y);return se===!0&&me.a===void 0&&(me.a=100),me.hex=da(me),me.rgb=Jr(me),Object.assign(me,cl(me))}function x(Y,se,me){const we=i.value;if(we===null)return;const Me=we.clientWidth,ce=we.clientHeight,$e=we.getBoundingClientRect();let ze=Math.min(Me,Math.max(0,Y-$e.left));l.lang.rtl===!0&&(ze=Me-ze);const Oe=Math.min(ce,Math.max(0,se-$e.top)),re=Math.round(100*ze/Me),fe=Math.round(100*Math.max(0,Math.min(1,-(Oe/ce)+1))),G=Co({h:m.value.h,s:re,v:fe,a:w.value===!0?m.value.a:void 0});m.value.s=re,m.value.v=fe,z(G,me)}function q(Y,se){const me=Math.round(Y),we=Co({h:me,s:m.value.s,v:m.value.v,a:w.value===!0?m.value.a:void 0});m.value.h=me,z(we,se)}function U(Y){q(Y,!0)}function W(Y,se,me,we,Me){if(we!==void 0&&dt(we),!/^[0-9]+$/.test(Y)){Me===!0&&n.$forceUpdate();return}const ce=Math.floor(Number(Y));if(ce<0||ce>me){Me===!0&&n.$forceUpdate();return}const $e={r:se==="r"?ce:m.value.r,g:se==="g"?ce:m.value.g,b:se==="b"?ce:m.value.b,a:w.value===!0?se==="a"?ce:m.value.a:void 0};if(se!=="a"){const ze=cl($e);m.value.h=ze.h,m.value.s=ze.s,m.value.v=ze.v}if(z($e,Me),we!==void 0&&Me!==!0&&we.target.selectionEnd!==void 0){const ze=we.target.selectionEnd;Qe(()=>{we.target.setSelectionRange(ze,ze)})}}function E(Y,se){let me;const we=Y.target.value;if(dt(Y),c.value==="hex"){if(we.length!==(w.value===!0?9:7)||!/^#[0-9A-Fa-f]+$/.test(we))return!0;me=cr(we)}else{let ce;if(we.endsWith(")"))if(w.value!==!0&&we.startsWith("rgb(")){if(ce=we.substring(4,we.length-1).split(",").map($e=>parseInt($e,10)),ce.length!==3||!/^rgb\([0-9]{1,3},[0-9]{1,3},[0-9]{1,3}\)$/.test(we))return!0}else if(w.value===!0&&we.startsWith("rgba(")){if(ce=we.substring(5,we.length-1).split(","),ce.length!==4||!/^rgba\([0-9]{1,3},[0-9]{1,3},[0-9]{1,3},(0|0\.[0-9]+[1-9]|0\.[1-9]+|1)\)$/.test(we))return!0;for(let ze=0;ze<3;ze++){const Oe=parseInt(ce[ze],10);if(Oe<0||Oe>255)return!0;ce[ze]=Oe}const $e=parseFloat(ce[3]);if($e<0||$e>1)return!0;ce[3]=$e}else return!0;else return!0;if(ce[0]<0||ce[0]>255||ce[1]<0||ce[1]>255||ce[2]<0||ce[2]>255||w.value===!0&&(ce[3]<0||ce[3]>1))return!0;me={r:ce[0],g:ce[1],b:ce[2],a:w.value===!0?ce[3]*100:void 0}}const Me=cl(me);if(m.value.h=Me.h,m.value.s=Me.s,m.value.v=Me.v,z(me,se),se!==!0){const ce=Y.target.selectionEnd;Qe(()=>{Y.target.setSelectionRange(ce,ce)})}}function V(Y){const se=T(Y),me={r:se.r,g:se.g,b:se.b,a:se.a};me.a===void 0&&(me.a=m.value.a),m.value.h=se.h,m.value.s=se.s,m.value.v=se.v,z(me,!0)}function j(Y){Y.isFinal?x(Y.position.left,Y.position.top,!0):ue(Y)}const ue=Cu(Y=>{x(Y.position.left,Y.position.top)},20);function I(Y){x(Y.pageX-window.pageXOffset,Y.pageY-window.pageYOffset,!0)}function C(Y){x(Y.pageX-window.pageXOffset,Y.pageY-window.pageYOffset)}function K(Y){u.value!==null&&(u.value.$el.style.opacity=Y?1:0)}function Q(Y){c.value=Y}function oe(){const Y=[];return e.noHeaderTabs!==!0&&Y.push(r(Yr,{class:"q-color-picker__header-tabs",modelValue:c.value,dense:!0,align:"justify","onUpdate:modelValue":Q},()=>[r(ya,{label:"HEX"+(w.value===!0?"A":""),name:"hex",ripple:!1}),r(ya,{label:"RGB"+(w.value===!0?"A":""),name:"rgb",ripple:!1})])),Y.push(r("div",{class:"q-color-picker__header-banner row flex-center no-wrap"},[r("input",{class:"fit",value:m.value[c.value],...v.value!==!0?{readonly:!0}:{},...o("topIn",{onInput:se=>{K(E(se)===!0)},onChange:dt,onBlur:se=>{E(se,!0)===!0&&n.$forceUpdate(),K(!1)}})}),r(je,{ref:u,class:"q-color-picker__error-icon absolute no-pointer-events",name:l.iconSet.type.negative})])),r("div",{class:"q-color-picker__header relative-position overflow-hidden"},[r("div",{class:"q-color-picker__header-bg absolute-full"}),r("div",{class:b.value,style:y.value},Y)])}function M(){return r(df,{modelValue:h.value,animated:!0},()=>[r(Jl,{class:"q-color-picker__spectrum-tab overflow-hidden",name:"spectrum"},H),r(Jl,{class:"q-pa-md q-color-picker__tune-tab",name:"tune"},de),r(Jl,{class:"q-color-picker__palette-tab",name:"palette"},xe)])}function D(Y){h.value=Y}function le(){return r("div",{class:"q-color-picker__footer relative-position overflow-hidden"},[r(Yr,{class:"absolute-full",modelValue:h.value,dense:!0,align:"justify","onUpdate:modelValue":D},()=>[r(ya,{icon:l.iconSet.colorPicker.spectrum,name:"spectrum",ripple:!1}),r(ya,{icon:l.iconSet.colorPicker.tune,name:"tune",ripple:!1}),r(ya,{icon:l.iconSet.colorPicker.palette,name:"palette",ripple:!1})])])}function H(){const Y={ref:i,class:"q-color-picker__spectrum non-selectable relative-position cursor-pointer"+(v.value!==!0?" readonly":""),style:S.value,...v.value===!0?{onClick:I,onMousedown:C}:{}},se=[r("div",{style:{paddingBottom:"100%"}}),r("div",{class:"q-color-picker__spectrum-white absolute-full"}),r("div",{class:"q-color-picker__spectrum-black absolute-full"}),r("div",{class:"absolute",style:B.value},[m.value.hex!==void 0?r("div",{class:"q-color-picker__spectrum-circle"}):null])],me=[r(Yn,{class:"q-color-picker__hue non-selectable",modelValue:m.value.h,min:0,max:360,trackSize:"8px",innerTrackColor:"transparent",selectionColor:"transparent",readonly:v.value!==!0,thumbPath:ei,"onUpdate:modelValue":q,onChange:U})];return w.value===!0&&me.push(r(Yn,{class:"q-color-picker__alpha non-selectable",modelValue:m.value.a,min:0,max:100,trackSize:"8px",trackColor:"white",innerTrackColor:"transparent",selectionColor:"transparent",trackImg:pf,readonly:v.value!==!0,hideSelection:!0,thumbPath:ei,...o("alphaSlide",{"onUpdate:modelValue":we=>W(we,"a",100),onChange:we=>W(we,"a",100,void 0,!0)})})),[Dt("div",Y,se,"spec",v.value,()=>P.value),r("div",{class:"q-color-picker__sliders"},me)]}function de(){return[r("div",{class:"row items-center no-wrap"},[r("div","R"),r(Yn,{modelValue:m.value.r,min:0,max:255,color:"red",dark:a.value,readonly:v.value!==!0,...o("rSlide",{"onUpdate:modelValue":Y=>W(Y,"r",255),onChange:Y=>W(Y,"r",255,void 0,!0)})}),r("input",{value:m.value.r,maxlength:3,readonly:v.value!==!0,onChange:dt,...o("rIn",{onInput:Y=>W(Y.target.value,"r",255,Y),onBlur:Y=>W(Y.target.value,"r",255,Y,!0)})})]),r("div",{class:"row items-center no-wrap"},[r("div","G"),r(Yn,{modelValue:m.value.g,min:0,max:255,color:"green",dark:a.value,readonly:v.value!==!0,...o("gSlide",{"onUpdate:modelValue":Y=>W(Y,"g",255),onChange:Y=>W(Y,"g",255,void 0,!0)})}),r("input",{value:m.value.g,maxlength:3,readonly:v.value!==!0,onChange:dt,...o("gIn",{onInput:Y=>W(Y.target.value,"g",255,Y),onBlur:Y=>W(Y.target.value,"g",255,Y,!0)})})]),r("div",{class:"row items-center no-wrap"},[r("div","B"),r(Yn,{modelValue:m.value.b,min:0,max:255,color:"blue",readonly:v.value!==!0,dark:a.value,...o("bSlide",{"onUpdate:modelValue":Y=>W(Y,"b",255),onChange:Y=>W(Y,"b",255,void 0,!0)})}),r("input",{value:m.value.b,maxlength:3,readonly:v.value!==!0,onChange:dt,...o("bIn",{onInput:Y=>W(Y.target.value,"b",255,Y),onBlur:Y=>W(Y.target.value,"b",255,Y,!0)})})]),w.value===!0?r("div",{class:"row items-center no-wrap"},[r("div","A"),r(Yn,{modelValue:m.value.a,color:"grey",readonly:v.value!==!0,dark:a.value,...o("aSlide",{"onUpdate:modelValue":Y=>W(Y,"a",100),onChange:Y=>W(Y,"a",100,void 0,!0)})}),r("input",{value:m.value.a,maxlength:3,readonly:v.value!==!0,onChange:dt,...o("aIn",{onInput:Y=>W(Y.target.value,"a",100,Y),onBlur:Y=>W(Y.target.value,"a",100,Y,!0)})})]):null]}function xe(){const Y=se=>r("div",{class:"q-color-picker__cube col-auto",style:{backgroundColor:se},...v.value===!0?o("palette#"+se,{onClick:()=>{V(se)}}):{}});return[r("div",{class:"row items-center q-color-picker__palette-rows"+(v.value===!0?" q-color-picker__palette-rows--editable":"")},L.value.map(Y))]}return()=>{const Y=[M()];return e.name!==void 0&&e.disable!==!0&&k(Y,"push"),e.noHeader!==!0&&Y.unshift(oe()),e.noFooter!==!0&&Y.push(le()),r("div",{class:R.value,...A.value},Y)}}}),mn=[-61,9,38,199,426,686,756,818,1111,1181,1210,1635,2060,2097,2192,2262,2324,2394,2456,3178];function wf(e,t,n){return Object.prototype.toString.call(e)==="[object Date]"&&(n=e.getDate(),t=e.getMonth()+1,e=e.getFullYear()),Cf(dr(e,t,n))}function ti(e,t,n){return os(xf(e,t,n))}function _f(e){return Sf(e)===0}function dl(e,t){return t<=6?31:t<=11||_f(e)?30:29}function Sf(e){const t=mn.length;let n=mn[0],l,a,o,i,u;if(e<n||e>=mn[t-1])throw new Error("Invalid Jalaali year "+e);for(u=1;u<t&&(l=mn[u],a=l-n,!(e<l));u+=1)n=l;return i=e-n,a-i<6&&(i=i-a+ct(a+4,33)*33),o=Lt(Lt(i+1,33)-1,4),o===-1&&(o=4),o}function ls(e,t){const n=mn.length,l=e+621;let a=-14,o=mn[0],i,u,d,f,c;if(e<o||e>=mn[n-1])throw new Error("Invalid Jalaali year "+e);for(c=1;c<n&&(i=mn[c],u=i-o,!(e<i));c+=1)a=a+ct(u,33)*8+ct(Lt(u,33),4),o=i;f=e-o,a=a+ct(f,33)*8+ct(Lt(f,33)+3,4),Lt(u,33)===4&&u-f===4&&(a+=1);const h=ct(l,4)-ct((ct(l,100)+1)*3,4)-150,m=20+a-h;return t||(u-f<6&&(f=f-u+ct(u+4,33)*33),d=Lt(Lt(f+1,33)-1,4),d===-1&&(d=4)),{leap:d,gy:l,march:m}}function xf(e,t,n){const l=ls(e,!0);return dr(l.gy,3,l.march)+(t-1)*31-ct(t,7)*(t-7)+n-1}function Cf(e){const t=os(e).gy;let n=t-621,l,a,o;const i=ls(n,!1),u=dr(t,3,i.march);if(o=e-u,o>=0){if(o<=185)return a=1+ct(o,31),l=Lt(o,31)+1,{jy:n,jm:a,jd:l};o-=186}else n-=1,o+=179,i.leap===1&&(o+=1);return a=7+ct(o,30),l=Lt(o,30)+1,{jy:n,jm:a,jd:l}}function dr(e,t,n){let l=ct((e+ct(t-8,6)+100100)*1461,4)+ct(153*Lt(t+9,12)+2,5)+n-34840408;return l=l-ct(ct(e+100100+ct(t-8,6),100)*3,4)+752,l}function os(e){let t=4*e+139361631;t=t+ct(ct(4*e+183187720,146097)*3,4)*4-3908;const n=ct(Lt(t,1461),4)*5+308,l=ct(Lt(n,153),5)+1,a=Lt(ct(n,153),12)+1;return{gy:ct(t,1461)-100100+ct(8-a,6),gm:a,gd:l}}function ct(e,t){return~~(e/t)}function Lt(e,t){return e-~~(e/t)*t}var kf=["gregorian","persian"],Ml={mask:{type:String},locale:Object,calendar:{type:String,validator:e=>kf.includes(e),default:"gregorian"},landscape:Boolean,color:String,textColor:String,square:Boolean,flat:Boolean,bordered:Boolean,readonly:Boolean,disable:Boolean},rs=["update:modelValue"];function nn(e){return e.year+"/"+Ye(e.month)+"/"+Ye(e.day)}function is(e,t){const n=s(()=>e.disable!==!0&&e.readonly!==!0),l=s(()=>n.value===!0?0:-1),a=s(()=>{const u=[];return e.color!==void 0&&u.push(`bg-${e.color}`),e.textColor!==void 0&&u.push(`text-${e.textColor}`),u.join(" ")});function o(){return e.locale!==void 0?{...t.lang.date,...e.locale}:t.lang.date}function i(u){const d=new Date,f=u===!0?null:0;if(e.calendar==="persian"){const c=wf(d);return{year:c.jy,month:c.jm,day:c.jd}}return{year:d.getFullYear(),month:d.getMonth()+1,day:d.getDate(),hour:f,minute:f,second:f,millisecond:f}}return{editable:n,tabindex:l,headerClass:a,getLocale:o,getCurrentDate:i}}var us=864e5,qf=36e5,ko=6e4,ss="YYYY-MM-DDTHH:mm:ss.SSSZ",Tf=/\[((?:[^\]\\]|\\]|\\)*)\]|d{1,4}|M{1,4}|m{1,2}|w{1,2}|Qo|Do|D{1,4}|YY(?:YY)?|H{1,2}|h{1,2}|s{1,2}|S{1,3}|Z{1,2}|a{1,2}|[AQExX]/g,Mf=/(\[[^\]]*\])|d{1,4}|M{1,4}|m{1,2}|w{1,2}|Qo|Do|D{1,4}|YY(?:YY)?|H{1,2}|h{1,2}|s{1,2}|S{1,3}|Z{1,2}|a{1,2}|[AQExX]|([.*+:?^,\s${}()|\\]+)/g,eo={};function $f(e,t){const n="("+t.days.join("|")+")",l=e+n;if(eo[l]!==void 0)return eo[l];const a="("+t.daysShort.join("|")+")",o="("+t.months.join("|")+")",i="("+t.monthsShort.join("|")+")",u={};let d=0;const f=e.replace(Mf,h=>{switch(d++,h){case"YY":return u.YY=d,"(-?\\d{1,2})";case"YYYY":return u.YYYY=d,"(-?\\d{1,4})";case"M":return u.M=d,"(\\d{1,2})";case"MM":return u.M=d,"(\\d{2})";case"MMM":return u.MMM=d,i;case"MMMM":return u.MMMM=d,o;case"D":return u.D=d,"(\\d{1,2})";case"Do":return u.D=d++,"(\\d{1,2}(st|nd|rd|th))";case"DD":return u.D=d,"(\\d{2})";case"H":return u.H=d,"(\\d{1,2})";case"HH":return u.H=d,"(\\d{2})";case"h":return u.h=d,"(\\d{1,2})";case"hh":return u.h=d,"(\\d{2})";case"m":return u.m=d,"(\\d{1,2})";case"mm":return u.m=d,"(\\d{2})";case"s":return u.s=d,"(\\d{1,2})";case"ss":return u.s=d,"(\\d{2})";case"S":return u.S=d,"(\\d{1})";case"SS":return u.S=d,"(\\d{2})";case"SSS":return u.S=d,"(\\d{3})";case"A":return u.A=d,"(AM|PM)";case"a":return u.a=d,"(am|pm)";case"aa":return u.aa=d,"(a\\.m\\.|p\\.m\\.)";case"ddd":return a;case"dddd":return n;case"Q":case"d":case"E":return"(\\d{1})";case"Qo":return"(1st|2nd|3rd|4th)";case"DDD":case"DDDD":return"(\\d{1,3})";case"w":return"(\\d{1,2})";case"ww":return"(\\d{2})";case"Z":return u.Z=d,"(Z|[+-]\\d{2}:\\d{2})";case"ZZ":return u.ZZ=d,"(Z|[+-]\\d{2}\\d{2})";case"X":return u.X=d,"(-?\\d+)";case"x":return u.x=d,"(-?\\d{4,})";default:return d--,h[0]==="["&&(h=h.substring(1,h.length-1)),h.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}}),c={map:u,regex:new RegExp("^"+f)};return eo[l]=c,c}function cs(e,t){return e!==void 0?e:t!==void 0?t.date:bo.date}function ni(e,t=""){const n=e>0?"-":"+",l=Math.abs(e),a=Math.floor(l/60),o=l%60;return n+Ye(a)+t+Ye(o)}function Bf(e,t,n){let l=e.getFullYear(),a=e.getMonth();const o=e.getDate();return t.year!==void 0&&(l+=n*t.year,delete t.year),t.month!==void 0&&(a+=n*t.month,delete t.month),e.setDate(1),e.setMonth(2),e.setFullYear(l),e.setMonth(a),e.setDate(Math.min(o,mr(e))),t.date!==void 0&&(e.setDate(e.getDate()+n*t.date),delete t.date),e}function Pf(e,t,n){const l=t.year!==void 0?t.year:e[`get${n}FullYear`](),a=t.month!==void 0?t.month-1:e[`get${n}Month`](),o=new Date(l,a+1,0).getDate(),i=Math.min(o,t.date!==void 0?t.date:e[`get${n}Date`]());return e[`set${n}Date`](1),e[`set${n}Month`](2),e[`set${n}FullYear`](l),e[`set${n}Month`](a),e[`set${n}Date`](i),delete t.year,delete t.month,delete t.date,e}function fr(e,t,n){const l=ds(t),a=new Date(e),o=l.year!==void 0||l.month!==void 0||l.date!==void 0?Bf(a,l,n):a;for(const i in l){const u=gu(i);o[`set${u}`](o[`get${u}`]()+n*l[i])}return o}function ds(e){const t={...e};return e.years!==void 0&&(t.year=e.years,delete t.years),e.months!==void 0&&(t.month=e.months,delete t.months),e.days!==void 0&&(t.date=e.days,delete t.days),e.day!==void 0&&(t.date=e.day,delete t.day),e.hour!==void 0&&(t.hours=e.hour,delete t.hour),e.minute!==void 0&&(t.minutes=e.minute,delete t.minute),e.second!==void 0&&(t.seconds=e.second,delete t.second),e.millisecond!==void 0&&(t.milliseconds=e.millisecond,delete t.millisecond),t}function fs(e,t,n){const l=ds(t),a=n===!0?"UTC":"",o=new Date(e),i=l.year!==void 0||l.month!==void 0||l.date!==void 0?Pf(o,l,a):o;for(const u in l){const d=u.charAt(0).toUpperCase()+u.slice(1);i[`set${a}${d}`](l[u])}return i}function Ef(e,t,n){const l=aa(e,t,n),a=new Date(l.year,l.month===null?null:l.month-1,l.day===null?1:l.day,l.hour,l.minute,l.second,l.millisecond),o=a.getTimezoneOffset();return l.timezoneOffset===null||l.timezoneOffset===o?a:fr(a,{minutes:l.timezoneOffset-o},1)}function aa(e,t,n,l,a){const o={year:null,month:null,day:null,hour:null,minute:null,second:null,millisecond:null,timezoneOffset:null,dateHash:null,timeHash:null};if(a!==void 0&&Object.assign(o,a),e==null||e===""||typeof e!="string")return o;t===void 0&&(t=ss);const i=cs(n,Ll.props),u=i.months,d=i.monthsShort,{regex:f,map:c}=$f(t,i),h=e.match(f);if(h===null)return o;let m="";if(c.X!==void 0||c.x!==void 0){const v=parseInt(h[c.X!==void 0?c.X:c.x],10);if(isNaN(v)===!0||v<0)return o;const g=new Date(v*(c.X!==void 0?1e3:1));o.year=g.getFullYear(),o.month=g.getMonth()+1,o.day=g.getDate(),o.hour=g.getHours(),o.minute=g.getMinutes(),o.second=g.getSeconds(),o.millisecond=g.getMilliseconds()}else{if(c.YYYY!==void 0)o.year=parseInt(h[c.YYYY],10);else if(c.YY!==void 0){const v=parseInt(h[c.YY],10);o.year=v<0?v:2e3+v}if(c.M!==void 0){if(o.month=parseInt(h[c.M],10),o.month<1||o.month>12)return o}else c.MMM!==void 0?o.month=d.indexOf(h[c.MMM])+1:c.MMMM!==void 0&&(o.month=u.indexOf(h[c.MMMM])+1);if(c.D!==void 0){if(o.day=parseInt(h[c.D],10),o.year===null||o.month===null||o.day<1)return o;const v=l!=="persian"?new Date(o.year,o.month,0).getDate():dl(o.year,o.month);if(o.day>v)return o}c.H!==void 0?o.hour=parseInt(h[c.H],10)%24:c.h!==void 0&&(o.hour=parseInt(h[c.h],10)%12,(c.A&&h[c.A]==="PM"||c.a&&h[c.a]==="pm"||c.aa&&h[c.aa]==="p.m.")&&(o.hour+=12),o.hour=o.hour%24),c.m!==void 0&&(o.minute=parseInt(h[c.m],10)%60),c.s!==void 0&&(o.second=parseInt(h[c.s],10)%60),c.S!==void 0&&(o.millisecond=parseInt(h[c.S],10)*10**(3-h[c.S].length)),(c.Z!==void 0||c.ZZ!==void 0)&&(m=c.Z!==void 0?h[c.Z].replace(":",""):h[c.ZZ],o.timezoneOffset=(m[0]==="+"?-1:1)*(60*m.slice(1,3)+1*m.slice(3,5)))}return o.dateHash=Ye(o.year,6)+"/"+Ye(o.month)+"/"+Ye(o.day),o.timeHash=Ye(o.hour)+":"+Ye(o.minute)+":"+Ye(o.second)+m,o}function Lf(e){return typeof e=="number"?!0:isNaN(Date.parse(e))===!1}function Af(e,t){return fs(new Date,e,t)}function zf(e){const t=new Date(e).getDay();return t===0?7:t}function qo(e){const t=new Date(e.getFullYear(),e.getMonth(),e.getDate());t.setDate(t.getDate()-(t.getDay()+6)%7+3);const n=new Date(t.getFullYear(),0,4);n.setDate(n.getDate()-(n.getDay()+6)%7+3);const l=t.getTimezoneOffset()-n.getTimezoneOffset();t.setHours(t.getHours()-l);const a=(t-n)/(us*7);return 1+Math.floor(a)}function Of(e){return e.getFullYear()*1e4+e.getMonth()*100+e.getDate()}function to(e,t){const n=new Date(e);return t===!0?Of(n):n.getTime()}function Rf(e,t,n,l={}){const a=to(t,l.onlyDate),o=to(n,l.onlyDate),i=to(e,l.onlyDate);return(i>a||l.inclusiveFrom===!0&&i===a)&&(i<o||l.inclusiveTo===!0&&i===o)}function Ff(e,t){return fr(e,t,1)}function Vf(e,t){return fr(e,t,-1)}function Jt(e,t,n){const l=new Date(e),a=`set${n===!0?"UTC":""}`;switch(t){case"year":case"years":l[`${a}Month`](0);case"month":case"months":l[`${a}Date`](1);case"day":case"days":case"date":l[`${a}Hours`](0);case"hour":case"hours":l[`${a}Minutes`](0);case"minute":case"minutes":l[`${a}Seconds`](0);case"second":case"seconds":l[`${a}Milliseconds`](0)}return l}function Df(e,t,n){const l=new Date(e),a=`set${n===!0?"UTC":""}`;switch(t){case"year":case"years":l[`${a}Month`](11);case"month":case"months":l[`${a}Date`](mr(l));case"day":case"days":case"date":l[`${a}Hours`](23);case"hour":case"hours":l[`${a}Minutes`](59);case"minute":case"minutes":l[`${a}Seconds`](59);case"second":case"seconds":l[`${a}Milliseconds`](999)}return l}function If(e){let t=new Date(e);return Array.prototype.slice.call(arguments,1).forEach(n=>{t=Math.max(t,new Date(n))}),t}function Hf(e){let t=new Date(e);return Array.prototype.slice.call(arguments,1).forEach(n=>{t=Math.min(t,new Date(n))}),t}function Za(e,t,n){return(e.getTime()-e.getTimezoneOffset()*ko-(t.getTime()-t.getTimezoneOffset()*ko))/n}function vr(e,t,n="days"){const l=new Date(e),a=new Date(t);switch(n){case"years":case"year":return l.getFullYear()-a.getFullYear();case"months":case"month":return(l.getFullYear()-a.getFullYear())*12+l.getMonth()-a.getMonth();case"days":case"day":case"date":return Za(Jt(l,"day"),Jt(a,"day"),us);case"hours":case"hour":return Za(Jt(l,"hour"),Jt(a,"hour"),qf);case"minutes":case"minute":return Za(Jt(l,"minute"),Jt(a,"minute"),ko);case"seconds":case"second":return Za(Jt(l,"second"),Jt(a,"second"),1e3)}}function To(e){return vr(e,Jt(e,"year"),"days")+1}function Nf(e){return sa(e)===!0?"date":typeof e=="number"?"number":"string"}function Qf(e,t,n){const l=new Date(e);if(t){const a=new Date(t);if(l<a)return a}if(n){const a=new Date(n);if(l>a)return a}return l}function jf(e,t,n){const l=new Date(e),a=new Date(t);if(n===void 0)return l.getTime()===a.getTime();switch(n){case"second":case"seconds":if(l.getSeconds()!==a.getSeconds())return!1;case"minute":case"minutes":if(l.getMinutes()!==a.getMinutes())return!1;case"hour":case"hours":if(l.getHours()!==a.getHours())return!1;case"day":case"days":case"date":if(l.getDate()!==a.getDate())return!1;case"month":case"months":if(l.getMonth()!==a.getMonth())return!1;case"year":case"years":if(l.getFullYear()!==a.getFullYear())return!1;break;default:throw new Error(`date isSameDate unknown unit ${n}`)}return!0}function mr(e){return new Date(e.getFullYear(),e.getMonth()+1,0).getDate()}function ai(e){if(e>=11&&e<=13)return`${e}th`;switch(e%10){case 1:return`${e}st`;case 2:return`${e}nd`;case 3:return`${e}rd`}return`${e}th`}var li={YY(e,t,n){const l=this.YYYY(e,t,n)%100;return l>=0?Ye(l):"-"+Ye(Math.abs(l))},YYYY(e,t,n){return n!=null?n:e.getFullYear()},M(e){return e.getMonth()+1},MM(e){return Ye(e.getMonth()+1)},MMM(e,t){return t.monthsShort[e.getMonth()]},MMMM(e,t){return t.months[e.getMonth()]},Q(e){return Math.ceil((e.getMonth()+1)/3)},Qo(e){return ai(this.Q(e))},D(e){return e.getDate()},Do(e){return ai(e.getDate())},DD(e){return Ye(e.getDate())},DDD(e){return To(e)},DDDD(e){return Ye(To(e),3)},d(e){return e.getDay()},dd(e,t){return this.dddd(e,t).slice(0,2)},ddd(e,t){return t.daysShort[e.getDay()]},dddd(e,t){return t.days[e.getDay()]},E(e){return e.getDay()||7},w(e){return qo(e)},ww(e){return Ye(qo(e))},H(e){return e.getHours()},HH(e){return Ye(e.getHours())},h(e){const t=e.getHours();return t===0?12:t>12?t%12:t},hh(e){return Ye(this.h(e))},m(e){return e.getMinutes()},mm(e){return Ye(e.getMinutes())},s(e){return e.getSeconds()},ss(e){return Ye(e.getSeconds())},S(e){return Math.floor(e.getMilliseconds()/100)},SS(e){return Ye(Math.floor(e.getMilliseconds()/10))},SSS(e){return Ye(e.getMilliseconds(),3)},A(e){return this.H(e)<12?"AM":"PM"},a(e){return this.H(e)<12?"am":"pm"},aa(e){return this.H(e)<12?"a.m.":"p.m."},Z(e,t,n,l){const a=l==null?e.getTimezoneOffset():l;return ni(a,":")},ZZ(e,t,n,l){const a=l==null?e.getTimezoneOffset():l;return ni(a)},X(e){return Math.floor(e.getTime()/1e3)},x(e){return e.getTime()}};function gr(e,t,n,l,a){if(e!==0&&!e||e===1/0||e===-1/0)return;const o=new Date(e);if(isNaN(o))return;t===void 0&&(t=ss);const i=cs(n,Ll.props);return t.replace(Tf,(u,d)=>u in li?li[u](o,i,l,a):d===void 0?u:d.split("\\]").join("]"))}function Kf(e){return sa(e)===!0?new Date(e.getTime()):e}var ih={isValid:Lf,extractDate:Ef,buildDate:Af,getDayOfWeek:zf,getWeekOfYear:qo,isBetweenDates:Rf,addToDate:Ff,subtractFromDate:Vf,adjustDate:fs,startOfDate:Jt,endOfDate:Df,getMaxDate:If,getMinDate:Hf,getDateDiff:vr,getDayOfYear:To,inferDateFormat:Nf,getDateBetween:Qf,isSameDate:jf,daysInMonth:mr,formatDate:gr,clone:Kf},Sn=20,Wf=["Calendar","Years","Months"],oi=e=>Wf.includes(e),no=e=>/^-?[\d]+\/[0-1]\d$/.test(e),Xn=" \u2014 ";function dn(e){return e.year+"/"+Ye(e.month)}var uh=te({name:"QDate",props:{...Ml,...Ht,...Ke,modelValue:{required:!0,validator:e=>typeof e=="string"||Array.isArray(e)===!0||Object(e)===e||e===null},multiple:Boolean,range:Boolean,title:String,subtitle:String,mask:{...Ml.mask,default:"YYYY/MM/DD"},defaultYearMonth:{type:String,validator:no},yearsInMonthView:Boolean,events:[Array,Function],eventColor:[String,Function],emitImmediately:Boolean,options:[Array,Function],navigationMinYearMonth:{type:String,validator:no},navigationMaxYearMonth:{type:String,validator:no},noUnset:Boolean,firstDayOfWeek:[String,Number],todayBtn:Boolean,minimal:Boolean,defaultView:{type:String,default:"Calendar",validator:oi}},emits:[...rs,"rangeStart","rangeEnd","navigation"],setup(e,{slots:t,emit:n}){const{proxy:l}=ve(),{$q:a}=l,o=We(e,a),{getCache:i}=Wa(),{tabindex:u,headerClass:d,getLocale:f,getCurrentDate:c}=is(e,a);let h;const m=Ka(e),v=wn(m),g=O(null),_=O(Ge()),p=O(f()),k=s(()=>Ge()),w=s(()=>f()),y=s(()=>c()),b=O(at(_.value,p.value)),S=O(e.defaultView),B=s(()=>a.lang.rtl===!0?"right":"left"),L=O(B.value),R=O(B.value),A=b.value.year,P=O(A-A%Sn-(A<0?Sn:0)),z=O(null),T=s(()=>{const $=e.landscape===!0?"landscape":"portrait";return`q-date q-date--${$} q-date--${$}-${e.minimal===!0?"minimal":"standard"}`+(o.value===!0?" q-date--dark q-dark":"")+(e.bordered===!0?" q-date--bordered":"")+(e.square===!0?" q-date--square no-border-radius":"")+(e.flat===!0?" q-date--flat no-shadow":"")+(e.disable===!0?" disabled":e.readonly===!0?" q-date--readonly":"")}),x=s(()=>e.color||"primary"),q=s(()=>e.textColor||"white"),U=s(()=>e.emitImmediately===!0&&e.multiple!==!0&&e.range!==!0),W=s(()=>Array.isArray(e.modelValue)===!0?e.modelValue:e.modelValue!==null&&e.modelValue!==void 0?[e.modelValue]:[]),E=s(()=>W.value.filter($=>typeof $=="string").map($=>nt($,_.value,p.value)).filter($=>$.dateHash!==null&&$.day!==null&&$.month!==null&&$.year!==null)),V=s(()=>{const $=N=>nt(N,_.value,p.value);return W.value.filter(N=>yt(N)===!0&&N.from!==void 0&&N.to!==void 0).map(N=>({from:$(N.from),to:$(N.to)})).filter(N=>N.from.dateHash!==null&&N.to.dateHash!==null&&N.from.dateHash<N.to.dateHash)}),j=s(()=>e.calendar!=="persian"?$=>new Date($.year,$.month-1,$.day):$=>{const N=ti($.year,$.month,$.day);return new Date(N.gy,N.gm-1,N.gd)}),ue=s(()=>e.calendar==="persian"?nn:($,N,Z)=>gr(new Date($.year,$.month-1,$.day,$.hour,$.minute,$.second,$.millisecond),N===void 0?_.value:N,Z===void 0?p.value:Z,$.year,$.timezoneOffset)),I=s(()=>E.value.length+V.value.reduce(($,N)=>$+1+vr(j.value(N.to),j.value(N.from)),0)),C=s(()=>{if(e.title!==void 0&&e.title!==null&&e.title.length!==0)return e.title;if(z.value!==null){const Z=z.value.init,ge=j.value(Z);return p.value.daysShort[ge.getDay()]+", "+p.value.monthsShort[Z.month-1]+" "+Z.day+Xn+"?"}if(I.value===0)return Xn;if(I.value>1)return`${I.value} ${p.value.pluralDay}`;const $=E.value[0],N=j.value($);return isNaN(N.valueOf())===!0?Xn:p.value.headerTitle!==void 0?p.value.headerTitle(N,$):p.value.daysShort[N.getDay()]+", "+p.value.monthsShort[$.month-1]+" "+$.day}),K=s(()=>E.value.concat(V.value.map(N=>N.from)).sort((N,Z)=>N.year-Z.year||N.month-Z.month)[0]),Q=s(()=>E.value.concat(V.value.map(N=>N.to)).sort((N,Z)=>Z.year-N.year||Z.month-N.month)[0]),oe=s(()=>{if(e.subtitle!==void 0&&e.subtitle!==null&&e.subtitle.length!==0)return e.subtitle;if(I.value===0)return Xn;if(I.value>1){const $=K.value,N=Q.value,Z=p.value.monthsShort;return Z[$.month-1]+($.year!==N.year?" "+$.year+Xn+Z[N.month-1]+" ":$.month!==N.month?Xn+Z[N.month-1]:"")+" "+N.year}return E.value[0].year}),M=s(()=>{const $=[a.iconSet.datetime.arrowLeft,a.iconSet.datetime.arrowRight];return a.lang.rtl===!0?$.reverse():$}),D=s(()=>e.firstDayOfWeek!==void 0?Number(e.firstDayOfWeek):p.value.firstDayOfWeek),le=s(()=>{const $=p.value.daysShort,N=D.value;return N>0?$.slice(N,7).concat($.slice(0,N)):$}),H=s(()=>{const $=b.value;return e.calendar!=="persian"?new Date($.year,$.month,0).getDate():dl($.year,$.month)}),de=s(()=>typeof e.eventColor=="function"?e.eventColor:()=>e.eventColor),xe=s(()=>{if(e.navigationMinYearMonth===void 0)return null;const $=e.navigationMinYearMonth.split("/");return{year:parseInt($[0],10),month:parseInt($[1],10)}}),Y=s(()=>{if(e.navigationMaxYearMonth===void 0)return null;const $=e.navigationMaxYearMonth.split("/");return{year:parseInt($[0],10),month:parseInt($[1],10)}}),se=s(()=>{const $={month:{prev:!0,next:!0},year:{prev:!0,next:!0}};return xe.value!==null&&xe.value.year>=b.value.year&&($.year.prev=!1,xe.value.year===b.value.year&&xe.value.month>=b.value.month&&($.month.prev=!1)),Y.value!==null&&Y.value.year<=b.value.year&&($.year.next=!1,Y.value.year===b.value.year&&Y.value.month<=b.value.month&&($.month.next=!1)),$}),me=s(()=>{const $={};return E.value.forEach(N=>{const Z=dn(N);$[Z]===void 0&&($[Z]=[]),$[Z].push(N.day)}),$}),we=s(()=>{const $={};return V.value.forEach(N=>{const Z=dn(N.from),ge=dn(N.to);if($[Z]===void 0&&($[Z]=[]),$[Z].push({from:N.from.day,to:Z===ge?N.to.day:void 0,range:N}),Z<ge){let he;const{year:Ue,month:Te}=N.from,Ie=Te<12?{year:Ue,month:Te+1}:{year:Ue+1,month:1};for(;(he=dn(Ie))<=ge;)$[he]===void 0&&($[he]=[]),$[he].push({from:void 0,to:he===ge?N.to.day:void 0,range:N}),Ie.month++,Ie.month>12&&(Ie.year++,Ie.month=1)}}),$}),Me=s(()=>{if(z.value===null)return;const{init:$,initHash:N,final:Z,finalHash:ge}=z.value,[he,Ue]=N<=ge?[$,Z]:[Z,$],Te=dn(he),Ie=dn(Ue);if(Te!==ce.value&&Ie!==ce.value)return;const Ze={};return Te===ce.value?(Ze.from=he.day,Ze.includeFrom=!0):Ze.from=1,Ie===ce.value?(Ze.to=Ue.day,Ze.includeTo=!0):Ze.to=H.value,Ze}),ce=s(()=>dn(b.value)),$e=s(()=>{const $={};if(e.options===void 0){for(let Z=1;Z<=H.value;Z++)$[Z]=!0;return $}const N=typeof e.options=="function"?e.options:Z=>e.options.includes(Z);for(let Z=1;Z<=H.value;Z++){const ge=ce.value+"/"+Ye(Z);$[Z]=N(ge)}return $}),ze=s(()=>{const $={};if(e.events===void 0)for(let N=1;N<=H.value;N++)$[N]=!1;else{const N=typeof e.events=="function"?e.events:Z=>e.events.includes(Z);for(let Z=1;Z<=H.value;Z++){const ge=ce.value+"/"+Ye(Z);$[Z]=N(ge)===!0&&de.value(ge)}}return $}),Oe=s(()=>{let $,N;const{year:Z,month:ge}=b.value;if(e.calendar!=="persian")$=new Date(Z,ge-1,1),N=new Date(Z,ge-1,0).getDate();else{const he=ti(Z,ge,1);$=new Date(he.gy,he.gm-1,he.gd);let Ue=ge-1,Te=Z;Ue===0&&(Ue=12,Te--),N=dl(Te,Ue)}return{days:$.getDay()-D.value-1,endDay:N}}),re=s(()=>{const $=[],{days:N,endDay:Z}=Oe.value,ge=N<0?N+7:N;if(ge<6)for(let Te=Z-ge;Te<=Z;Te++)$.push({i:Te,fill:!0});const he=$.length;for(let Te=1;Te<=H.value;Te++){const Ie={i:Te,event:ze.value[Te],classes:[]};$e.value[Te]===!0&&(Ie.in=!0,Ie.flat=!0),$.push(Ie)}if(me.value[ce.value]!==void 0&&me.value[ce.value].forEach(Te=>{const Ie=he+Te-1;Object.assign($[Ie],{selected:!0,unelevated:!0,flat:!1,color:x.value,textColor:q.value})}),we.value[ce.value]!==void 0&&we.value[ce.value].forEach(Te=>{if(Te.from!==void 0){const Ie=he+Te.from-1,Ze=he+(Te.to||H.value)-1;for(let zt=Ie;zt<=Ze;zt++)Object.assign($[zt],{range:Te.range,unelevated:!0,color:x.value,textColor:q.value});Object.assign($[Ie],{rangeFrom:!0,flat:!1}),Te.to!==void 0&&Object.assign($[Ze],{rangeTo:!0,flat:!1})}else if(Te.to!==void 0){const Ie=he+Te.to-1;for(let Ze=he;Ze<=Ie;Ze++)Object.assign($[Ze],{range:Te.range,unelevated:!0,color:x.value,textColor:q.value});Object.assign($[Ie],{flat:!1,rangeTo:!0})}else{const Ie=he+H.value-1;for(let Ze=he;Ze<=Ie;Ze++)Object.assign($[Ze],{range:Te.range,unelevated:!0,color:x.value,textColor:q.value})}}),Me.value!==void 0){const Te=he+Me.value.from-1,Ie=he+Me.value.to-1;for(let Ze=Te;Ze<=Ie;Ze++)$[Ze].color=x.value,$[Ze].editRange=!0;Me.value.includeFrom===!0&&($[Te].editRangeFrom=!0),Me.value.includeTo===!0&&($[Ie].editRangeTo=!0)}b.value.year===y.value.year&&b.value.month===y.value.month&&($[he+y.value.day-1].today=!0);const Ue=$.length%7;if(Ue>0){const Te=7-Ue;for(let Ie=1;Ie<=Te;Ie++)$.push({i:Ie,fill:!0})}return $.forEach(Te=>{let Ie="q-date__calendar-item ";Te.fill===!0?Ie+="q-date__calendar-item--fill":(Ie+=`q-date__calendar-item--${Te.in===!0?"in":"out"}`,Te.range!==void 0&&(Ie+=` q-date__range${Te.rangeTo===!0?"-to":Te.rangeFrom===!0?"-from":""}`),Te.editRange===!0&&(Ie+=` q-date__edit-range${Te.editRangeFrom===!0?"-from":""}${Te.editRangeTo===!0?"-to":""}`),(Te.range!==void 0||Te.editRange===!0)&&(Ie+=` text-${Te.color}`)),Te.classes=Ie}),$}),fe=s(()=>e.disable===!0?{"aria-disabled":"true"}:{});ne(()=>e.modelValue,$=>{if(h===$)h=0;else{const N=at(_.value,p.value);He(N.year,N.month,N)}}),ne(S,()=>{g.value!==null&&l.$el.contains(document.activeElement)===!0&&g.value.focus()}),ne(()=>b.value.year+"|"+b.value.month,()=>{n("navigation",{year:b.value.year,month:b.value.month})}),ne(k,$=>{ee($,p.value,"mask"),_.value=$}),ne(w,$=>{ee(_.value,$,"locale"),p.value=$});function G(){const{year:$,month:N,day:Z}=y.value,ge={...b.value,year:$,month:N,day:Z},he=me.value[dn(ge)];(he===void 0||he.includes(ge.day)===!1)&&en(ge),Ee(ge.year,ge.month)}function be($){oi($)===!0&&(S.value=$)}function Be($,N){["month","year"].includes($)&&($==="month"?ie:X)(N===!0?-1:1)}function Ee($,N){S.value="Calendar",He($,N)}function Pe($,N){if(e.range===!1||!$){z.value=null;return}const Z=Object.assign({...b.value},$),ge=N!==void 0?Object.assign({...b.value},N):Z;z.value={init:Z,initHash:nn(Z),final:ge,finalHash:nn(ge)},Ee(Z.year,Z.month)}function Ge(){return e.calendar==="persian"?"YYYY/MM/DD":e.mask}function nt($,N,Z){return aa($,N,Z,e.calendar,{hour:0,minute:0,second:0,millisecond:0})}function at($,N){const Z=Array.isArray(e.modelValue)===!0?e.modelValue:e.modelValue?[e.modelValue]:[];if(Z.length===0)return J();const ge=Z[Z.length-1],he=nt(ge.from!==void 0?ge.from:ge,$,N);return he.dateHash===null?J():he}function J(){let $,N;if(e.defaultYearMonth!==void 0){const Z=e.defaultYearMonth.split("/");$=parseInt(Z[0],10),N=parseInt(Z[1],10)}else{const Z=y.value!==void 0?y.value:c();$=Z.year,N=Z.month}return{year:$,month:N,day:1,hour:0,minute:0,second:0,millisecond:0,dateHash:$+"/"+Ye(N)+"/01"}}function ie($){let N=b.value.year,Z=Number(b.value.month)+$;Z===13?(Z=1,N++):Z===0&&(Z=12,N--),He(N,Z),U.value===!0&&ut("month")}function X($){const N=Number(b.value.year)+$;He(N,b.value.month),U.value===!0&&ut("year")}function ae($){He($,b.value.month),S.value=e.defaultView==="Years"?"Months":"Calendar",U.value===!0&&ut("year")}function ke($){He(b.value.year,$),S.value="Calendar",U.value===!0&&ut("month")}function Re($,N){const Z=me.value[N];(Z!==void 0&&Z.includes($.day)===!0?Zt:en)($)}function _e($){return{year:$.year,month:$.month,day:$.day}}function He($,N,Z){if(xe.value!==null&&$<=xe.value.year&&((N<xe.value.month||$<xe.value.year)&&(N=xe.value.month),$=xe.value.year),Y.value!==null&&$>=Y.value.year&&((N>Y.value.month||$>Y.value.year)&&(N=Y.value.month),$=Y.value.year),Z!==void 0){const{hour:he,minute:Ue,second:Te,millisecond:Ie,timezoneOffset:Ze,timeHash:zt}=Z;Object.assign(b.value,{hour:he,minute:Ue,second:Te,millisecond:Ie,timezoneOffset:Ze,timeHash:zt})}const ge=$+"/"+Ye(N)+"/01";ge!==b.value.dateHash&&(L.value=b.value.dateHash<ge==(a.lang.rtl!==!0)?"left":"right",$!==b.value.year&&(R.value=L.value),Qe(()=>{P.value=$-$%Sn-($<0?Sn:0),Object.assign(b.value,{year:$,month:N,day:1,dateHash:ge})}))}function ot($,N,Z){const ge=$!==null&&$.length===1&&e.multiple===!1?$[0]:$;h=ge;const{reason:he,details:Ue}=Bt(N,Z);n("update:modelValue",ge,he,Ue)}function ut($){const N=E.value[0]!==void 0&&E.value[0].dateHash!==null?{...E.value[0]}:{...b.value};Qe(()=>{N.year=b.value.year,N.month=b.value.month;const Z=e.calendar!=="persian"?new Date(N.year,N.month,0).getDate():dl(N.year,N.month);N.day=Math.min(Math.max(1,N.day),Z);const ge=At(N);h=ge;const{details:he}=Bt("",N);n("update:modelValue",ge,$,he)})}function Bt($,N){return N.from!==void 0?{reason:`${$}-range`,details:{..._e(N.target),from:_e(N.from),to:_e(N.to)}}:{reason:`${$}-day`,details:_e(N)}}function At($,N,Z){return $.from!==void 0?{from:ue.value($.from,N,Z),to:ue.value($.to,N,Z)}:ue.value($,N,Z)}function en($){let N;if(e.multiple===!0)if($.from!==void 0){const Z=nn($.from),ge=nn($.to),he=E.value.filter(Te=>Te.dateHash<Z||Te.dateHash>ge),Ue=V.value.filter(({from:Te,to:Ie})=>Ie.dateHash<Z||Te.dateHash>ge);N=he.concat(Ue).concat($).map(Te=>At(Te))}else{const Z=W.value.slice();Z.push(At($)),N=Z}else N=At($);ot(N,"add",$)}function Zt($){if(e.noUnset===!0)return;let N=null;if(e.multiple===!0&&Array.isArray(e.modelValue)===!0){const Z=At($);$.from!==void 0?N=e.modelValue.filter(ge=>ge.from!==void 0?ge.from!==Z.from&&ge.to!==Z.to:!0):N=e.modelValue.filter(ge=>ge!==Z),N.length===0&&(N=null)}ot(N,"remove",$)}function ee($,N,Z){const ge=E.value.concat(V.value).map(he=>At(he,$,N)).filter(he=>he.from!==void 0?he.from.dateHash!==null&&he.to.dateHash!==null:he.dateHash!==null);n("update:modelValue",(e.multiple===!0?ge:ge[0])||null,Z)}function ye(){if(e.minimal!==!0)return r("div",{class:"q-date__header "+d.value},[r("div",{class:"relative-position"},[r(_t,{name:"q-transition--fade"},()=>r("div",{key:"h-yr-"+oe.value,class:"q-date__header-subtitle q-date__header-link "+(S.value==="Years"?"q-date__header-link--active":"cursor-pointer"),tabindex:u.value,...i("vY",{onClick(){S.value="Years"},onKeyup($){$.keyCode===13&&(S.value="Years")}})},[oe.value]))]),r("div",{class:"q-date__header-title relative-position flex no-wrap"},[r("div",{class:"relative-position col"},[r(_t,{name:"q-transition--fade"},()=>r("div",{key:"h-sub"+C.value,class:"q-date__header-title-label q-date__header-link "+(S.value==="Calendar"?"q-date__header-link--active":"cursor-pointer"),tabindex:u.value,...i("vC",{onClick(){S.value="Calendar"},onKeyup($){$.keyCode===13&&(S.value="Calendar")}})},[C.value]))]),e.todayBtn===!0?r(Xe,{class:"q-date__header-today self-start",icon:a.iconSet.datetime.today,flat:!0,size:"sm",round:!0,tabindex:u.value,onClick:G}):null])])}function qe({label:$,type:N,key:Z,dir:ge,goTo:he,boundaries:Ue,cls:Te}){return[r("div",{class:"row items-center q-date__arrow"},[r(Xe,{round:!0,dense:!0,size:"sm",flat:!0,icon:M.value[0],tabindex:u.value,disable:Ue.prev===!1,...i("go-#"+N,{onClick(){he(-1)}})})]),r("div",{class:"relative-position overflow-hidden flex flex-center"+Te},[r(_t,{name:"q-transition--jump-"+ge},()=>r("div",{key:Z},[r(Xe,{flat:!0,dense:!0,noCaps:!0,label:$,tabindex:u.value,...i("view#"+N,{onClick:()=>{S.value=N}})})]))]),r("div",{class:"row items-center q-date__arrow"},[r(Xe,{round:!0,dense:!0,size:"sm",flat:!0,icon:M.value[1],tabindex:u.value,disable:Ue.next===!1,...i("go+#"+N,{onClick(){he(1)}})})])]}const Le={Calendar:()=>[r("div",{key:"calendar-view",class:"q-date__view q-date__calendar"},[r("div",{class:"q-date__navigation row items-center no-wrap"},qe({label:p.value.months[b.value.month-1],type:"Months",key:b.value.month,dir:L.value,goTo:ie,boundaries:se.value.month,cls:" col"}).concat(qe({label:b.value.year,type:"Years",key:b.value.year,dir:R.value,goTo:X,boundaries:se.value.year,cls:""}))),r("div",{class:"q-date__calendar-weekdays row items-center no-wrap"},le.value.map($=>r("div",{class:"q-date__calendar-item"},[r("div",$)]))),r("div",{class:"q-date__calendar-days-container relative-position overflow-hidden"},[r(_t,{name:"q-transition--slide-"+L.value},()=>r("div",{key:ce.value,class:"q-date__calendar-days fit"},re.value.map($=>r("div",{class:$.classes},[$.in===!0?r(Xe,{class:$.today===!0?"q-date__today":"",dense:!0,flat:$.flat,unelevated:$.unelevated,color:$.color,textColor:$.textColor,label:$.i,tabindex:u.value,...i("day#"+$.i,{onClick:()=>{Ve($.i)},onMouseover:()=>{st($.i)}})},$.event!==!1?()=>r("div",{class:"q-date__event bg-"+$.event}):null):r("div",""+$.i)]))))])])],Months(){const $=b.value.year===y.value.year,N=ge=>xe.value!==null&&b.value.year===xe.value.year&&xe.value.month>ge||Y.value!==null&&b.value.year===Y.value.year&&Y.value.month<ge,Z=p.value.monthsShort.map((ge,he)=>{const Ue=b.value.month===he+1;return r("div",{class:"q-date__months-item flex flex-center"},[r(Xe,{class:$===!0&&y.value.month===he+1?"q-date__today":null,flat:Ue!==!0,label:ge,unelevated:Ue,color:Ue===!0?x.value:null,textColor:Ue===!0?q.value:null,tabindex:u.value,disable:N(he+1),...i("month#"+he,{onClick:()=>{ke(he+1)}})})])});return e.yearsInMonthView===!0&&Z.unshift(r("div",{class:"row no-wrap full-width"},[qe({label:b.value.year,type:"Years",key:b.value.year,dir:R.value,goTo:X,boundaries:se.value.year,cls:" col"})])),r("div",{key:"months-view",class:"q-date__view q-date__months flex flex-center"},Z)},Years(){const $=P.value,N=$+Sn,Z=[],ge=he=>xe.value!==null&&xe.value.year>he||Y.value!==null&&Y.value.year<he;for(let he=$;he<=N;he++){const Ue=b.value.year===he;Z.push(r("div",{class:"q-date__years-item flex flex-center"},[r(Xe,{key:"yr"+he,class:y.value.year===he?"q-date__today":null,flat:!Ue,label:he,dense:!0,unelevated:Ue,color:Ue===!0?x.value:null,textColor:Ue===!0?q.value:null,tabindex:u.value,disable:ge(he),...i("yr#"+he,{onClick:()=>{ae(he)}})})]))}return r("div",{class:"q-date__view q-date__years flex flex-center"},[r("div",{class:"col-auto"},[r(Xe,{round:!0,dense:!0,flat:!0,icon:M.value[0],tabindex:u.value,disable:ge($),...i("y-",{onClick:()=>{P.value-=Sn}})})]),r("div",{class:"q-date__years-content col self-stretch row items-center"},Z),r("div",{class:"col-auto"},[r(Xe,{round:!0,dense:!0,flat:!0,icon:M.value[1],tabindex:u.value,disable:ge(N),...i("y+",{onClick:()=>{P.value+=Sn}})})])])}};function Ve($){const N={...b.value,day:$};if(e.range===!1){Re(N,ce.value);return}if(z.value===null){const Z=re.value.find(he=>he.fill!==!0&&he.i===$);if(e.noUnset!==!0&&Z.range!==void 0){Zt({target:N,from:Z.range.from,to:Z.range.to});return}if(Z.selected===!0){Zt(N);return}const ge=nn(N);z.value={init:N,initHash:ge,final:N,finalHash:ge},n("rangeStart",_e(N))}else{const Z=z.value.initHash,ge=nn(N),he=Z<=ge?{from:z.value.init,to:N}:{from:N,to:z.value.init};z.value=null,en(Z===ge?N:{target:N,...he}),n("rangeEnd",{from:_e(he.from),to:_e(he.to)})}}function st($){if(z.value!==null){const N={...b.value,day:$};Object.assign(z.value,{final:N,finalHash:nn(N)})}}return Object.assign(l,{setToday:G,setView:be,offsetCalendar:Be,setCalendarTo:Ee,setEditingRange:Pe}),()=>{const $=[r("div",{class:"q-date__content col relative-position"},[r(_t,{name:"q-transition--fade"},Le[S.value])])],N=Ce(t.default);return N!==void 0&&$.push(r("div",{class:"q-date__actions"},N)),e.name!==void 0&&e.disable!==!0&&v($,"push"),r("div",{class:T.value,...fe.value},[ye(),r("div",{ref:g,class:"q-date__main col column",tabindex:-1},$)])}}});function vs(e,t,n){let l;function a(){l!==void 0&&(La.remove(l),l=void 0)}return Ne(()=>{e.value===!0&&a()}),{removeFromHistory:a,addToHistory(){l={condition:()=>n.value===!0,handler:t},La.add(l)}}}var pa=0,ao,lo,ka,oo=!1,ri,ii,ui,xn=null;function Uf(e){Yf(e)&&Fe(e)}function Yf(e){if(e.target===document.body||e.target.classList.contains("q-layout__backdrop"))return!0;const t=tu(e),n=e.shiftKey&&!e.deltaX,l=!n&&Math.abs(e.deltaX)<=Math.abs(e.deltaY),a=n||l?e.deltaY:e.deltaX;for(let o=0;o<t.length;o++){const i=t[o];if(Lu(i,l))return l?a<0&&i.scrollTop===0?!0:a>0&&i.scrollTop+i.clientHeight===i.scrollHeight:a<0&&i.scrollLeft===0?!0:a>0&&i.scrollLeft+i.clientWidth===i.scrollWidth}return!0}function si(e){e.target===document&&(document.scrollingElement.scrollTop=document.scrollingElement.scrollTop)}function Ja(e){oo!==!0&&(oo=!0,requestAnimationFrame(()=>{oo=!1;const{height:t}=e.target,{clientHeight:n,scrollTop:l}=document.scrollingElement;(ka===void 0||t!==window.innerHeight)&&(ka=n-t,document.scrollingElement.scrollTop=l),l>ka&&(document.scrollingElement.scrollTop-=Math.ceil((l-ka)/8))}))}function ci(e){const t=document.body,n=window.visualViewport!==void 0;if(e==="add"){const{overflowY:l,overflowX:a}=window.getComputedStyle(t);ao=ja(window),lo=rn(window),ri=t.style.left,ii=t.style.top,ui=window.location.href,t.style.left=`-${ao}px`,t.style.top=`-${lo}px`,a!=="hidden"&&(a==="scroll"||t.scrollWidth>window.innerWidth)&&t.classList.add("q-body--force-scrollbar-x"),l!=="hidden"&&(l==="scroll"||t.scrollHeight>window.innerHeight)&&t.classList.add("q-body--force-scrollbar-y"),t.classList.add("q-body--prevent-scroll"),document.qScrollPrevented=!0,Ae.is.ios===!0&&(n===!0?(window.scrollTo(0,0),window.visualViewport.addEventListener("resize",Ja,et.passiveCapture),window.visualViewport.addEventListener("scroll",Ja,et.passiveCapture),window.scrollTo(0,0)):window.addEventListener("scroll",si,et.passiveCapture))}Ae.is.desktop===!0&&Ae.is.mac===!0&&window[`${e}EventListener`]("wheel",Uf,et.notPassive),e==="remove"&&(Ae.is.ios===!0&&(n===!0?(window.visualViewport.removeEventListener("resize",Ja,et.passiveCapture),window.visualViewport.removeEventListener("scroll",Ja,et.passiveCapture)):window.removeEventListener("scroll",si,et.passiveCapture)),t.classList.remove("q-body--prevent-scroll"),t.classList.remove("q-body--force-scrollbar-x"),t.classList.remove("q-body--force-scrollbar-y"),document.qScrollPrevented=!1,t.style.left=ri,t.style.top=ii,window.location.href===ui&&window.scrollTo(ao,lo),ka=void 0)}function Mo(e){let t="add";if(e===!0){if(pa++,xn!==null){clearTimeout(xn),xn=null;return}if(pa>1)return}else{if(pa===0||(pa--,pa>0))return;if(t="remove",Ae.is.ios===!0&&Ae.is.nativeMobile===!0){xn!==null&&clearTimeout(xn),xn=setTimeout(()=>{ci(t),xn=null},100);return}}ci(t)}function ms(){let e;return{preventBodyScroll(t){t!==e&&(e!==void 0||t===!0)&&(e=t,Mo(t))}}}var el=0,Xf={standard:"fixed-full flex-center",top:"fixed-top justify-center",bottom:"fixed-bottom justify-center",right:"fixed-right items-center",left:"fixed-left items-center"},di={standard:["scale","scale"],top:["slide-down","slide-up"],bottom:["slide-up","slide-down"],right:["slide-left","slide-right"],left:["slide-right","slide-left"]},Dl=te({name:"QDialog",inheritAttrs:!1,props:{...va,...En,transitionShow:String,transitionHide:String,persistent:Boolean,autoClose:Boolean,allowFocusOutside:Boolean,noEscDismiss:Boolean,noBackdropDismiss:Boolean,noRouteDismiss:Boolean,noRefocus:Boolean,noFocus:Boolean,noShake:Boolean,seamless:Boolean,maximized:Boolean,fullWidth:Boolean,fullHeight:Boolean,square:Boolean,backdropFilter:String,position:{type:String,default:"standard",validator:e=>["standard","top","bottom","left","right"].includes(e)}},emits:[...ma,"shake","click","escapeKey"],setup(e,{slots:t,emit:n,attrs:l}){const a=ve(),o=O(null),i=O(!1),u=O(!1);let d=null,f=null,c,h;const m=s(()=>e.persistent!==!0&&e.noRouteDismiss!==!0&&e.seamless!==!0),{preventBodyScroll:v}=ms(),{registerTimeout:g}=hn(),{registerTick:_,removeTick:p}=ia(),{transitionProps:k,transitionStyle:w}=Rl(e,()=>di[e.position][0],()=>di[e.position][1]),y=s(()=>w.value+(e.backdropFilter!==void 0?`;backdrop-filter:${e.backdropFilter};-webkit-backdrop-filter:${e.backdropFilter}`:"")),{showPortal:b,hidePortal:S,portalIsAccessible:B,renderPortal:L}=Uo(a,o,oe,"dialog"),{hide:R}=ga({showing:i,hideOnRouteChange:m,handleShow:U,handleHide:W,processOnMount:!0}),{addToHistory:A,removeFromHistory:P}=vs(i,R,m),z=s(()=>`q-dialog__inner flex no-pointer-events q-dialog__inner--${e.maximized===!0?"maximized":"minimized"} q-dialog__inner--${e.position} ${Xf[e.position]}`+(u.value===!0?" q-dialog__inner--animating":"")+(e.fullWidth===!0?" q-dialog__inner--fullwidth":"")+(e.fullHeight===!0?" q-dialog__inner--fullheight":"")+(e.square===!0?" q-dialog__inner--square":"")),T=s(()=>i.value===!0&&e.seamless!==!0),x=s(()=>e.autoClose===!0?{onClick:C}:{}),q=s(()=>[`q-dialog fullscreen no-pointer-events q-dialog--${T.value===!0?"modal":"seamless"}`,l.class]);ne(()=>e.maximized,M=>{i.value===!0&&I(M)}),ne(T,M=>{v(M),M===!0?(Go(Q),zu(j)):(wl(Q),pl(j))});function U(M){A(),f=e.noRefocus===!1&&document.activeElement!==null?document.activeElement:null,I(e.maximized),b(),u.value=!0,e.noFocus!==!0?(document.activeElement!==null&&document.activeElement.blur(),_(E)):p(),g(()=>{if(a.proxy.$q.platform.is.ios===!0){if(e.seamless!==!0&&document.activeElement){const{top:D,bottom:le}=document.activeElement.getBoundingClientRect(),{innerHeight:H}=window,de=window.visualViewport!==void 0?window.visualViewport.height:H;D>0&&le>de/2&&(document.scrollingElement.scrollTop=Math.min(document.scrollingElement.scrollHeight-de,le>=H?1/0:Math.ceil(document.scrollingElement.scrollTop+le-de/2))),document.activeElement.scrollIntoView()}h=!0,o.value.click(),h=!1}b(!0),u.value=!1,n("show",M)},e.transitionDuration)}function W(M){p(),P(),ue(!0),u.value=!0,S(),f!==null&&(((M&&M.type.indexOf("key")===0?f.closest('[tabindex]:not([tabindex^="-"])'):void 0)||f).focus(),f=null),g(()=>{S(!0),u.value=!1,n("hide",M)},e.transitionDuration)}function E(M){ha(()=>{let D=o.value;if(D!==null){if(M!==void 0){const le=D.querySelector(M);if(le!==null){le.focus({preventScroll:!0});return}}D.contains(document.activeElement)!==!0&&(D=D.querySelector("[autofocus][tabindex], [data-autofocus][tabindex]")||D.querySelector("[autofocus] [tabindex], [data-autofocus] [tabindex]")||D.querySelector("[autofocus], [data-autofocus]")||D,D.focus({preventScroll:!0}))}})}function V(M){M&&typeof M.focus=="function"?M.focus({preventScroll:!0}):E(),n("shake");const D=o.value;D!==null&&(D.classList.remove("q-animate--scale"),D.classList.add("q-animate--scale"),d!==null&&clearTimeout(d),d=setTimeout(()=>{d=null,o.value!==null&&(D.classList.remove("q-animate--scale"),E())},170))}function j(){e.seamless!==!0&&(e.persistent===!0||e.noEscDismiss===!0?e.maximized!==!0&&e.noShake!==!0&&V():(n("escapeKey"),R()))}function ue(M){d!==null&&(clearTimeout(d),d=null),(M===!0||i.value===!0)&&(I(!1),e.seamless!==!0&&(v(!1),wl(Q),pl(j))),M!==!0&&(f=null)}function I(M){M===!0?c!==!0&&(el<1&&document.body.classList.add("q-body--dialog"),el++,c=!0):c===!0&&(el<2&&document.body.classList.remove("q-body--dialog"),el--,c=!1)}function C(M){h!==!0&&(R(M),n("click",M))}function K(M){e.persistent!==!0&&e.noBackdropDismiss!==!0?R(M):e.noShake!==!0&&V()}function Q(M){e.allowFocusOutside!==!0&&B.value===!0&&xu(o.value,M.target)!==!0&&E('[tabindex]:not([tabindex="-1"])')}Object.assign(a.proxy,{focus:E,shake:V,__updateRefocusTarget(M){f=M||null}}),Ne(ue);function oe(){return r("div",{role:"dialog","aria-modal":T.value===!0?"true":"false",...l,class:q.value},[r(_t,{name:"q-transition--fade",appear:!0},()=>T.value===!0?r("div",{class:"q-dialog__backdrop fixed-full",style:y.value,"aria-hidden":"true",tabindex:-1,onClick:K}):null),r(_t,k.value,()=>i.value===!0?r("div",{ref:o,class:z.value,style:w.value,tabindex:-1,...x.value},Ce(t.default)):null)])}return L}}),fi=150,sh=te({name:"QDrawer",inheritAttrs:!1,props:{...va,...Ke,side:{type:String,default:"left",validator:e=>["left","right"].includes(e)},width:{type:Number,default:300},mini:Boolean,miniToOverlay:Boolean,miniWidth:{type:Number,default:57},noMiniAnimation:Boolean,breakpoint:{type:Number,default:1023},showIfAbove:Boolean,behavior:{type:String,validator:e=>["default","desktop","mobile"].includes(e),default:"default"},bordered:Boolean,elevated:Boolean,overlay:Boolean,persistent:Boolean,noSwipeOpen:Boolean,noSwipeClose:Boolean,noSwipeBackdrop:Boolean},emits:[...ma,"onLayout","miniState"],setup(e,{slots:t,emit:n,attrs:l}){const a=ve(),{proxy:{$q:o}}=a,i=We(e,o),{preventBodyScroll:u}=ms(),{registerTimeout:d,removeTimeout:f}=hn(),c=Et(Nn,Je);if(c===Je)return console.error("QDrawer needs to be child of QLayout"),Je;let h,m=null,v;const g=O(e.behavior==="mobile"||e.behavior!=="desktop"&&c.totalWidth.value<=e.breakpoint),_=s(()=>e.mini===!0&&g.value!==!0),p=s(()=>_.value===!0?e.miniWidth:e.width),k=O(e.showIfAbove===!0&&g.value===!1?!0:e.modelValue===!0),w=s(()=>e.persistent!==!0&&(g.value===!0||ue.value===!0));function y(G,be){if(L(),G!==!1&&c.animate(),se(0),g.value===!0){const Be=c.instances[W.value];Be!==void 0&&Be.belowBreakpoint===!0&&Be.hide(!1),me(1),c.isContainer.value!==!0&&u(!0)}else me(0),G!==!1&&we(!1);d(()=>{G!==!1&&we(!0),be!==!0&&n("show",G)},fi)}function b(G,be){R(),G!==!1&&c.animate(),me(0),se(z.value*p.value),ze(),be!==!0?d(()=>{n("hide",G)},fi):f()}const{show:S,hide:B}=ga({showing:k,hideOnRouteChange:w,handleShow:y,handleHide:b}),{addToHistory:L,removeFromHistory:R}=vs(k,B,w),A={belowBreakpoint:g,hide:B},P=s(()=>e.side==="right"),z=s(()=>(o.lang.rtl===!0?-1:1)*(P.value===!0?1:-1)),T=O(0),x=O(!1),q=O(!1),U=O(p.value*z.value),W=s(()=>P.value===!0?"left":"right"),E=s(()=>k.value===!0&&g.value===!1&&e.overlay===!1?e.miniToOverlay===!0?e.miniWidth:p.value:0),V=s(()=>e.overlay===!0||e.miniToOverlay===!0||c.view.value.indexOf(P.value?"R":"L")!==-1||o.platform.is.ios===!0&&c.isContainer.value===!0),j=s(()=>e.overlay===!1&&k.value===!0&&g.value===!1),ue=s(()=>e.overlay===!0&&k.value===!0&&g.value===!1),I=s(()=>"fullscreen q-drawer__backdrop"+(k.value===!1&&x.value===!1?" hidden":"")),C=s(()=>({backgroundColor:`rgba(0,0,0,${T.value*.4})`})),K=s(()=>P.value===!0?c.rows.value.top[2]==="r":c.rows.value.top[0]==="l"),Q=s(()=>P.value===!0?c.rows.value.bottom[2]==="r":c.rows.value.bottom[0]==="l"),oe=s(()=>{const G={};return c.header.space===!0&&K.value===!1&&(V.value===!0?G.top=`${c.header.offset}px`:c.header.space===!0&&(G.top=`${c.header.size}px`)),c.footer.space===!0&&Q.value===!1&&(V.value===!0?G.bottom=`${c.footer.offset}px`:c.footer.space===!0&&(G.bottom=`${c.footer.size}px`)),G}),M=s(()=>{const G={width:`${p.value}px`,transform:`translateX(${U.value}px)`};return g.value===!0?G:Object.assign(G,oe.value)}),D=s(()=>"q-drawer__content fit "+(c.isContainer.value!==!0?"scroll":"overflow-auto")),le=s(()=>`q-drawer q-drawer--${e.side}`+(q.value===!0?" q-drawer--mini-animate":"")+(e.bordered===!0?" q-drawer--bordered":"")+(i.value===!0?" q-drawer--dark q-dark":"")+(x.value===!0?" no-transition":k.value===!0?"":" q-layout--prevent-focus")+(g.value===!0?" fixed q-drawer--on-top q-drawer--mobile q-drawer--top-padding":` q-drawer--${_.value===!0?"mini":"standard"}`+(V.value===!0||j.value!==!0?" fixed":"")+(e.overlay===!0||e.miniToOverlay===!0?" q-drawer--on-top":"")+(K.value===!0?" q-drawer--top-padding":""))),H=s(()=>{const G=o.lang.rtl===!0?e.side:W.value;return[[Ft,ce,void 0,{[G]:!0,mouse:!0}]]}),de=s(()=>{const G=o.lang.rtl===!0?W.value:e.side;return[[Ft,$e,void 0,{[G]:!0,mouse:!0}]]}),xe=s(()=>{const G=o.lang.rtl===!0?W.value:e.side;return[[Ft,$e,void 0,{[G]:!0,mouse:!0,mouseAllDir:!0}]]});function Y(){re(g,e.behavior==="mobile"||e.behavior!=="desktop"&&c.totalWidth.value<=e.breakpoint)}ne(g,G=>{G===!0?(h=k.value,k.value===!0&&B(!1)):e.overlay===!1&&e.behavior!=="mobile"&&h!==!1&&(k.value===!0?(se(0),me(0),ze()):S(!1))}),ne(()=>e.side,(G,be)=>{c.instances[be]===A&&(c.instances[be]=void 0,c[be].space=!1,c[be].offset=0),c.instances[G]=A,c[G].size=p.value,c[G].space=j.value,c[G].offset=E.value}),ne(c.totalWidth,()=>{(c.isContainer.value===!0||document.qScrollPrevented!==!0)&&Y()}),ne(()=>e.behavior+e.breakpoint,Y),ne(c.isContainer,G=>{k.value===!0&&u(G!==!0),G===!0&&Y()}),ne(c.scrollbarWidth,()=>{se(k.value===!0?0:void 0)}),ne(E,G=>{Oe("offset",G)}),ne(j,G=>{n("onLayout",G),Oe("space",G)}),ne(P,()=>{se()}),ne(p,G=>{se(),fe(e.miniToOverlay,G)}),ne(()=>e.miniToOverlay,G=>{fe(G,p.value)}),ne(()=>o.lang.rtl,()=>{se()}),ne(()=>e.mini,()=>{e.noMiniAnimation||e.modelValue===!0&&(Me(),c.animate())}),ne(_,G=>{n("miniState",G)});function se(G){G===void 0?Qe(()=>{G=k.value===!0?0:p.value,se(z.value*G)}):(c.isContainer.value===!0&&P.value===!0&&(g.value===!0||Math.abs(G)===p.value)&&(G+=z.value*c.scrollbarWidth.value),U.value=G)}function me(G){T.value=G}function we(G){const be=G===!0?"remove":c.isContainer.value!==!0?"add":"";be!==""&&document.body.classList[be]("q-body--drawer-toggle")}function Me(){m!==null&&clearTimeout(m),a.proxy&&a.proxy.$el&&a.proxy.$el.classList.add("q-drawer--mini-animate"),q.value=!0,m=setTimeout(()=>{m=null,q.value=!1,a&&a.proxy&&a.proxy.$el&&a.proxy.$el.classList.remove("q-drawer--mini-animate")},150)}function ce(G){if(k.value!==!1)return;const be=p.value,Be=tt(G.distance.x,0,be);if(G.isFinal===!0){Be>=Math.min(75,be)===!0?S():(c.animate(),me(0),se(z.value*be)),x.value=!1;return}se((o.lang.rtl===!0?P.value!==!0:P.value)?Math.max(be-Be,0):Math.min(0,Be-be)),me(tt(Be/be,0,1)),G.isFirst===!0&&(x.value=!0)}function $e(G){if(k.value!==!0)return;const be=p.value,Be=G.direction===e.side,Ee=(o.lang.rtl===!0?Be!==!0:Be)?tt(G.distance.x,0,be):0;if(G.isFinal===!0){Math.abs(Ee)<Math.min(75,be)===!0?(c.animate(),me(1),se(0)):B(),x.value=!1;return}se(z.value*Ee),me(tt(1-Ee/be,0,1)),G.isFirst===!0&&(x.value=!0)}function ze(){u(!1),we(!0)}function Oe(G,be){c.update(e.side,G,be)}function re(G,be){G.value!==be&&(G.value=be)}function fe(G,be){Oe("size",G===!0?e.miniWidth:be)}return c.instances[e.side]=A,fe(e.miniToOverlay,p.value),Oe("space",j.value),Oe("offset",E.value),e.showIfAbove===!0&&e.modelValue!==!0&&k.value===!0&&e["onUpdate:modelValue"]!==void 0&&n("update:modelValue",!0),ft(()=>{n("onLayout",j.value),n("miniState",_.value),h=e.showIfAbove===!0;const G=()=>{(k.value===!0?y:b)(!1,!0)};if(c.totalWidth.value!==0){Qe(G);return}v=ne(c.totalWidth,()=>{v(),v=void 0,k.value===!1&&e.showIfAbove===!0&&g.value===!1?S(!1):G()})}),Ne(()=>{v!==void 0&&v(),m!==null&&(clearTimeout(m),m=null),k.value===!0&&ze(),c.instances[e.side]===A&&(c.instances[e.side]=void 0,Oe("size",0),Oe("offset",0),Oe("space",!1))}),()=>{const G=[];g.value===!0&&(e.noSwipeOpen===!1&&G.push(Ut(r("div",{key:"open",class:`q-drawer__opener fixed-${e.side}`,"aria-hidden":"true"}),H.value)),G.push(Dt("div",{ref:"backdrop",class:I.value,style:C.value,"aria-hidden":"true",onClick:B},void 0,"backdrop",e.noSwipeBackdrop!==!0&&k.value===!0,()=>xe.value)));const be=_.value===!0&&t.mini!==void 0,Be=[r("div",{...l,key:""+be,class:[D.value,l.class]},be===!0?t.mini():Ce(t.default))];return e.elevated===!0&&k.value===!0&&Be.push(r("div",{class:"q-layout__shadow absolute-full overflow-hidden no-pointer-events"})),G.push(Dt("aside",{ref:"content",class:le.value,style:M.value},Be,"contentclose",e.noSwipeClose!==!0&&g.value===!0,()=>de.value)),r("div",{class:"q-drawer-container"},G)}}});function gs(e,t){if(t&&e===t)return null;const n=e.nodeName.toLowerCase();if(["div","li","ul","ol","blockquote"].includes(n)===!0)return e;const l=window.getComputedStyle?window.getComputedStyle(e):e.currentStyle,a=l.display;return a==="block"||a==="table"?e:gs(e.parentNode)}function ro(e,t,n){return!e||e===document.body?!1:n===!0&&e===t||(t===document?document.body:t).contains(e.parentNode)}function hs(e,t,n){if(n||(n=document.createRange(),n.selectNode(e),n.setStart(e,0)),t.count===0)n.setEnd(e,t.count);else if(t.count>0)if(e.nodeType===Node.TEXT_NODE)e.textContent.length<t.count?t.count-=e.textContent.length:(n.setEnd(e,t.count),t.count=0);else for(let l=0;t.count!==0&&l<e.childNodes.length;l++)n=hs(e.childNodes[l],t,n);return n}var Gf=/^https?:\/\//,Zf=class{constructor(e,t){this.el=e,this.eVm=t,this._range=null}get selection(){if(this.el){const e=document.getSelection();if(ro(e.anchorNode,this.el,!0)&&ro(e.focusNode,this.el,!0))return e}return null}get hasSelection(){return this.selection!==null?this.selection.toString().length!==0:!1}get range(){const e=this.selection;return e!==null&&e.rangeCount?e.getRangeAt(0):this._range}get parent(){const e=this.range;if(e!==null){const t=e.startContainer;return t.nodeType===document.ELEMENT_NODE?t:t.parentNode}return null}get blockParent(){const e=this.parent;return e!==null?gs(e,this.el):null}save(e=this.range){e!==null&&(this._range=e)}restore(e=this._range){const t=document.createRange(),n=document.getSelection();e!==null?(t.setStart(e.startContainer,e.startOffset),t.setEnd(e.endContainer,e.endOffset),n.removeAllRanges(),n.addRange(t)):(n.selectAllChildren(this.el),n.collapseToEnd())}savePosition(){let e=-1,t;const n=document.getSelection(),l=this.el.parentNode;if(n.focusNode&&ro(n.focusNode,l))for(t=n.focusNode,e=n.focusOffset;t&&t!==l;)t!==this.el&&t.previousSibling?(t=t.previousSibling,e+=t.textContent.length):t=t.parentNode;this.savedPos=e}restorePosition(e=0){if(this.savedPos>0&&this.savedPos<e){const t=window.getSelection(),n=hs(this.el,{count:this.savedPos});n&&(n.collapse(!1),t.removeAllRanges(),t.addRange(n))}}hasParent(e,t){const n=t?this.parent:this.blockParent;return n!==null?n.nodeName.toLowerCase()===e.toLowerCase():!1}hasParents(e,t,n=this.parent){return n===null?!1:e.includes(n.nodeName.toLowerCase())===!0?!0:t===!0?this.hasParents(e,t,n.parentNode):!1}is(e,t){if(this.selection===null)return!1;switch(e){case"formatBlock":return t==="DIV"&&this.parent===this.el||this.hasParent(t,t==="PRE");case"link":return this.hasParent("A",!0);case"fontSize":return document.queryCommandValue(e)===t;case"fontName":const n=document.queryCommandValue(e);return n===`"${t}"`||n===t;case"fullscreen":return this.eVm.inFullscreen.value;case"viewsource":return this.eVm.isViewingSource.value;case void 0:return!1;default:const l=document.queryCommandState(e);return t!==void 0?l===t:l}}getParentAttribute(e){return this.parent!==null?this.parent.getAttribute(e):null}can(e){if(e==="outdent")return this.hasParents(["blockquote","li"],!0);if(e==="indent")return this.hasParents(["li"],!0);if(e==="link")return this.selection!==null||this.is("link")}apply(e,t,n=it){if(e==="formatBlock")["BLOCKQUOTE","H1","H2","H3","H4","H5","H6"].includes(t)&&this.is(e,t)&&(e="outdent",t=null),t==="PRE"&&this.is(e,"PRE")&&(t="P");else if(e==="print"){n();const l=window.open();l.document.write(`
        <!doctype html>
        <html>
          <head>
            <title>Print - ${document.title}</title>
          </head>
          <body>
            <div>${this.el.innerHTML}</div>
          </body>
        </html>
      `),l.print(),l.close();return}else if(e==="link"){const l=this.getParentAttribute("href");if(l===null){const a=this.selectWord(this.selection),o=a?a.toString():"";if(!o.length&&(!this.range||!this.range.cloneContents().querySelector("img")))return;this.eVm.editLinkUrl.value=Gf.test(o)?o:"https://",document.execCommand("createLink",!1,this.eVm.editLinkUrl.value),this.save(a.getRangeAt(0))}else this.eVm.editLinkUrl.value=l,this.range.selectNodeContents(this.parent),this.save();return}else if(e==="fullscreen"){this.eVm.toggleFullscreen(),n();return}else if(e==="viewsource"){this.eVm.isViewingSource.value=this.eVm.isViewingSource.value===!1,this.eVm.setContent(this.eVm.props.modelValue),n();return}document.execCommand(e,!1,t),n()}selectWord(e){if(e===null||e.isCollapsed!==!0||e.modify===void 0)return e;const t=document.createRange();t.setStart(e.anchorNode,e.anchorOffset),t.setEnd(e.focusNode,e.focusOffset);const n=t.collapsed?["backward","forward"]:["forward","backward"];t.detach();const l=e.focusNode,a=e.focusOffset;return e.collapse(e.anchorNode,e.anchorOffset),e.modify("move",n[0],"character"),e.modify("move",n[1],"word"),e.extend(l,a),e.modify("extend",n[1],"character"),e.modify("extend",n[0],"word"),e}},Jf=te({name:"QTooltip",inheritAttrs:!1,props:{...Mu,...va,...En,maxHeight:{type:String,default:null},maxWidth:{type:String,default:null},transitionShow:{...En.transitionShow,default:"jump-down"},transitionHide:{...En.transitionHide,default:"jump-up"},anchor:{type:String,default:"bottom middle",validator:Cl},self:{type:String,default:"top middle",validator:Cl},offset:{type:Array,default:()=>[14,14],validator:Fu},scrollTarget:Qn,delay:{type:Number,default:0},hideDelay:{type:Number,default:0},persistent:Boolean},emits:[...ma],setup(e,{slots:t,emit:n,attrs:l}){let a,o;const i=ve(),{proxy:{$q:u}}=i,d=O(null),f=O(!1),c=s(()=>kl(e.anchor,u.lang.rtl)),h=s(()=>kl(e.self,u.lang.rtl)),m=s(()=>e.persistent!==!0),{registerTick:v,removeTick:g}=ia(),{registerTimeout:_}=hn(),{transitionProps:p,transitionStyle:k}=Rl(e),{localScrollTarget:w,changeScrollEvent:y,unconfigureScrollTarget:b}=Bu(e,ue),{anchorEl:S,canShow:B,anchorEvents:L}=Ko({showing:f,configureAnchorEl:j}),{show:R,hide:A}=ga({showing:f,canShow:B,handleShow:x,handleHide:q,hideOnRouteChange:m,processOnMount:!0});Object.assign(L,{delayShow:E,delayHide:V});const{showPortal:P,hidePortal:z,renderPortal:T}=Uo(i,d,C,"tooltip");if(u.platform.is.mobile===!0){const K={anchorEl:S,innerRef:d,onClickOutside(oe){return A(oe),oe.target.classList.contains("q-dialog__backdrop")&&Fe(oe),!0}},Q=s(()=>e.modelValue===null&&e.persistent!==!0&&f.value===!0);ne(Q,oe=>{(oe===!0?Ru:xl)(K)}),Ne(()=>{xl(K)})}function x(K){P(),v(()=>{o=new MutationObserver(()=>W()),o.observe(d.value,{attributes:!1,childList:!0,characterData:!0,subtree:!0}),W(),ue()}),a===void 0&&(a=ne(()=>u.screen.width+"|"+u.screen.height+"|"+e.self+"|"+e.anchor+"|"+u.lang.rtl,W)),_(()=>{P(!0),n("show",K)},e.transitionDuration)}function q(K){g(),z(),U(),_(()=>{z(!0),n("hide",K)},e.transitionDuration)}function U(){o!==void 0&&(o.disconnect(),o=void 0),a!==void 0&&(a(),a=void 0),b(),Mt(L,"tooltipTemp")}function W(){Zo({targetEl:d.value,offset:e.offset,anchorEl:S.value,anchorOrigin:c.value,selfOrigin:h.value,maxHeight:e.maxHeight,maxWidth:e.maxWidth})}function E(K){if(u.platform.is.mobile===!0){Wt(),document.body.classList.add("non-selectable");const Q=S.value,oe=["touchmove","touchcancel","touchend","click"].map(M=>[Q,M,"delayHide","passiveCapture"]);vt(L,"tooltipTemp",oe)}_(()=>{R(K)},e.delay)}function V(K){u.platform.is.mobile===!0&&(Mt(L,"tooltipTemp"),Wt(),setTimeout(()=>{document.body.classList.remove("non-selectable")},10)),_(()=>{A(K)},e.hideDelay)}function j(){if(e.noParentEvent===!0||S.value===null)return;const K=u.platform.is.mobile===!0?[[S.value,"touchstart","delayShow","passive"]]:[[S.value,"mouseenter","delayShow","passive"],[S.value,"mouseleave","delayHide","passive"]];vt(L,"anchor",K)}function ue(){if(S.value!==null||e.scrollTarget!==void 0){w.value=Gt(S.value,e.scrollTarget);const K=e.noParentEvent===!0?W:A;y(w.value,K)}}function I(){return f.value===!0?r("div",{...l,ref:d,class:["q-tooltip q-tooltip--style q-position-engine no-pointer-events",l.class],style:[l.style,k.value],role:"tooltip"},Ce(t.default)):null}function C(){return r(_t,p.value,I)}return Ne(U),Object.assign(i.proxy,{updatePosition:W}),T}}),Il=te({name:"QItem",props:{...Ke,...Na,tag:{type:String,default:"div"},active:{type:Boolean,default:null},clickable:Boolean,dense:Boolean,insetLevel:Number,tabindex:[String,Number],focused:Boolean,manualFocus:Boolean},emits:["click","keyup"],setup(e,{slots:t,emit:n}){const{proxy:{$q:l}}=ve(),a=We(e,l),{hasLink:o,linkAttrs:i,linkClass:u,linkTag:d,navigateOnClick:f}=zl(),c=O(null),h=O(null),m=s(()=>e.clickable===!0||o.value===!0||e.tag==="label"),v=s(()=>e.disable!==!0&&m.value===!0),g=s(()=>"q-item q-item-type row no-wrap"+(e.dense===!0?" q-item--dense":"")+(a.value===!0?" q-item--dark":"")+(o.value===!0&&e.active===null?u.value:e.active===!0?` q-item--active${e.activeClass!==void 0?` ${e.activeClass}`:""}`:"")+(e.disable===!0?" disabled":"")+(v.value===!0?" q-item--clickable q-link cursor-pointer "+(e.manualFocus===!0?"q-manual-focusable":"q-focusable q-hoverable")+(e.focused===!0?" q-manual-focusable--focused":""):"")),_=s(()=>{if(e.insetLevel===void 0)return null;const y=l.lang.rtl===!0?"Right":"Left";return{["padding"+y]:16+e.insetLevel*56+"px"}});function p(y){v.value===!0&&(h.value!==null&&(y.qKeyEvent!==!0&&document.activeElement===c.value?h.value.focus():document.activeElement===h.value&&c.value.focus()),f(y))}function k(y){if(v.value===!0&&Vt(y,[13,32])===!0){Fe(y),y.qKeyEvent=!0;const b=new MouseEvent("click",y);b.qKeyEvent=!0,c.value.dispatchEvent(b)}n("keyup",y)}function w(){const y=Ha(t.default,[]);return v.value===!0&&y.unshift(r("div",{class:"q-focus-helper",tabindex:-1,ref:h})),y}return()=>{const y={ref:c,class:g.value,style:_.value,role:"listitem",onClick:p,onKeyup:k};return v.value===!0?(y.tabindex=e.tabindex||"0",Object.assign(y,i.value)):m.value===!0&&(y["aria-disabled"]="true"),r(d.value,y,w())}}}),gn=te({name:"QItemSection",props:{avatar:Boolean,thumbnail:Boolean,side:Boolean,top:Boolean,noWrap:Boolean},setup(e,{slots:t}){const n=s(()=>`q-item__section column q-item__section--${e.avatar===!0||e.side===!0||e.thumbnail===!0?"side":"main"}`+(e.top===!0?" q-item__section--top justify-start":" justify-center")+(e.avatar===!0?" q-item__section--avatar":"")+(e.thumbnail===!0?" q-item__section--thumbnail":"")+(e.noWrap===!0?" q-item__section--nowrap":""));return()=>r("div",{class:n.value},Ce(t.default))}});function bs(e,t,n){t.handler?t.handler(e,n,n.caret):n.runCmd(t.cmd,t.param)}function hr(e){return r("div",{class:"q-editor__toolbar-group"},e)}function ys(e,t,n,l=!1){const a=l||(t.type==="toggle"?t.toggled?t.toggled(e):t.cmd&&e.caret.is(t.cmd,t.param):!1),o=[];if(t.tip&&e.$q.platform.is.desktop){const i=t.key?r("div",[r("small",`(CTRL + ${String.fromCharCode(t.key)})`)]):null;o.push(r(Jf,{delay:1e3},()=>[r("div",{innerHTML:t.tip}),i]))}return r(Xe,{...e.buttonProps.value,icon:t.icon!==null?t.icon:void 0,color:a?t.toggleColor||e.props.toolbarToggleColor:t.color||e.props.toolbarColor,textColor:a&&!e.props.toolbarPush?null:t.textColor||e.props.toolbarTextColor,label:t.label,disable:t.disable?typeof t.disable=="function"?t.disable(e):!0:!1,size:"sm",onClick(i){n&&n(),bs(i,t,e)}},()=>o)}function ev(e,t){const n=t.list==="only-icons";let l=t.label,a=t.icon!==null?t.icon:void 0,o,i;function u(){f.component.proxy.hide()}if(n)i=t.options.map(c=>{const h=c.type===void 0?e.caret.is(c.cmd,c.param):!1;return h&&(l=c.tip,a=c.icon!==null?c.icon:void 0),ys(e,c,u,h)}),o=e.toolbarBackgroundClass.value,i=[hr(i)];else{const c=e.props.toolbarToggleColor!==void 0?`text-${e.props.toolbarToggleColor}`:null,h=e.props.toolbarTextColor!==void 0?`text-${e.props.toolbarTextColor}`:null,m=t.list==="no-icons";i=t.options.map(v=>{const g=v.disable?v.disable(e):!1,_=v.type===void 0?e.caret.is(v.cmd,v.param):!1;_&&(l=v.tip,a=v.icon!==null?v.icon:void 0);const p=v.htmlTip;return r(Il,{active:_,activeClass:c,clickable:!0,disable:g,dense:!0,onClick(k){u(),e.contentRef.value!==null&&e.contentRef.value.focus(),e.caret.restore(),bs(k,v,e)}},()=>[m===!0?null:r(gn,{class:_?c:h,side:!0},()=>r(je,{name:v.icon!==null?v.icon:void 0})),r(gn,p?()=>r("div",{class:"text-no-wrap",innerHTML:v.htmlTip}):v.tip?()=>r("div",{class:"text-no-wrap"},v.tip):void 0)])}),o=[e.toolbarBackgroundClass.value,h]}const d=t.highlight&&l!==t.label,f=r(Kd,{...e.buttonProps.value,noCaps:!0,noWrap:!0,color:d?e.props.toolbarToggleColor:e.props.toolbarColor,textColor:d&&!e.props.toolbarPush?null:e.props.toolbarTextColor,label:t.fixedLabel?t.label:l,icon:t.fixedIcon?t.icon!==null?t.icon:void 0:a,contentClass:o,onShow:c=>e.emit("dropdownShow",c),onHide:c=>e.emit("dropdownHide",c),onBeforeShow:c=>e.emit("dropdownBeforeShow",c),onBeforeHide:c=>e.emit("dropdownBeforeHide",c)},()=>i);return f}function tv(e){if(e.caret)return e.buttons.value.filter(t=>!e.isViewingSource.value||t.find(n=>n.cmd==="viewsource")).map(t=>hr(t.map(n=>e.isViewingSource.value&&n.cmd!=="viewsource"?!1:n.type==="slot"?Ce(e.slots[n.slot]):n.type==="dropdown"?ev(e,n):ys(e,n))))}function nv(e,t,n,l={}){const a=Object.keys(l);if(a.length===0)return{};const o={default_font:{cmd:"fontName",param:e,icon:n,tip:t}};return a.forEach(i=>{const u=l[i];o[i]={cmd:"fontName",param:u,icon:n,tip:u,htmlTip:`<font face="${u}">${u}</font>`}}),o}function av(e){if(e.caret){const t=e.props.toolbarColor||e.props.toolbarTextColor;let n=e.editLinkUrl.value;const l=()=>{e.caret.restore(),n!==e.editLinkUrl.value&&document.execCommand("createLink",!1,n===""?" ":n),e.editLinkUrl.value=null};return[r("div",{class:`q-mx-xs text-${t}`},`${e.$q.lang.editor.url}: `),r("input",{key:"qedt_btm_input",class:"col q-editor__link-input",value:n,onInput:a=>{dt(a),n=a.target.value},onKeydown:a=>{if(Hn(a)!==!0)switch(a.keyCode){case 13:return wt(a),l();case 27:wt(a),e.caret.restore(),(!e.editLinkUrl.value||e.editLinkUrl.value==="https://")&&document.execCommand("unlink"),e.editLinkUrl.value=null;break}}}),hr([r(Xe,{key:"qedt_btm_rem",tabindex:-1,...e.buttonProps.value,label:e.$q.lang.label.remove,noCaps:!0,onClick:()=>{e.caret.restore(),document.execCommand("unlink"),e.editLinkUrl.value=null}}),r(Xe,{key:"qedt_btm_upd",...e.buttonProps.value,label:e.$q.lang.label.update,noCaps:!0,onClick:l})])]}}var vi=/^on[A-Z]/;function ps(){const{attrs:e,vnode:t}=ve(),n={listeners:O({}),attributes:O({})};function l(){const a={},o={};for(const i in e)i!=="class"&&i!=="style"&&vi.test(i)===!1&&(a[i]=e[i]);for(const i in t.props)vi.test(i)===!0&&(o[i]=t.props[i]);n.attributes.value=a,n.listeners.value=o}return Da(l),l(),n}var lv=Object.prototype.toString,io=Object.prototype.hasOwnProperty,ov=new Set(["Boolean","Number","String","Function","Array","Date","RegExp"].map(e=>"[object "+e+"]"));function mi(e){if(e!==Object(e)||ov.has(lv.call(e))===!0||e.constructor&&io.call(e,"constructor")===!1&&io.call(e.constructor.prototype,"isPrototypeOf")===!1)return!1;let t;for(t in e);return t===void 0||io.call(e,t)}function br(){let e,t,n,l,a,o,i=arguments[0]||{},u=1,d=!1;const f=arguments.length;for(typeof i=="boolean"&&(d=i,i=arguments[1]||{},u=2),Object(i)!==i&&typeof i!="function"&&(i={}),f===u&&(i=this,u--);u<f;u++)if((e=arguments[u])!==null)for(t in e)n=i[t],l=e[t],i!==l&&(d===!0&&l&&((a=Array.isArray(l))||mi(l)===!0)?(a===!0?o=Array.isArray(n)===!0?n:[]:o=mi(n)===!0?n:{},i[t]=br(d,o,l)):l!==void 0&&(i[t]=l));return i}var ch=te({name:"QEditor",props:{...Ke,...or,modelValue:{type:String,required:!0},readonly:Boolean,disable:Boolean,minHeight:{type:String,default:"10rem"},maxHeight:String,height:String,definitions:Object,fonts:Object,placeholder:String,toolbar:{type:Array,validator:e=>e.length===0||e.every(t=>t.length),default:()=>[["left","center","right","justify"],["bold","italic","underline","strike"],["undo","redo"]]},toolbarColor:String,toolbarBg:String,toolbarTextColor:String,toolbarToggleColor:{type:String,default:"primary"},toolbarOutline:Boolean,toolbarPush:Boolean,toolbarRounded:Boolean,paragraphTag:{type:String,validator:e=>["div","p"].includes(e),default:"div"},contentStyle:Object,contentClass:[Object,Array,String],square:Boolean,flat:Boolean,dense:Boolean},emits:[...rr,"update:modelValue","keydown","click","focus","blur","dropdownShow","dropdownHide","dropdownBeforeShow","dropdownBeforeHide","linkShow","linkHide"],setup(e,{slots:t,emit:n}){const{proxy:l}=ve(),{$q:a}=l,o=We(e,a),{inFullscreen:i,toggleFullscreen:u}=ir(),d=ps(),f=O(null),c=O(null),h=O(null),m=O(!1),v=s(()=>!e.readonly&&!e.disable);let g,_,p=e.modelValue;document.execCommand("defaultParagraphSeparator",!1,e.paragraphTag),g=window.getComputedStyle(document.body).fontFamily;const k=s(()=>e.toolbarBg?` bg-${e.toolbarBg}`:""),w=s(()=>{const M=e.toolbarOutline!==!0&&e.toolbarPush!==!0;return{type:"a",flat:M,noWrap:!0,outline:e.toolbarOutline,push:e.toolbarPush,rounded:e.toolbarRounded,dense:!0,color:e.toolbarColor,disable:!v.value,size:"sm"}}),y=s(()=>{const M=a.lang.editor,D=a.iconSet.editor;return{bold:{cmd:"bold",icon:D.bold,tip:M.bold,key:66},italic:{cmd:"italic",icon:D.italic,tip:M.italic,key:73},strike:{cmd:"strikeThrough",icon:D.strikethrough,tip:M.strikethrough,key:83},underline:{cmd:"underline",icon:D.underline,tip:M.underline,key:85},unordered:{cmd:"insertUnorderedList",icon:D.unorderedList,tip:M.unorderedList},ordered:{cmd:"insertOrderedList",icon:D.orderedList,tip:M.orderedList},subscript:{cmd:"subscript",icon:D.subscript,tip:M.subscript,htmlTip:"x<subscript>2</subscript>"},superscript:{cmd:"superscript",icon:D.superscript,tip:M.superscript,htmlTip:"x<superscript>2</superscript>"},link:{cmd:"link",disable:le=>le.caret&&!le.caret.can("link"),icon:D.hyperlink,tip:M.hyperlink,key:76},fullscreen:{cmd:"fullscreen",icon:D.toggleFullscreen,tip:M.toggleFullscreen,key:70},viewsource:{cmd:"viewsource",icon:D.viewSource,tip:M.viewSource},quote:{cmd:"formatBlock",param:"BLOCKQUOTE",icon:D.quote,tip:M.quote,key:81},left:{cmd:"justifyLeft",icon:D.left,tip:M.left},center:{cmd:"justifyCenter",icon:D.center,tip:M.center},right:{cmd:"justifyRight",icon:D.right,tip:M.right},justify:{cmd:"justifyFull",icon:D.justify,tip:M.justify},print:{type:"no-state",cmd:"print",icon:D.print,tip:M.print,key:80},outdent:{type:"no-state",disable:le=>le.caret&&!le.caret.can("outdent"),cmd:"outdent",icon:D.outdent,tip:M.outdent},indent:{type:"no-state",disable:le=>le.caret&&!le.caret.can("indent"),cmd:"indent",icon:D.indent,tip:M.indent},removeFormat:{type:"no-state",cmd:"removeFormat",icon:D.removeFormat,tip:M.removeFormat},hr:{type:"no-state",cmd:"insertHorizontalRule",icon:D.hr,tip:M.hr},undo:{type:"no-state",cmd:"undo",icon:D.undo,tip:M.undo,key:90},redo:{type:"no-state",cmd:"redo",icon:D.redo,tip:M.redo,key:89},h1:{cmd:"formatBlock",param:"H1",icon:D.heading1||D.heading,tip:M.heading1,htmlTip:`<h1 class="q-ma-none">${M.heading1}</h1>`},h2:{cmd:"formatBlock",param:"H2",icon:D.heading2||D.heading,tip:M.heading2,htmlTip:`<h2 class="q-ma-none">${M.heading2}</h2>`},h3:{cmd:"formatBlock",param:"H3",icon:D.heading3||D.heading,tip:M.heading3,htmlTip:`<h3 class="q-ma-none">${M.heading3}</h3>`},h4:{cmd:"formatBlock",param:"H4",icon:D.heading4||D.heading,tip:M.heading4,htmlTip:`<h4 class="q-ma-none">${M.heading4}</h4>`},h5:{cmd:"formatBlock",param:"H5",icon:D.heading5||D.heading,tip:M.heading5,htmlTip:`<h5 class="q-ma-none">${M.heading5}</h5>`},h6:{cmd:"formatBlock",param:"H6",icon:D.heading6||D.heading,tip:M.heading6,htmlTip:`<h6 class="q-ma-none">${M.heading6}</h6>`},p:{cmd:"formatBlock",param:e.paragraphTag,icon:D.heading,tip:M.paragraph},code:{cmd:"formatBlock",param:"PRE",icon:D.code,htmlTip:`<code>${M.code}</code>`},"size-1":{cmd:"fontSize",param:"1",icon:D.size1||D.size,tip:M.size1,htmlTip:`<font size="1">${M.size1}</font>`},"size-2":{cmd:"fontSize",param:"2",icon:D.size2||D.size,tip:M.size2,htmlTip:`<font size="2">${M.size2}</font>`},"size-3":{cmd:"fontSize",param:"3",icon:D.size3||D.size,tip:M.size3,htmlTip:`<font size="3">${M.size3}</font>`},"size-4":{cmd:"fontSize",param:"4",icon:D.size4||D.size,tip:M.size4,htmlTip:`<font size="4">${M.size4}</font>`},"size-5":{cmd:"fontSize",param:"5",icon:D.size5||D.size,tip:M.size5,htmlTip:`<font size="5">${M.size5}</font>`},"size-6":{cmd:"fontSize",param:"6",icon:D.size6||D.size,tip:M.size6,htmlTip:`<font size="6">${M.size6}</font>`},"size-7":{cmd:"fontSize",param:"7",icon:D.size7||D.size,tip:M.size7,htmlTip:`<font size="7">${M.size7}</font>`}}}),b=s(()=>{const M=e.definitions||{},D=e.definitions||e.fonts?br(!0,{},y.value,M,nv(g,a.lang.editor.defaultFont,a.iconSet.editor.font,e.fonts)):y.value;return e.toolbar.map(le=>le.map(H=>{if(H.options)return{type:"dropdown",icon:H.icon,label:H.label,size:"sm",dense:!0,fixedLabel:H.fixedLabel,fixedIcon:H.fixedIcon,highlight:H.highlight,list:H.list,options:H.options.map(xe=>D[xe])};const de=D[H];return de?de.type==="no-state"||M[H]&&(de.cmd===void 0||y.value[de.cmd]&&y.value[de.cmd].type==="no-state")?de:Object.assign({type:"toggle"},de):{type:"slot",slot:H}}))}),S={$q:a,props:e,slots:t,emit:n,inFullscreen:i,toggleFullscreen:u,runCmd:C,isViewingSource:m,editLinkUrl:h,toolbarBackgroundClass:k,buttonProps:w,contentRef:c,buttons:b,setContent:I};ne(()=>e.modelValue,M=>{p!==M&&(p=M,I(M,!0))}),ne(h,M=>{n(`link${M?"Show":"Hide"}`)});const B=s(()=>e.toolbar&&e.toolbar.length!==0),L=s(()=>{const M={},D=le=>{le.key&&(M[le.key]={cmd:le.cmd,param:le.param})};return b.value.forEach(le=>{le.forEach(H=>{H.options?H.options.forEach(D):D(H)})}),M}),R=s(()=>i.value?e.contentStyle:[{minHeight:e.minHeight,height:e.height,maxHeight:e.maxHeight},e.contentStyle]),A=s(()=>`q-editor q-editor--${m.value===!0?"source":"default"}`+(e.disable===!0?" disabled":"")+(i.value===!0?" fullscreen column":"")+(e.square===!0?" q-editor--square no-border-radius":"")+(e.flat===!0?" q-editor--flat":"")+(e.dense===!0?" q-editor--dense":"")+(o.value===!0?" q-editor--dark q-dark":"")),P=s(()=>[e.contentClass,"q-editor__content",{col:i.value,"overflow-auto":i.value||e.maxHeight}]),z=s(()=>e.disable===!0?{"aria-disabled":"true"}:{});function T(){if(c.value!==null){const M=`inner${m.value===!0?"Text":"HTML"}`,D=c.value[M];D!==e.modelValue&&(p=D,n("update:modelValue",D))}}function x(M){if(n("keydown",M),M.ctrlKey!==!0||Hn(M)===!0){K();return}const D=M.keyCode,le=L.value[D];if(le!==void 0){const{cmd:H,param:de}=le;Fe(M),C(H,de,!1)}}function q(M){K(),n("click",M)}function U(M){if(c.value!==null){const{scrollTop:D,scrollHeight:le}=c.value;_=le-D}S.caret.save(),n("blur",M)}function W(M){Qe(()=>{c.value!==null&&_!==void 0&&(c.value.scrollTop=c.value.scrollHeight-_)}),n("focus",M)}function E(M){const D=f.value;if(D!==null&&D.contains(M.target)===!0&&(M.relatedTarget===null||D.contains(M.relatedTarget)!==!0)){const le=`inner${m.value===!0?"Text":"HTML"}`;S.caret.restorePosition(c.value[le].length),K()}}function V(M){const D=f.value;D!==null&&D.contains(M.target)===!0&&(M.relatedTarget===null||D.contains(M.relatedTarget)!==!0)&&(S.caret.savePosition(),K())}function j(){_=void 0}function ue(M){S.caret.save()}function I(M,D){if(c.value!==null){D===!0&&S.caret.savePosition();const le=`inner${m.value===!0?"Text":"HTML"}`;c.value[le]=M,D===!0&&(S.caret.restorePosition(c.value[le].length),K())}}function C(M,D,le=!0){Q(),S.caret.restore(),S.caret.apply(M,D,()=>{Q(),S.caret.save(),le&&K()})}function K(){setTimeout(()=>{h.value=null,l.$forceUpdate()},1)}function Q(){ha(()=>{c.value!==null&&c.value.focus({preventScroll:!0})})}function oe(){return c.value}return ft(()=>{S.caret=l.caret=new Zf(c.value,S),I(e.modelValue),K(),document.addEventListener("selectionchange",ue)}),Ne(()=>{document.removeEventListener("selectionchange",ue)}),Object.assign(l,{runCmd:C,refreshToolbar:K,focus:Q,getContentEl:oe}),()=>{let M;if(B.value){const D=[r("div",{key:"qedt_top",class:"q-editor__toolbar row no-wrap scroll-x"+k.value},tv(S))];h.value!==null&&D.push(r("div",{key:"qedt_btm",class:"q-editor__toolbar row no-wrap items-center scroll-x"+k.value},av(S))),M=r("div",{key:"toolbar_ctainer",class:"q-editor__toolbars-container"},D)}return r("div",{ref:f,class:A.value,style:{height:i.value===!0?"100%":null},...z.value,onFocusin:E,onFocusout:V},[M,r("div",{ref:c,style:R.value,class:P.value,contenteditable:v.value,placeholder:e.placeholder,...d.listeners.value,onInput:T,onKeydown:x,onClick:q,onBlur:U,onFocus:W,onMousedown:j,onTouchstartPassive:j})])}}}),$o=te({name:"QItemLabel",props:{overline:Boolean,caption:Boolean,header:Boolean,lines:[Number,String]},setup(e,{slots:t}){const n=s(()=>parseInt(e.lines,10)),l=s(()=>"q-item__label"+(e.overline===!0?" q-item__label--overline text-overline":"")+(e.caption===!0?" q-item__label--caption text-caption":"")+(e.header===!0?" q-item__label--header":"")+(n.value===1?" ellipsis":"")),a=s(()=>e.lines!==void 0&&n.value>1?{overflow:"hidden",display:"-webkit-box","-webkit-box-orient":"vertical","-webkit-line-clamp":n.value}:null);return()=>r("div",{style:a.value,class:l.value},Ce(t.default))}}),yr=te({name:"QSlideTransition",props:{appear:Boolean,duration:{type:Number,default:300}},emits:["show","hide"],setup(e,{slots:t,emit:n}){let l=!1,a,o,i=null,u=null,d,f;function c(){a&&a(),a=null,l=!1,i!==null&&(clearTimeout(i),i=null),u!==null&&(clearTimeout(u),u=null),o!==void 0&&o.removeEventListener("transitionend",d),d=null}function h(_,p,k){p!==void 0&&(_.style.height=`${p}px`),_.style.transition=`height ${e.duration}ms cubic-bezier(.25, .8, .50, 1)`,l=!0,a=k}function m(_,p){_.style.overflowY=null,_.style.height=null,_.style.transition=null,c(),p!==f&&n(p)}function v(_,p){let k=0;o=_,l===!0?(c(),k=_.offsetHeight===_.scrollHeight?0:void 0):(f="hide",_.style.overflowY="hidden"),h(_,k,p),i=setTimeout(()=>{i=null,_.style.height=`${_.scrollHeight}px`,d=w=>{u=null,(Object(w)!==w||w.target===_)&&m(_,"show")},_.addEventListener("transitionend",d),u=setTimeout(d,e.duration*1.1)},100)}function g(_,p){let k;o=_,l===!0?c():(f="show",_.style.overflowY="hidden",k=_.scrollHeight),h(_,k,p),i=setTimeout(()=>{i=null,_.style.height=0,d=w=>{u=null,(Object(w)!==w||w.target===_)&&m(_,"hide")},_.addEventListener("transitionend",d),u=setTimeout(d,e.duration*1.1)},100)}return Ne(()=>{l===!0&&c()}),()=>r(_t,{css:!1,appear:e.appear,onEnter:v,onLeave:g},t.default)}}),rv={true:"inset",item:"item-inset","item-thumbnail":"item-thumbnail-inset"},uo={xs:2,sm:4,md:8,lg:16,xl:24},Dn=te({name:"QSeparator",props:{...Ke,spaced:[Boolean,String],inset:[Boolean,String],vertical:Boolean,color:String,size:String},setup(e){const t=ve(),n=We(e,t.proxy.$q),l=s(()=>e.vertical===!0?"vertical":"horizontal"),a=s(()=>` q-separator--${l.value}`),o=s(()=>e.inset!==!1?`${a.value}-${rv[e.inset]}`:""),i=s(()=>`q-separator${a.value}${o.value}`+(e.color!==void 0?` bg-${e.color}`:"")+(n.value===!0?" q-separator--dark":"")),u=s(()=>{const d={};if(e.size!==void 0&&(d[e.vertical===!0?"width":"height"]=e.size),e.spaced!==!1){const f=e.spaced===!0?`${uo.md}px`:e.spaced in uo?`${uo[e.spaced]}px`:e.spaced,c=e.vertical===!0?["Left","Right"]:["Top","Bottom"];d[`margin${c[0]}`]=d[`margin${c[1]}`]=f}return d});return()=>r("hr",{class:i.value,style:u.value,"aria-orientation":l.value})}}),Cn=yc({}),iv=Object.keys(Na),dh=te({name:"QExpansionItem",props:{...Na,...va,...Ke,icon:String,label:String,labelLines:[Number,String],caption:String,captionLines:[Number,String],dense:Boolean,toggleAriaLabel:String,expandIcon:String,expandedIcon:String,expandIconClass:[Array,String,Object],duration:{},headerInsetLevel:Number,contentInsetLevel:Number,expandSeparator:Boolean,defaultOpened:Boolean,hideExpandIcon:Boolean,expandIconToggle:Boolean,switchToggleSide:Boolean,denseToggle:Boolean,group:String,popup:Boolean,headerStyle:[Array,String,Object],headerClass:[Array,String,Object]},emits:[...ma,"click","afterShow","afterHide"],setup(e,{slots:t,emit:n}){const{proxy:{$q:l}}=ve(),a=We(e,l),o=O(e.modelValue!==null?e.modelValue:e.defaultOpened),i=O(null),u=Vl(),{show:d,hide:f,toggle:c}=ga({showing:o});let h,m;const v=s(()=>`q-expansion-item q-item-type q-expansion-item--${o.value===!0?"expanded":"collapsed"} q-expansion-item--${e.popup===!0?"popup":"standard"}`),g=s(()=>{if(e.contentInsetLevel===void 0)return null;const E=l.lang.rtl===!0?"Right":"Left";return{["padding"+E]:e.contentInsetLevel*56+"px"}}),_=s(()=>e.disable!==!0&&(e.href!==void 0||e.to!==void 0&&e.to!==null&&e.to!=="")),p=s(()=>{const E={};return iv.forEach(V=>{E[V]=e[V]}),E}),k=s(()=>_.value===!0||e.expandIconToggle!==!0),w=s(()=>e.expandedIcon!==void 0&&o.value===!0?e.expandedIcon:e.expandIcon||l.iconSet.expansionItem[e.denseToggle===!0?"denseIcon":"icon"]),y=s(()=>e.disable!==!0&&(_.value===!0||e.expandIconToggle===!0)),b=s(()=>({expanded:o.value===!0,detailsId:u.value,toggle:c,show:d,hide:f})),S=s(()=>{const E=e.toggleAriaLabel!==void 0?e.toggleAriaLabel:l.lang.label[o.value===!0?"collapse":"expand"](e.label);return{role:"button","aria-expanded":o.value===!0?"true":"false","aria-controls":u.value,"aria-label":E}});ne(()=>e.group,E=>{m!==void 0&&m(),E!==void 0&&z()});function B(E){_.value!==!0&&c(E),n("click",E)}function L(E){E.keyCode===13&&R(E,!0)}function R(E,V){V!==!0&&i.value!==null&&i.value.focus(),c(E),Fe(E)}function A(){n("afterShow")}function P(){n("afterHide")}function z(){h===void 0&&(h=Fa()),o.value===!0&&(Cn[e.group]=h);const E=ne(o,j=>{j===!0?Cn[e.group]=h:Cn[e.group]===h&&delete Cn[e.group]}),V=ne(()=>Cn[e.group],(j,ue)=>{ue===h&&j!==void 0&&j!==h&&f()});m=()=>{E(),V(),Cn[e.group]===h&&delete Cn[e.group],m=void 0}}function T(){const E={class:[`q-focusable relative-position cursor-pointer${e.denseToggle===!0&&e.switchToggleSide===!0?" items-end":""}`,e.expandIconClass],side:e.switchToggleSide!==!0,avatar:e.switchToggleSide},V=[r(je,{class:"q-expansion-item__toggle-icon"+(e.expandedIcon===void 0&&o.value===!0?" q-expansion-item__toggle-icon--rotated":""),name:w.value})];return y.value===!0&&(Object.assign(E,{tabindex:0,...S.value,onClick:R,onKeyup:L}),V.unshift(r("div",{ref:i,class:"q-expansion-item__toggle-focus q-icon q-focus-helper q-focus-helper--rounded",tabindex:-1}))),r(gn,E,()=>V)}function x(){let E;return t.header!==void 0?E=[].concat(t.header(b.value)):(E=[r(gn,()=>[r($o,{lines:e.labelLines},()=>e.label||""),e.caption?r($o,{lines:e.captionLines,caption:!0},()=>e.caption):null])],e.icon&&E[e.switchToggleSide===!0?"push":"unshift"](r(gn,{side:e.switchToggleSide===!0,avatar:e.switchToggleSide!==!0},()=>r(je,{name:e.icon})))),e.disable!==!0&&e.hideExpandIcon!==!0&&E[e.switchToggleSide===!0?"unshift":"push"](T()),E}function q(){const E={ref:"item",style:e.headerStyle,class:e.headerClass,dark:a.value,disable:e.disable,dense:e.dense,insetLevel:e.headerInsetLevel};return k.value===!0&&(E.clickable=!0,E.onClick=B,Object.assign(E,_.value===!0?p.value:S.value)),r(Il,E,x)}function U(){return Ut(r("div",{key:"e-content",class:"q-expansion-item__content relative-position",style:g.value,id:u.value},Ce(t.default)),[[Zi,o.value]])}function W(){const E=[q(),r(yr,{duration:e.duration,onShow:A,onHide:P},U)];return e.expandSeparator===!0&&E.push(r(Dn,{class:"q-expansion-item__border q-expansion-item__border--top absolute-top",dark:a.value}),r(Dn,{class:"q-expansion-item__border q-expansion-item__border--bottom absolute-bottom",dark:a.value})),E}return e.group!==void 0&&z(),Ne(()=>{m!==void 0&&m()}),()=>r("div",{class:v.value},[r("div",{class:"q-expansion-item__container relative-position"},W())])}}),uv=["top","right","bottom","left"],Bo={type:{type:String,default:"a"},outline:Boolean,push:Boolean,flat:Boolean,unelevated:Boolean,color:String,textColor:String,glossy:Boolean,square:Boolean,padding:String,label:{type:[String,Number],default:""},labelPosition:{type:String,default:"right",validator:e=>uv.includes(e)},externalLabel:Boolean,hideLabel:{type:Boolean},labelClass:[Array,String,Object],labelStyle:[Array,String,Object],disable:Boolean,tabindex:[Number,String]};function ws(e,t){return{formClass:s(()=>`q-fab--form-${e.square===!0?"square":"rounded"}`),stacked:s(()=>e.externalLabel===!1&&["top","bottom"].includes(e.labelPosition)),labelProps:s(()=>{if(e.externalLabel===!0){const n=e.hideLabel===null?t.value===!1:e.hideLabel;return{action:"push",data:{class:[e.labelClass,`q-fab__label q-tooltip--style q-fab__label--external q-fab__label--external-${e.labelPosition}`+(n===!0?" q-fab__label--external-hidden":"")],style:e.labelStyle}}}return{action:["left","top"].includes(e.labelPosition)?"unshift":"push",data:{class:[e.labelClass,`q-fab__label q-fab__label--internal q-fab__label--internal-${e.labelPosition}`+(e.hideLabel===!0?" q-fab__label--internal-hidden":"")],style:e.labelStyle}}})}}var sv=["up","right","down","left"],cv=["left","center","right"],fh=te({name:"QFab",props:{...Bo,...va,icon:String,activeIcon:String,hideIcon:Boolean,hideLabel:{...Bo.hideLabel,default:null},direction:{type:String,default:"right",validator:e=>sv.includes(e)},persistent:Boolean,verticalActionsAlign:{type:String,default:"center",validator:e=>cv.includes(e)}},emits:ma,setup(e,{slots:t}){const n=O(null),l=O(e.modelValue===!0),a=Vl(),{proxy:{$q:o}}=ve(),{formClass:i,labelProps:u}=ws(e,l),d=s(()=>e.persistent!==!0),{hide:f,toggle:c}=ga({showing:l,hideOnRouteChange:d}),h=s(()=>({opened:l.value})),m=s(()=>`q-fab z-fab row inline justify-center q-fab--align-${e.verticalActionsAlign} ${i.value}`+(l.value===!0?" q-fab--opened":" q-fab--closed")),v=s(()=>`q-fab__actions flex no-wrap inline q-fab__actions--${e.direction} q-fab__actions--${l.value===!0?"opened":"closed"}`),g=s(()=>{const w={id:a.value,role:"menu"};return l.value!==!0&&(w["aria-hidden"]="true"),w}),_=s(()=>`q-fab__icon-holder  q-fab__icon-holder--${l.value===!0?"opened":"closed"}`);function p(w,y){const b=t[w],S=`q-fab__${w} absolute-full`;return b===void 0?r(je,{class:S,name:e[y]||o.iconSet.fab[y]}):r("div",{class:S},b(h.value))}function k(){const w=[];return e.hideIcon!==!0&&w.push(r("div",{class:_.value},[p("icon","icon"),p("active-icon","activeIcon")])),(e.label!==""||t.label!==void 0)&&w[u.value.action](r("div",u.value.data,t.label!==void 0?t.label(h.value):[e.label])),mt(t.tooltip,w)}return yn(cu,{showing:l,onChildClick(w){f(w),n.value!==null&&n.value.$el.focus()}}),()=>r("div",{class:m.value},[r(Xe,{ref:n,class:i.value,...e,noWrap:!0,stack:e.stacked,align:void 0,icon:void 0,label:void 0,noCaps:!0,fab:!0,"aria-expanded":l.value===!0?"true":"false","aria-haspopup":"true","aria-controls":a.value,onClick:c},k),r("div",{class:v.value,...g.value},Ce(t.default))])}}),_s={start:"self-end",center:"self-center",end:"self-start"},dv=Object.keys(_s),vh=te({name:"QFabAction",props:{...Bo,icon:{type:String,default:""},anchor:{type:String,validator:e=>dv.includes(e)},to:[String,Object],replace:Boolean},emits:["click"],setup(e,{slots:t,emit:n}){const l=Et(cu,()=>({showing:{value:!0},onChildClick:it})),{formClass:a,labelProps:o}=ws(e,l.showing),i=s(()=>{const h=_s[e.anchor];return a.value+(h!==void 0?` ${h}`:"")}),u=s(()=>e.disable===!0||l.showing.value!==!0);function d(h){l.onChildClick(h),n("click",h)}function f(){const h=[];return t.icon!==void 0?h.push(t.icon()):e.icon!==""&&h.push(r(je,{name:e.icon})),(e.label!==""||t.label!==void 0)&&h[o.value.action](r("div",o.value.data,t.label!==void 0?t.label():[e.label])),mt(t.default,h)}const c=ve();return Object.assign(c.proxy,{click:d}),()=>r(Xe,{class:i.value,...e,noWrap:!0,stack:e.stacked,icon:void 0,label:void 0,noCaps:!0,fabMini:!0,disable:u.value,onClick:d},f)}});function fv({validate:e,resetValidation:t,requiresQForm:n}){const l=Et(na,!1);if(l!==!1){const{props:a,proxy:o}=ve();Object.assign(o,{validate:e,resetValidation:t}),ne(()=>a.disable,i=>{i===!0?(typeof t=="function"&&t(),l.unbindComponent(o)):l.bindComponent(o)}),ft(()=>{a.disable!==!0&&l.bindComponent(o)}),Ne(()=>{a.disable!==!0&&l.unbindComponent(o)})}else n===!0&&console.error("Parent QForm not found on useFormChild()!")}var vv=[!0,!1,"ondemand"],mv={modelValue:{},error:{type:Boolean,default:null},errorMessage:String,noErrorIcon:Boolean,rules:Array,reactiveRules:Boolean,lazyRules:{type:[Boolean,String],default:!1,validator:e=>vv.includes(e)}};function gv(e,t){const{props:n,proxy:l}=ve(),a=O(!1),o=O(null),i=O(!1);fv({validate:_,resetValidation:g});let u=0,d;const f=s(()=>n.rules!==void 0&&n.rules!==null&&n.rules.length!==0),c=s(()=>n.disable!==!0&&f.value===!0&&t.value===!1),h=s(()=>n.error===!0||a.value===!0),m=s(()=>typeof n.errorMessage=="string"&&n.errorMessage.length!==0?n.errorMessage:o.value);ne(()=>n.modelValue,()=>{i.value=!0,c.value===!0&&n.lazyRules===!1&&p()});function v(){n.lazyRules!=="ondemand"&&c.value===!0&&i.value===!0&&p()}ne(()=>n.reactiveRules,k=>{k===!0?d===void 0&&(d=ne(()=>n.rules,v,{immediate:!0,deep:!0})):d!==void 0&&(d(),d=void 0)},{immediate:!0}),ne(()=>n.lazyRules,v),ne(e,k=>{k===!0?i.value=!0:c.value===!0&&n.lazyRules!=="ondemand"&&p()});function g(){u++,t.value=!1,i.value=!1,a.value=!1,o.value=null,p.cancel()}function _(k=n.modelValue){if(n.disable===!0||f.value===!1)return!0;const w=++u,y=t.value!==!0?()=>{i.value=!0}:()=>{},b=(B,L)=>{B===!0&&y(),a.value=B,o.value=L||null,t.value=!1},S=[];for(let B=0;B<n.rules.length;B++){const L=n.rules[B];let R;if(typeof L=="function"?R=L(k,Ba):typeof L=="string"&&Ba[L]!==void 0&&(R=Ba[L](k)),R===!1||typeof R=="string")return b(!0,R),!1;R!==!0&&R!==void 0&&S.push(R)}return S.length===0?(b(!1),!0):(t.value=!0,Promise.all(S).then(B=>{if(B===void 0||Array.isArray(B)===!1||B.length===0)return w===u&&b(!1),!0;const L=B.find(R=>R===!1||typeof R=="string");return w===u&&b(L!==void 0,L),L===void 0},B=>(w===u&&(console.error(B),b(!0)),!1)))}const p=fa(_,0);return Ne(()=>{d!==void 0&&d(),p.cancel()}),Object.assign(l,{resetValidation:g,validate:_}),St(l,"hasError",()=>h.value),{isDirtyModel:i,hasRules:f,hasError:h,errorMessage:m,validate:_,resetValidation:g}}function In(e){return e!=null&&(""+e).length!==0}var Ss={...Ke,...mv,label:String,stackLabel:Boolean,hint:String,hideHint:Boolean,prefix:String,suffix:String,labelColor:String,color:String,bgColor:String,filled:Boolean,outlined:Boolean,borderless:Boolean,standout:[Boolean,String],square:Boolean,loading:Boolean,labelSlot:Boolean,bottomSlots:Boolean,hideBottomSpace:Boolean,rounded:Boolean,dense:Boolean,itemAligned:Boolean,counter:Boolean,clearable:Boolean,clearIcon:String,disable:Boolean,readonly:Boolean,autofocus:Boolean,for:String},Hl={...Ss,maxlength:[Number,String]},Nl=["update:modelValue","clear","focus","blur"];function Ql({requiredForAttr:e=!0,tagProp:t,changeEvent:n=!1}={}){const{props:l,proxy:a}=ve(),o=We(l,a.$q),i=Vl({required:e,getValue:()=>l.for});return{requiredForAttr:e,changeEvent:n,tag:t===!0?s(()=>l.tag):{value:"label"},isDark:o,editable:s(()=>l.disable!==!0&&l.readonly!==!0),innerLoading:O(!1),focused:O(!1),hasPopupOpen:!1,splitAttrs:ps(),targetUid:i,rootRef:O(null),targetRef:O(null),controlRef:O(null)}}function jl(e){const{props:t,emit:n,slots:l,attrs:a,proxy:o}=ve(),{$q:i}=o;let u=null;e.hasValue===void 0&&(e.hasValue=s(()=>In(t.modelValue))),e.emitValue===void 0&&(e.emitValue=V=>{n("update:modelValue",V)}),e.controlEvents===void 0&&(e.controlEvents={onFocusin:A,onFocusout:P}),Object.assign(e,{clearValue:z,onControlFocusin:A,onControlFocusout:P,focus:L}),e.computedCounter===void 0&&(e.computedCounter=s(()=>{if(t.counter!==!1){const V=typeof t.modelValue=="string"||typeof t.modelValue=="number"?(""+t.modelValue).length:Array.isArray(t.modelValue)===!0?t.modelValue.length:0,j=t.maxlength!==void 0?t.maxlength:t.maxValues;return V+(j!==void 0?" / "+j:"")}}));const{isDirtyModel:d,hasRules:f,hasError:c,errorMessage:h,resetValidation:m}=gv(e.focused,e.innerLoading),v=e.floatingLabel!==void 0?s(()=>t.stackLabel===!0||e.focused.value===!0||e.floatingLabel.value===!0):s(()=>t.stackLabel===!0||e.focused.value===!0||e.hasValue.value===!0),g=s(()=>t.bottomSlots===!0||t.hint!==void 0||f.value===!0||t.counter===!0||t.error!==null),_=s(()=>t.filled===!0?"filled":t.outlined===!0?"outlined":t.borderless===!0?"borderless":t.standout?"standout":"standard"),p=s(()=>`q-field row no-wrap items-start q-field--${_.value}`+(e.fieldClass!==void 0?` ${e.fieldClass.value}`:"")+(t.rounded===!0?" q-field--rounded":"")+(t.square===!0?" q-field--square":"")+(v.value===!0?" q-field--float":"")+(w.value===!0?" q-field--labeled":"")+(t.dense===!0?" q-field--dense":"")+(t.itemAligned===!0?" q-field--item-aligned q-item-type":"")+(e.isDark.value===!0?" q-field--dark":"")+(e.getControl===void 0?" q-field--auto-height":"")+(e.focused.value===!0?" q-field--focused":"")+(c.value===!0?" q-field--error":"")+(c.value===!0||e.focused.value===!0?" q-field--highlighted":"")+(t.hideBottomSpace!==!0&&g.value===!0?" q-field--with-bottom":"")+(t.disable===!0?" q-field--disabled":t.readonly===!0?" q-field--readonly":"")),k=s(()=>"q-field__control relative-position row no-wrap"+(t.bgColor!==void 0?` bg-${t.bgColor}`:"")+(c.value===!0?" text-negative":typeof t.standout=="string"&&t.standout.length!==0&&e.focused.value===!0?` ${t.standout}`:t.color!==void 0?` text-${t.color}`:"")),w=s(()=>t.labelSlot===!0||t.label!==void 0),y=s(()=>"q-field__label no-pointer-events absolute ellipsis"+(t.labelColor!==void 0&&c.value!==!0?` text-${t.labelColor}`:"")),b=s(()=>({id:e.targetUid.value,editable:e.editable.value,focused:e.focused.value,floatingLabel:v.value,modelValue:t.modelValue,emitValue:e.emitValue})),S=s(()=>{const V={};return e.targetUid.value&&(V.for=e.targetUid.value),t.disable===!0&&(V["aria-disabled"]="true"),V});function B(){const V=document.activeElement;let j=e.targetRef!==void 0&&e.targetRef.value;j&&(V===null||V.id!==e.targetUid.value)&&(j.hasAttribute("tabindex")===!0||(j=j.querySelector("[tabindex]")),j&&j!==V&&j.focus({preventScroll:!0}))}function L(){ha(B)}function R(){Td(B);const V=document.activeElement;V!==null&&e.rootRef.value.contains(V)&&V.blur()}function A(V){u!==null&&(clearTimeout(u),u=null),e.editable.value===!0&&e.focused.value===!1&&(e.focused.value=!0,n("focus",V))}function P(V,j){u!==null&&clearTimeout(u),u=setTimeout(()=>{u=null,!(document.hasFocus()===!0&&(e.hasPopupOpen===!0||e.controlRef===void 0||e.controlRef.value===null||e.controlRef.value.contains(document.activeElement)!==!1))&&(e.focused.value===!0&&(e.focused.value=!1,n("blur",V)),j!==void 0&&j())})}function z(V){Fe(V),i.platform.is.mobile!==!0?(e.targetRef!==void 0&&e.targetRef.value||e.rootRef.value).focus():e.rootRef.value.contains(document.activeElement)===!0&&document.activeElement.blur(),t.type==="file"&&(e.inputRef.value.value=null),n("update:modelValue",null),e.changeEvent===!0&&n("change",null),n("clear",t.modelValue),Qe(()=>{const j=d.value;m(),d.value=j})}function T(V){[13,32].includes(V.keyCode)&&z(V)}function x(){const V=[];return l.prepend!==void 0&&V.push(r("div",{class:"q-field__prepend q-field__marginal row no-wrap items-center",key:"prepend",onClick:wt},l.prepend())),V.push(r("div",{class:"q-field__control-container col relative-position row no-wrap q-anchor--skip"},q())),c.value===!0&&t.noErrorIcon===!1&&V.push(W("error",[r(je,{name:i.iconSet.field.error,color:"negative"})])),t.loading===!0||e.innerLoading.value===!0?V.push(W("inner-loading-append",l.loading!==void 0?l.loading():[r(It,{color:t.color})])):t.clearable===!0&&e.hasValue.value===!0&&e.editable.value===!0&&V.push(W("inner-clearable-append",[r(je,{class:"q-field__focusable-action",name:t.clearIcon||i.iconSet.field.clear,tabindex:0,role:"button","aria-hidden":"false","aria-label":i.lang.label.clear,onKeyup:T,onClick:z})])),l.append!==void 0&&V.push(r("div",{class:"q-field__append q-field__marginal row no-wrap items-center",key:"append",onClick:wt},l.append())),e.getInnerAppend!==void 0&&V.push(W("inner-append",e.getInnerAppend())),e.getControlChild!==void 0&&V.push(e.getControlChild()),V}function q(){const V=[];return t.prefix!==void 0&&t.prefix!==null&&V.push(r("div",{class:"q-field__prefix no-pointer-events row items-center"},t.prefix)),e.getShadowControl!==void 0&&e.hasShadow.value===!0&&V.push(e.getShadowControl()),e.getControl!==void 0?V.push(e.getControl()):l.rawControl!==void 0?V.push(l.rawControl()):l.control!==void 0&&V.push(r("div",{ref:e.targetRef,class:"q-field__native row",tabindex:-1,...e.splitAttrs.attributes.value,"data-autofocus":t.autofocus===!0||void 0},l.control(b.value))),w.value===!0&&V.push(r("div",{class:y.value},Ce(l.label,t.label))),t.suffix!==void 0&&t.suffix!==null&&V.push(r("div",{class:"q-field__suffix no-pointer-events row items-center"},t.suffix)),V.concat(Ce(l.default))}function U(){let V,j;c.value===!0?h.value!==null?(V=[r("div",{role:"alert"},h.value)],j=`q--slot-error-${h.value}`):(V=Ce(l.error),j="q--slot-error"):(t.hideHint!==!0||e.focused.value===!0)&&(t.hint!==void 0?(V=[r("div",t.hint)],j=`q--slot-hint-${t.hint}`):(V=Ce(l.hint),j="q--slot-hint"));const ue=t.counter===!0||l.counter!==void 0;if(t.hideBottomSpace===!0&&ue===!1&&V===void 0)return;const I=r("div",{key:j,class:"q-field__messages col"},V);return r("div",{class:"q-field__bottom row items-start q-field__bottom--"+(t.hideBottomSpace!==!0?"animated":"stale"),onClick:wt},[t.hideBottomSpace===!0?I:r(_t,{name:"q-transition--field-message"},()=>I),ue===!0?r("div",{class:"q-field__counter"},l.counter!==void 0?l.counter():e.computedCounter.value):null])}function W(V,j){return j===null?null:r("div",{key:V,class:"q-field__append q-field__marginal row no-wrap items-center q-anchor--skip"},j)}let E=!1;return Yt(()=>{E=!0}),bn(()=>{E===!0&&t.autofocus===!0&&o.focus()}),t.autofocus===!0&&ft(()=>{o.focus()}),Ne(()=>{u!==null&&clearTimeout(u)}),Object.assign(o,{focus:L,blur:R}),function(){const j=e.getControl===void 0&&l.control===void 0?{...e.splitAttrs.attributes.value,"data-autofocus":t.autofocus===!0||void 0,...S.value}:S.value;return r(e.tag.value,{ref:e.rootRef,class:[p.value,a.class],style:a.style,...j},[l.before!==void 0?r("div",{class:"q-field__before q-field__marginal row no-wrap items-center",onClick:wt},l.before()):null,r("div",{class:"q-field__inner relative-position col self-stretch"},[r("div",{ref:e.controlRef,class:k.value,tabindex:-1,...e.controlEvents},x()),g.value===!0?U():null]),l.after!==void 0?r("div",{class:"q-field__after q-field__marginal row no-wrap items-center",onClick:wt},l.after()):null])}}var hv=te({name:"QField",inheritAttrs:!1,props:{...Hl,tag:{type:String,default:"label"}},emits:Nl,setup(){return jl(Ql({tagProp:!0}))}});function Gn(e,t,n,l){const a=[];return e.forEach(o=>{l(o)===!0?a.push(o):t.push({failedPropValidation:n,file:o})}),a}function tl(e){e&&e.dataTransfer&&(e.dataTransfer.dropEffect="copy"),Fe(e)}var xs={multiple:Boolean,accept:String,capture:String,maxFileSize:[Number,String],maxTotalSize:[Number,String],maxFiles:[Number,String],filter:Function},Cs=["rejected"];function ks({editable:e,dnd:t,getFileInput:n,addFilesToQueue:l}){const{props:a,emit:o,proxy:i}=ve(),u=O(null),d=s(()=>a.accept!==void 0?a.accept.split(",").map(w=>(w=w.trim(),w==="*"?"*/":(w.endsWith("/*")&&(w=w.slice(0,w.length-1)),w.toUpperCase()))):null),f=s(()=>parseInt(a.maxFiles,10)),c=s(()=>parseInt(a.maxTotalSize,10));function h(w){if(e.value)if(w!==Object(w)&&(w={target:null}),w.target!==null&&w.target.matches('input[type="file"]')===!0)w.clientX===0&&w.clientY===0&&dt(w);else{const y=n();y&&y!==w.target&&y.click(w)}}function m(w){e.value&&w&&l(null,w)}function v(w,y,b,S){let B=Array.from(y||w.target.files);const L=[],R=()=>{L.length!==0&&o("rejected",L)};if(a.accept!==void 0&&d.value.indexOf("*/")===-1&&(B=Gn(B,L,"accept",A=>d.value.some(P=>A.type.toUpperCase().startsWith(P)||A.name.toUpperCase().endsWith(P))),B.length===0))return R();if(a.maxFileSize!==void 0){const A=parseInt(a.maxFileSize,10);if(B=Gn(B,L,"max-file-size",P=>P.size<=A),B.length===0)return R()}if(a.multiple!==!0&&B.length!==0&&(B=[B[0]]),B.forEach(A=>{A.__key=A.webkitRelativePath+A.lastModified+A.name+A.size}),S===!0){const A=b.map(P=>P.__key);B=Gn(B,L,"duplicate",P=>A.includes(P.__key)===!1)}if(B.length===0)return R();if(a.maxTotalSize!==void 0){let A=S===!0?b.reduce((P,z)=>P+z.size,0):0;if(B=Gn(B,L,"max-total-size",P=>(A+=P.size,A<=c.value)),B.length===0)return R()}if(typeof a.filter=="function"){const A=a.filter(B);B=Gn(B,L,"filter",P=>A.includes(P))}if(a.maxFiles!==void 0){let A=S===!0?b.length:0;if(B=Gn(B,L,"max-files",()=>(A++,A<=f.value)),B.length===0)return R()}if(R(),B.length!==0)return B}function g(w){tl(w),t.value!==!0&&(t.value=!0)}function _(w){Fe(w),(w.relatedTarget!==null||Ae.is.safari!==!0?w.relatedTarget!==u.value:document.elementsFromPoint(w.clientX,w.clientY).includes(u.value)===!1)===!0&&(t.value=!1)}function p(w){tl(w);const y=w.dataTransfer.files;y.length!==0&&l(null,y),t.value=!1}function k(w){if(t.value===!0)return r("div",{ref:u,class:`q-${w}__dnd absolute-full`,onDragenter:tl,onDragover:tl,onDragleave:_,onDrop:p})}return Object.assign(i,{pickFiles:h,addFiles:m}),{pickFiles:h,addFiles:m,onDragover:g,onDragleave:_,processFiles:v,getDndNode:k,maxFilesNumber:f,maxTotalSizeNumber:c}}function qs(e,t){function n(){const l=e.modelValue;try{const a="DataTransfer"in window?new DataTransfer:"ClipboardEvent"in window?new ClipboardEvent("").clipboardData:void 0;return Object(l)===l&&("length"in l?Array.from(l):[l]).forEach(o=>{a.items.add(o)}),{files:a.files}}catch{return{files:void 0}}}return t===!0?s(()=>{if(e.type==="file")return n()}):s(n)}var mh=te({name:"QFile",inheritAttrs:!1,props:{...Ss,...Ht,...xs,modelValue:[File,FileList,Array],append:Boolean,useChips:Boolean,displayValue:[String,Number],tabindex:{type:[String,Number],default:0},counterLabel:Function,inputClass:[Array,String,Object],inputStyle:[Array,String,Object]},emits:[...Nl,...Cs],setup(e,{slots:t,emit:n,attrs:l}){const{proxy:a}=ve(),o=Ql(),i=O(null),u=O(!1),d=Jo(e),{pickFiles:f,onDragover:c,onDragleave:h,processFiles:m,getDndNode:v}=ks({editable:o.editable,dnd:u,getFileInput:T,addFilesToQueue:x}),g=qs(e),_=s(()=>Object(e.modelValue)===e.modelValue?"length"in e.modelValue?Array.from(e.modelValue):[e.modelValue]:[]),p=s(()=>In(_.value)),k=s(()=>_.value.map(E=>E.name).join(", ")),w=s(()=>hl(_.value.reduce((E,V)=>E+V.size,0))),y=s(()=>({totalSize:w.value,filesNumber:_.value.length,maxFiles:e.maxFiles})),b=s(()=>({tabindex:-1,type:"file",title:"",accept:e.accept,capture:e.capture,name:d.value,...l,id:o.targetUid.value,disabled:o.editable.value!==!0})),S=s(()=>"q-file q-field--auto-height"+(u.value===!0?" q-file--dnd":"")),B=s(()=>e.multiple===!0&&e.append===!0);function L(E){const V=_.value.slice();V.splice(E,1),A(V)}function R(E){const V=_.value.indexOf(E);V!==-1&&L(V)}function A(E){n("update:modelValue",e.multiple===!0?E:E[0])}function P(E){E.keyCode===13&&wt(E)}function z(E){(E.keyCode===13||E.keyCode===32)&&f(E)}function T(){return i.value}function x(E,V){const j=m(E,V,_.value,B.value),ue=T();ue!=null&&(ue.value=""),j!==void 0&&((e.multiple===!0?e.modelValue&&j.every(I=>_.value.includes(I)):e.modelValue===j[0])||A(B.value===!0?_.value.concat(j):j))}function q(){return[r("input",{class:[e.inputClass,"q-file__filler"],style:e.inputStyle})]}function U(){if(t.file!==void 0)return _.value.length===0?q():_.value.map((V,j)=>t.file({index:j,file:V,ref:this}));if(t.selected!==void 0)return _.value.length===0?q():t.selected({files:_.value,ref:this});if(e.useChips===!0)return _.value.length===0?q():_.value.map((V,j)=>r(ju,{key:"file-"+j,removable:o.editable.value,dense:!0,textColor:e.color,tabindex:e.tabindex,onRemove:()=>{L(j)}},()=>r("span",{class:"ellipsis",textContent:V.name})));const E=e.displayValue!==void 0?e.displayValue:k.value;return E.length!==0?[r("div",{class:e.inputClass,style:e.inputStyle,textContent:E})]:q()}function W(){const E={ref:i,...b.value,...g.value,class:"q-field__input fit absolute-full cursor-pointer",onChange:x};return e.multiple===!0&&(E.multiple=!0),r("input",E)}return Object.assign(o,{fieldClass:S,emitValue:A,hasValue:p,inputRef:i,innerValue:_,floatingLabel:s(()=>p.value===!0||In(e.displayValue)),computedCounter:s(()=>{if(e.counterLabel!==void 0)return e.counterLabel(y.value);const E=e.maxFiles;return`${_.value.length}${E!==void 0?" / "+E:""} (${w.value})`}),getControlChild:()=>v("file"),getControl:()=>{const E={ref:o.targetRef,class:"q-field__native row items-center cursor-pointer",tabindex:e.tabindex};return o.editable.value===!0&&Object.assign(E,{onDragover:c,onDragleave:h,onKeydown:P,onKeyup:z}),r("div",E,[W()].concat(U()))}}),Object.assign(a,{removeAtIndex:L,removeFile:R,getNativeElement:()=>i.value}),St(a,"nativeEl",()=>i.value),jl(o)}}),gh=te({name:"QFooter",props:{modelValue:{type:Boolean,default:!0},reveal:Boolean,bordered:Boolean,elevated:Boolean,heightHint:{type:[String,Number],default:50}},emits:["reveal","focusin"],setup(e,{slots:t,emit:n}){const{proxy:{$q:l}}=ve(),a=Et(Nn,Je);if(a===Je)return console.error("QFooter needs to be child of QLayout"),Je;const o=O(parseInt(e.heightHint,10)),i=O(!0),u=O(Pt.value===!0||a.isContainer.value===!0?0:window.innerHeight),d=s(()=>e.reveal===!0||a.view.value.indexOf("F")!==-1||l.platform.is.ios&&a.isContainer.value===!0),f=s(()=>a.isContainer.value===!0?a.containerHeight.value:u.value),c=s(()=>{if(e.modelValue!==!0)return 0;if(d.value===!0)return i.value===!0?o.value:0;const S=a.scroll.value.position+f.value+o.value-a.height.value;return S>0?S:0}),h=s(()=>e.modelValue!==!0||d.value===!0&&i.value!==!0),m=s(()=>e.modelValue===!0&&h.value===!0&&e.reveal===!0),v=s(()=>"q-footer q-layout__section--marginal "+(d.value===!0?"fixed":"absolute")+"-bottom"+(e.bordered===!0?" q-footer--bordered":"")+(h.value===!0?" q-footer--hidden":"")+(e.modelValue!==!0?" q-layout--prevent-focus"+(d.value!==!0?" hidden":""):"")),g=s(()=>{const S=a.rows.value.bottom,B={};return S[0]==="l"&&a.left.space===!0&&(B[l.lang.rtl===!0?"right":"left"]=`${a.left.size}px`),S[2]==="r"&&a.right.space===!0&&(B[l.lang.rtl===!0?"left":"right"]=`${a.right.size}px`),B});function _(S,B){a.update("footer",S,B)}function p(S,B){S.value!==B&&(S.value=B)}function k({height:S}){p(o,S),_("size",S)}function w(){if(e.reveal!==!0)return;const{direction:S,position:B,inflectionPoint:L}=a.scroll.value;p(i,S==="up"||B-L<100||a.height.value-f.value-B-o.value<300)}function y(S){m.value===!0&&p(i,!0),n("focusin",S)}ne(()=>e.modelValue,S=>{_("space",S),p(i,!0),a.animate()}),ne(c,S=>{_("offset",S)}),ne(()=>e.reveal,S=>{S===!1&&p(i,e.modelValue)}),ne(i,S=>{a.animate(),n("reveal",S)}),ne([o,a.scroll,a.height],w),ne(()=>l.screen.height,S=>{a.isContainer.value!==!0&&p(u,S)});const b={};return a.instances.footer=b,e.modelValue===!0&&_("size",o.value),_("space",e.modelValue),_("offset",c.value),Ne(()=>{a.instances.footer===b&&(a.instances.footer=void 0,_("size",0),_("offset",0),_("space",!1))}),()=>{const S=mt(t.default,[r(Vn,{debounce:0,onResize:k})]);return e.elevated===!0&&S.push(r("div",{class:"q-layout__shadow absolute-full overflow-hidden no-pointer-events"})),r("footer",{class:v.value,style:g.value,onFocusin:y},S)}}}),hh=te({name:"QForm",props:{autofocus:Boolean,noErrorFocus:Boolean,noResetFocus:Boolean,greedy:Boolean,onSubmit:Function},emits:["reset","validationSuccess","validationError"],setup(e,{slots:t,emit:n}){const l=ve(),a=O(null);let o=0;const i=[];function u(v){const g=typeof v=="boolean"?v:e.noErrorFocus!==!0,_=++o,p=(y,b)=>{n(`validation${y===!0?"Success":"Error"}`,b)},k=y=>{const b=y.validate();return typeof b.then=="function"?b.then(S=>({valid:S,comp:y}),S=>({valid:!1,comp:y,err:S})):Promise.resolve({valid:b,comp:y})};return(e.greedy===!0?Promise.all(i.map(k)).then(y=>y.filter(b=>b.valid!==!0)):i.reduce((y,b)=>y.then(()=>k(b).then(S=>{if(S.valid===!1)return Promise.reject(S)})),Promise.resolve()).catch(y=>[y])).then(y=>{if(y===void 0||y.length===0)return _===o&&p(!0),!0;if(_===o){const{comp:b,err:S}=y[0];if(S!==void 0&&console.error(S),p(!1,b),g===!0){const B=y.find(({comp:L})=>typeof L.focus=="function"&&on(L.$)===!1);B!==void 0&&B.comp.focus()}}return!1})}function d(){o++,i.forEach(v=>{typeof v.resetValidation=="function"&&v.resetValidation()})}function f(v){v!==void 0&&Fe(v);const g=o+1;u().then(_=>{g===o&&_===!0&&(e.onSubmit!==void 0?n("submit",v):v!==void 0&&v.target!==void 0&&typeof v.target.submit=="function"&&v.target.submit())})}function c(v){v!==void 0&&Fe(v),n("reset"),Qe(()=>{d(),e.autofocus===!0&&e.noResetFocus!==!0&&h()})}function h(){ha(()=>{if(a.value===null)return;const v=a.value.querySelector("[autofocus][tabindex], [data-autofocus][tabindex]")||a.value.querySelector("[autofocus] [tabindex], [data-autofocus] [tabindex]")||a.value.querySelector("[autofocus], [data-autofocus]")||Array.prototype.find.call(a.value.querySelectorAll("[tabindex]"),g=>g.tabIndex!==-1);v!=null&&v.focus({preventScroll:!0})})}yn(na,{bindComponent(v){i.push(v)},unbindComponent(v){const g=i.indexOf(v);g!==-1&&i.splice(g,1)}});let m=!1;return Yt(()=>{m=!0}),bn(()=>{m===!0&&e.autofocus===!0&&h()}),ft(()=>{e.autofocus===!0&&h()}),Object.assign(l.proxy,{validate:u,resetValidation:d,submit:f,reset:c,focus:h,getValidationComponents:()=>i}),()=>r("form",{class:"q-form",ref:a,onSubmit:f,onReset:c},Ce(t.default))}}),bh={inject:{[na]:{default:it}},watch:{disable(e){const t=this.$.provides[na];t!==void 0&&(e===!0?(this.resetValidation(),t.unbindComponent(this)):t.bindComponent(this))}},methods:{validate(){},resetValidation(){}},mounted(){const e=this.$.provides[na];e!==void 0&&this.disable!==!0&&e.bindComponent(this)},beforeUnmount(){const e=this.$.provides[na];e!==void 0&&this.disable!==!0&&e.unbindComponent(this)}},yh=te({name:"QHeader",props:{modelValue:{type:Boolean,default:!0},reveal:Boolean,revealOffset:{type:Number,default:250},bordered:Boolean,elevated:Boolean,heightHint:{type:[String,Number],default:50}},emits:["reveal","focusin"],setup(e,{slots:t,emit:n}){const{proxy:{$q:l}}=ve(),a=Et(Nn,Je);if(a===Je)return console.error("QHeader needs to be child of QLayout"),Je;const o=O(parseInt(e.heightHint,10)),i=O(!0),u=s(()=>e.reveal===!0||a.view.value.indexOf("H")!==-1||l.platform.is.ios&&a.isContainer.value===!0),d=s(()=>{if(e.modelValue!==!0)return 0;if(u.value===!0)return i.value===!0?o.value:0;const w=o.value-a.scroll.value.position;return w>0?w:0}),f=s(()=>e.modelValue!==!0||u.value===!0&&i.value!==!0),c=s(()=>e.modelValue===!0&&f.value===!0&&e.reveal===!0),h=s(()=>"q-header q-layout__section--marginal "+(u.value===!0?"fixed":"absolute")+"-top"+(e.bordered===!0?" q-header--bordered":"")+(f.value===!0?" q-header--hidden":"")+(e.modelValue!==!0?" q-layout--prevent-focus":"")),m=s(()=>{const w=a.rows.value.top,y={};return w[0]==="l"&&a.left.space===!0&&(y[l.lang.rtl===!0?"right":"left"]=`${a.left.size}px`),w[2]==="r"&&a.right.space===!0&&(y[l.lang.rtl===!0?"left":"right"]=`${a.right.size}px`),y});function v(w,y){a.update("header",w,y)}function g(w,y){w.value!==y&&(w.value=y)}function _({height:w}){g(o,w),v("size",w)}function p(w){c.value===!0&&g(i,!0),n("focusin",w)}ne(()=>e.modelValue,w=>{v("space",w),g(i,!0),a.animate()}),ne(d,w=>{v("offset",w)}),ne(()=>e.reveal,w=>{w===!1&&g(i,e.modelValue)}),ne(i,w=>{a.animate(),n("reveal",w)}),ne(a.scroll,w=>{e.reveal===!0&&g(i,w.direction==="up"||w.position<=e.revealOffset||w.position-w.inflectionPoint<100)});const k={};return a.instances.header=k,e.modelValue===!0&&v("size",o.value),v("space",e.modelValue),v("offset",d.value),Ne(()=>{a.instances.header===k&&(a.instances.header=void 0,v("size",0),v("offset",0),v("space",!1))}),()=>{const w=Ha(t.default,[]);return e.elevated===!0&&w.push(r("div",{class:"q-layout__shadow absolute-full overflow-hidden no-pointer-events"})),w.push(r(Vn,{debounce:0,onResize:_})),r("header",{class:h.value,style:m.value,onFocusin:p},w)}}}),pr={ratio:[String,Number]};function wr(e,t){return s(()=>{const n=Number(e.ratio||(t!==void 0?t.value:void 0));return isNaN(n)!==!0&&n>0?{paddingBottom:`${100/n}%`}:null})}var bv=1.7778,ph=te({name:"QImg",props:{...pr,src:String,srcset:String,sizes:String,alt:String,crossorigin:String,decoding:String,referrerpolicy:String,draggable:Boolean,loading:{type:String,default:"lazy"},loadingShowDelay:{type:[Number,String],default:0},fetchpriority:{type:String,default:"auto"},width:String,height:String,initialRatio:{type:[Number,String],default:bv},placeholderSrc:String,errorSrc:String,fit:{type:String,default:"cover"},position:{type:String,default:"50% 50%"},imgClass:String,imgStyle:Object,noSpinner:Boolean,noNativeMenu:Boolean,noTransition:Boolean,spinnerColor:String,spinnerSize:String},emits:["load","error"],setup(e,{slots:t,emit:n}){const l=O(e.initialRatio),a=wr(e,l),o=ve(),{registerTimeout:i,removeTimeout:u}=hn(),{registerTimeout:d,removeTimeout:f}=hn(),c=s(()=>e.placeholderSrc!==void 0?{src:e.placeholderSrc}:null),h=s(()=>e.errorSrc!==void 0?{src:e.errorSrc,__qerror:!0}:null),m=[O(null),O(c.value)],v=O(0),g=O(!1),_=O(!1),p=s(()=>`q-img q-img--${e.noNativeMenu===!0?"no-":""}menu`),k=s(()=>({width:e.width,height:e.height})),w=s(()=>`q-img__image ${e.imgClass!==void 0?e.imgClass+" ":""}q-img__image--with${e.noTransition===!0?"out":""}-transition q-img__image--`),y=s(()=>({...e.imgStyle,objectFit:e.fit,objectPosition:e.position}));function b(){if(f(),e.loadingShowDelay===0){g.value=!0;return}d(()=>{g.value=!0},e.loadingShowDelay)}function S(){f(),g.value=!1}function B({target:T}){on(o)===!1&&(u(),l.value=T.naturalHeight===0?.5:T.naturalWidth/T.naturalHeight,L(T,1))}function L(T,x){x===1e3||on(o)===!0||(T.complete===!0?R(T):i(()=>{L(T,x+1)},50))}function R(T){on(o)!==!0&&(v.value=v.value^1,m[v.value].value=null,S(),T.getAttribute("__qerror")!=="true"&&(_.value=!1),n("load",T.currentSrc||T.src))}function A(T){u(),S(),_.value=!0,m[v.value].value=h.value,m[v.value^1].value=c.value,n("error",T)}function P(T){const x=m[T].value,q={key:"img_"+T,class:w.value,style:y.value,alt:e.alt,crossorigin:e.crossorigin,decoding:e.decoding,referrerpolicy:e.referrerpolicy,height:e.height,width:e.width,loading:e.loading,fetchpriority:e.fetchpriority,"aria-hidden":"true",draggable:e.draggable,...x};return v.value===T?Object.assign(q,{class:q.class+"current",onLoad:B,onError:A}):q.class+="loaded",r("div",{class:"q-img__container absolute-full",key:"img"+T},r("img",q))}function z(){return g.value===!1?r("div",{key:"content",class:"q-img__content absolute-full q-anchor--skip"},Ce(t[_.value===!0?"error":"default"])):r("div",{key:"loading",class:"q-img__loading absolute-full flex flex-center"},t.loading!==void 0?t.loading():e.noSpinner===!0?void 0:[r(It,{color:e.spinnerColor,size:e.spinnerSize})])}{let T=function(){ne(()=>e.src||e.srcset||e.sizes?{src:e.src,srcset:e.srcset,sizes:e.sizes}:null,x=>{u(),_.value=!1,x===null?(S(),m[v.value^1].value=c.value):b(),m[v.value].value=x},{immediate:!0})};Pt.value===!0?ft(T):T()}return()=>{const T=[];return a.value!==null&&T.push(r("div",{key:"filler",style:a.value})),m[0].value!==null&&T.push(P(0)),m[1].value!==null&&T.push(P(1)),T.push(r(_t,{name:"q-transition--fade"},z)),r("div",{key:"main",class:p.value,style:k.value,role:"img","aria-label":e.alt},T)}}}),{passive:kn}=et,wh=te({name:"QInfiniteScroll",props:{offset:{type:Number,default:500},debounce:{type:[String,Number],default:100},scrollTarget:Qn,initialIndex:{type:Number,default:0},disable:Boolean,reverse:Boolean},emits:["load"],setup(e,{slots:t,emit:n}){const l=O(!1),a=O(!0),o=O(null),i=O(null);let u=e.initialIndex,d,f;const c=s(()=>"q-infinite-scroll__loading"+(l.value===!0?"":" invisible"));function h(){if(e.disable===!0||l.value===!0||a.value===!1)return;const L=Ca(d),R=rn(d),A=Fn(d);e.reverse===!1?Math.round(R+A+e.offset)>=Math.round(L)&&m():Math.round(R)<=e.offset&&m()}function m(){if(e.disable===!0||l.value===!0||a.value===!1)return;u++,l.value=!0;const L=Ca(d);n("load",u,R=>{a.value===!0&&(l.value=!1,Qe(()=>{if(e.reverse===!0){const A=Ca(d),P=rn(d),z=A-L;Ln(d,P+z)}R===!0?_():o.value&&o.value.closest("body")&&f()}))})}function v(){u=0}function g(){a.value===!1&&(a.value=!0,d.addEventListener("scroll",f,kn)),h()}function _(){a.value===!0&&(a.value=!1,l.value=!1,d.removeEventListener("scroll",f,kn),f!==void 0&&f.cancel!==void 0&&f.cancel())}function p(){if(d&&a.value===!0&&d.removeEventListener("scroll",f,kn),d=Gt(o.value,e.scrollTarget),a.value===!0){if(d.addEventListener("scroll",f,kn),e.reverse===!0){const L=Ca(d),R=Fn(d);Ln(d,L-R)}h()}}function k(L){u=L}function w(L){L=parseInt(L,10);const R=f;f=L<=0?h:fa(h,isNaN(L)===!0?100:L),d&&a.value===!0&&(R!==void 0&&d.removeEventListener("scroll",R,kn),d.addEventListener("scroll",f,kn))}function y(L){if(b.value===!0){if(i.value===null){L!==!0&&Qe(()=>{y(!0)});return}const R=`${l.value===!0?"un":""}pauseAnimations`;Array.from(i.value.getElementsByTagName("svg")).forEach(A=>{A[R]()})}}const b=s(()=>e.disable!==!0&&a.value===!0);ne([l,b],()=>{y()}),ne(()=>e.disable,L=>{L===!0?_():g()}),ne(()=>e.reverse,()=>{l.value===!1&&a.value===!0&&h()}),ne(()=>e.scrollTarget,p),ne(()=>e.debounce,w);let S=!1;bn(()=>{S!==!1&&d&&Ln(d,S)}),Yt(()=>{S=d?rn(d):!1}),Ne(()=>{a.value===!0&&d.removeEventListener("scroll",f,kn)}),ft(()=>{w(e.debounce),p(),l.value===!1&&y()});const B=ve();return Object.assign(B.proxy,{poll:()=>{f!==void 0&&f()},trigger:m,stop:_,reset:v,resume:g,setIndex:k,updateScrollTarget:p}),()=>{const L=Ha(t.default,[]);return b.value===!0&&L[e.reverse===!1?"push":"unshift"](r("div",{ref:i,class:c.value},Ce(t.loading))),r("div",{class:"q-infinite-scroll",ref:o},L)}}}),_h=te({name:"QInnerLoading",props:{...Ke,...En,showing:Boolean,color:String,size:{type:[String,Number],default:"42px"},label:String,labelClass:String,labelStyle:[String,Array,Object]},setup(e,{slots:t}){const n=ve(),l=We(e,n.proxy.$q),{transitionProps:a,transitionStyle:o}=Rl(e),i=s(()=>"q-inner-loading q--avoid-card-border absolute-full column flex-center"+(l.value===!0?" q-inner-loading--dark":"")),u=s(()=>"q-inner-loading__label"+(e.labelClass!==void 0?` ${e.labelClass}`:""));function d(){const c=[r(It,{size:e.size,color:e.color})];return e.label!==void 0&&c.push(r("div",{class:u.value,style:e.labelStyle},[e.label])),c}function f(){return e.showing===!0?r("div",{class:i.value,style:o.value},t.default!==void 0?t.default():d()):null}return()=>r(_t,a.value,f)}}),gi={date:"####/##/##",datetime:"####/##/## ##:##",time:"##:##",fulltime:"##:##:##",phone:"(###) ### - ####",card:"#### #### #### ####"},$l={"#":{pattern:"[\\d]",negate:"[^\\d]"},S:{pattern:"[a-zA-Z]",negate:"[^a-zA-Z]"},N:{pattern:"[0-9a-zA-Z]",negate:"[^0-9a-zA-Z]"},A:{pattern:"[a-zA-Z]",negate:"[^a-zA-Z]",transform:e=>e.toLocaleUpperCase()},a:{pattern:"[a-zA-Z]",negate:"[^a-zA-Z]",transform:e=>e.toLocaleLowerCase()},X:{pattern:"[0-9a-zA-Z]",negate:"[^0-9a-zA-Z]",transform:e=>e.toLocaleUpperCase()},x:{pattern:"[0-9a-zA-Z]",negate:"[^0-9a-zA-Z]",transform:e=>e.toLocaleLowerCase()}},Ts=Object.keys($l);Ts.forEach(e=>{$l[e].regex=new RegExp($l[e].pattern)});var yv=new RegExp("\\\\([^.*+?^${}()|([\\]])|([.*+?^${}()|[\\]])|(["+Ts.join("")+"])|(.)","g"),hi=/[.*+?^${}()|[\]\\]/g,pt=String.fromCharCode(1),pv={mask:String,reverseFillMask:Boolean,fillMask:[Boolean,String],unmaskedValue:Boolean};function wv(e,t,n,l){let a,o,i,u,d,f;const c=O(null),h=O(v());function m(){return e.autogrow===!0||["textarea","text","search","url","tel","password"].includes(e.type)}ne(()=>e.type+e.autogrow,_),ne(()=>e.mask,A=>{if(A!==void 0)p(h.value,!0);else{const P=L(h.value);_(),e.modelValue!==P&&t("update:modelValue",P)}}),ne(()=>e.fillMask+e.reverseFillMask,()=>{c.value===!0&&p(h.value,!0)}),ne(()=>e.unmaskedValue,()=>{c.value===!0&&p(h.value)});function v(){if(_(),c.value===!0){const A=S(L(e.modelValue));return e.fillMask!==!1?R(A):A}return e.modelValue}function g(A){if(A<a.length)return a.slice(-A);let P="",z=a;const T=z.indexOf(pt);if(T!==-1){for(let x=A-z.length;x>0;x--)P+=pt;z=z.slice(0,T)+P+z.slice(T)}return z}function _(){if(c.value=e.mask!==void 0&&e.mask.length!==0&&m(),c.value===!1){u=void 0,a="",o="";return}const A=gi[e.mask]===void 0?e.mask:gi[e.mask],P=typeof e.fillMask=="string"&&e.fillMask.length!==0?e.fillMask.slice(0,1):"_",z=P.replace(hi,"\\$&"),T=[],x=[],q=[];let U=e.reverseFillMask===!0,W="",E="";A.replace(yv,(I,C,K,Q,oe)=>{if(Q!==void 0){const M=$l[Q];q.push(M),E=M.negate,U===!0&&(x.push("(?:"+E+"+)?("+M.pattern+"+)?(?:"+E+"+)?("+M.pattern+"+)?"),U=!1),x.push("(?:"+E+"+)?("+M.pattern+")?")}else if(K!==void 0)W="\\"+(K==="\\"?"":K),q.push(K),T.push("([^"+W+"]+)?"+W+"?");else{const M=C!==void 0?C:oe;W=M==="\\"?"\\\\\\\\":M.replace(hi,"\\\\$&"),q.push(M),T.push("([^"+W+"]+)?"+W+"?")}});const V=new RegExp("^"+T.join("")+"("+(W===""?".":"[^"+W+"]")+"+)?"+(W===""?"":"["+W+"]*")+"$"),j=x.length-1,ue=x.map((I,C)=>C===0&&e.reverseFillMask===!0?new RegExp("^"+z+"*"+I):C===j?new RegExp("^"+I+"("+(E===""?".":E)+"+)?"+(e.reverseFillMask===!0?"$":z+"*")):new RegExp("^"+I));i=q,u=I=>{const C=V.exec(e.reverseFillMask===!0?I:I.slice(0,q.length+1));C!==null&&(I=C.slice(1).join(""));const K=[],Q=ue.length;for(let oe=0,M=I;oe<Q;oe++){const D=ue[oe].exec(M);if(D===null)break;M=M.slice(D.shift().length),K.push(...D)}return K.length!==0?K.join(""):I},a=q.map(I=>typeof I=="string"?I:pt).join(""),o=a.split(pt).join(P)}function p(A,P,z){const T=l.value,x=T.selectionEnd,q=T.value.length-x,U=L(A);P===!0&&_();const W=S(U),E=e.fillMask!==!1?R(W):W,V=h.value!==E;T.value!==E&&(T.value=E),V===!0&&(h.value=E),document.activeElement===T&&Qe(()=>{if(E===o){const ue=e.reverseFillMask===!0?o.length:0;T.setSelectionRange(ue,ue,"forward");return}if(z==="insertFromPaste"&&e.reverseFillMask!==!0){const ue=T.selectionEnd;let I=x-1;for(let C=d;C<=I&&C<ue;C++)a[C]!==pt&&I++;w.right(T,I);return}if(["deleteContentBackward","deleteContentForward"].indexOf(z)!==-1){const ue=e.reverseFillMask===!0?x===0?E.length>W.length?1:0:Math.max(0,E.length-(E===o?0:Math.min(W.length,q)+1))+1:x;T.setSelectionRange(ue,ue,"forward");return}if(e.reverseFillMask===!0)if(V===!0){const ue=Math.max(0,E.length-(E===o?0:Math.min(W.length,q+1)));ue===1&&x===1?T.setSelectionRange(ue,ue,"forward"):w.rightReverse(T,ue)}else{const ue=E.length-q;T.setSelectionRange(ue,ue,"backward")}else if(V===!0){const ue=Math.max(0,a.indexOf(pt),Math.min(W.length,x)-1);w.right(T,ue)}else{const ue=x-1;w.right(T,ue)}});const j=e.unmaskedValue===!0?L(E):E;String(e.modelValue)!==j&&(e.modelValue!==null||j!=="")&&n(j,!0)}function k(A,P,z){const T=S(L(A.value));P=Math.max(0,a.indexOf(pt),Math.min(T.length,P)),d=P,A.setSelectionRange(P,z,"forward")}const w={left(A,P){const z=a.slice(P-1).indexOf(pt)===-1;let T=Math.max(0,P-1);for(;T>=0;T--)if(a[T]===pt){P=T,z===!0&&P++;break}if(T<0&&a[P]!==void 0&&a[P]!==pt)return w.right(A,0);P>=0&&A.setSelectionRange(P,P,"backward")},right(A,P){const z=A.value.length;let T=Math.min(z,P+1);for(;T<=z;T++)if(a[T]===pt){P=T;break}else a[T-1]===pt&&(P=T);if(T>z&&a[P-1]!==void 0&&a[P-1]!==pt)return w.left(A,z);A.setSelectionRange(P,P,"forward")},leftReverse(A,P){const z=g(A.value.length);let T=Math.max(0,P-1);for(;T>=0;T--)if(z[T-1]===pt){P=T;break}else if(z[T]===pt&&(P=T,T===0))break;if(T<0&&z[P]!==void 0&&z[P]!==pt)return w.rightReverse(A,0);P>=0&&A.setSelectionRange(P,P,"backward")},rightReverse(A,P){const z=A.value.length,T=g(z),x=T.slice(0,P+1).indexOf(pt)===-1;let q=Math.min(z,P+1);for(;q<=z;q++)if(T[q-1]===pt){P=q,P>0&&x===!0&&P--;break}if(q>z&&T[P-1]!==void 0&&T[P-1]!==pt)return w.leftReverse(A,z);A.setSelectionRange(P,P,"forward")}};function y(A){t("click",A),f=void 0}function b(A){if(t("keydown",A),Hn(A)===!0||A.altKey===!0)return;const P=l.value,z=P.selectionStart,T=P.selectionEnd;if(A.shiftKey||(f=void 0),A.keyCode===37||A.keyCode===39){A.shiftKey&&f===void 0&&(f=P.selectionDirection==="forward"?z:T);const x=w[(A.keyCode===39?"right":"left")+(e.reverseFillMask===!0?"Reverse":"")];if(A.preventDefault(),x(P,f===z?T:z),A.shiftKey){const q=P.selectionStart;P.setSelectionRange(Math.min(f,q),Math.max(f,q),"forward")}}else A.keyCode===8&&e.reverseFillMask!==!0&&z===T?(w.left(P,z),P.setSelectionRange(P.selectionStart,T,"backward")):A.keyCode===46&&e.reverseFillMask===!0&&z===T&&(w.rightReverse(P,T),P.setSelectionRange(z,P.selectionEnd,"forward"))}function S(A){if(A==null||A==="")return"";if(e.reverseFillMask===!0)return B(A);const P=i;let z=0,T="";for(let x=0;x<P.length;x++){const q=A[z],U=P[x];if(typeof U=="string")T+=U,q===U&&z++;else if(q!==void 0&&U.regex.test(q))T+=U.transform!==void 0?U.transform(q):q,z++;else return T}return T}function B(A){const P=i,z=a.indexOf(pt);let T=A.length-1,x="";for(let q=P.length-1;q>=0&&T!==-1;q--){const U=P[q];let W=A[T];if(typeof U=="string")x=U+x,W===U&&T--;else if(W!==void 0&&U.regex.test(W))do x=(U.transform!==void 0?U.transform(W):W)+x,T--,W=A[T];while(z===q&&W!==void 0&&U.regex.test(W));else return x}return x}function L(A){return typeof A!="string"||u===void 0?typeof A=="number"?u(""+A):A:u(A)}function R(A){return o.length-A.length<=0?A:e.reverseFillMask===!0&&A.length!==0?o.slice(0,-A.length)+A:A+o.slice(A.length)}return{innerValue:h,hasMask:c,moveCursorForPaste:k,updateMaskValue:p,onMaskedKeydown:b,onMaskedClick:y}}var _v=/[\u3000-\u303f\u3040-\u309f\u30a0-\u30ff\uff00-\uff9f\u4e00-\u9faf\u3400-\u4dbf]/,Sv=/[\u4e00-\u9fff\u3400-\u4dbf\u{20000}-\u{2a6df}\u{2a700}-\u{2b73f}\u{2b740}-\u{2b81f}\u{2b820}-\u{2ceaf}\uf900-\ufaff\u3300-\u33ff\ufe30-\ufe4f\uf900-\ufaff\u{2f800}-\u{2fa1f}]/u,xv=/[\u3131-\u314e\u314f-\u3163\uac00-\ud7a3]/,Cv=/[a-z0-9_ -]$/i;function Ms(e){return function(n){if(n.type==="compositionend"||n.type==="change"){if(n.target.qComposing!==!0)return;n.target.qComposing=!1,e(n)}else n.type==="compositionupdate"&&n.target.qComposing!==!0&&typeof n.data=="string"&&(Ae.is.firefox===!0?Cv.test(n.data)===!1:_v.test(n.data)===!0||Sv.test(n.data)===!0||xv.test(n.data)===!0)===!0&&(n.target.qComposing=!0)}}var $s=te({name:"QInput",inheritAttrs:!1,props:{...Hl,...pv,...Ht,modelValue:[String,Number,FileList],shadowText:String,type:{type:String,default:"text"},debounce:[String,Number],autogrow:Boolean,inputClass:[Array,String,Object],inputStyle:[Array,String,Object]},emits:[...Nl,"paste","change","keydown","click","animationend"],setup(e,{emit:t,attrs:n}){const{proxy:l}=ve(),{$q:a}=l,o={};let i=NaN,u,d,f=null,c;const h=O(null),m=Jo(e),{innerValue:v,hasMask:g,moveCursorForPaste:_,updateMaskValue:p,onMaskedKeydown:k,onMaskedClick:w}=wv(e,t,W,h),y=qs(e,!0),b=s(()=>In(v.value)),S=Ms(q),B=Ql({changeEvent:!0}),L=s(()=>e.type==="textarea"||e.autogrow===!0),R=s(()=>L.value===!0||["text","search","url","tel","password"].includes(e.type)),A=s(()=>{const C={...B.splitAttrs.listeners.value,onInput:q,onPaste:x,onChange:V,onBlur:j,onFocus:dt};return C.onCompositionstart=C.onCompositionupdate=C.onCompositionend=S,g.value===!0&&(C.onKeydown=k,C.onClick=w),e.autogrow===!0&&(C.onAnimationend=U),C}),P=s(()=>{const C={tabindex:0,"data-autofocus":e.autofocus===!0||void 0,rows:e.type==="textarea"?6:void 0,"aria-label":e.label,name:m.value,...B.splitAttrs.attributes.value,id:B.targetUid.value,maxlength:e.maxlength,disabled:e.disable===!0,readonly:e.readonly===!0};return L.value===!1&&(C.type=e.type),e.autogrow===!0&&(C.rows=1),C});ne(()=>e.type,()=>{h.value&&(h.value.value=e.modelValue)}),ne(()=>e.modelValue,C=>{if(g.value===!0){if(d===!0&&(d=!1,String(C)===i))return;p(C)}else v.value!==C&&(v.value=C,e.type==="number"&&o.hasOwnProperty("value")===!0&&(u===!0?u=!1:delete o.value));e.autogrow===!0&&Qe(E)}),ne(()=>e.autogrow,C=>{C===!0?Qe(E):h.value!==null&&n.rows>0&&(h.value.style.height="auto")}),ne(()=>e.dense,()=>{e.autogrow===!0&&Qe(E)});function z(){ha(()=>{const C=document.activeElement;h.value!==null&&h.value!==C&&(C===null||C.id!==B.targetUid.value)&&h.value.focus({preventScroll:!0})})}function T(){h.value!==null&&h.value.select()}function x(C){if(g.value===!0&&e.reverseFillMask!==!0){const K=C.target;_(K,K.selectionStart,K.selectionEnd)}t("paste",C)}function q(C){if(!C||!C.target)return;if(e.type==="file"){t("update:modelValue",C.target.files);return}const K=C.target.value;if(C.target.qComposing===!0){o.value=K;return}if(g.value===!0)p(K,!1,C.inputType);else if(W(K),R.value===!0&&C.target===document.activeElement){const{selectionStart:Q,selectionEnd:oe}=C.target;Q!==void 0&&oe!==void 0&&Qe(()=>{C.target===document.activeElement&&K.indexOf(C.target.value)===0&&C.target.setSelectionRange(Q,oe)})}e.autogrow===!0&&E()}function U(C){t("animationend",C),E()}function W(C,K){c=()=>{f=null,e.type!=="number"&&o.hasOwnProperty("value")===!0&&delete o.value,e.modelValue!==C&&i!==C&&(i=C,K===!0&&(d=!0),t("update:modelValue",C),Qe(()=>{i===C&&(i=NaN)})),c=void 0},e.type==="number"&&(u=!0,o.value=C),e.debounce!==void 0?(f!==null&&clearTimeout(f),o.value=C,f=setTimeout(c,e.debounce)):c()}function E(){requestAnimationFrame(()=>{const C=h.value;if(C!==null){const K=C.parentNode.style,{scrollTop:Q}=C,{overflowY:oe,maxHeight:M}=a.platform.is.firefox===!0?{}:window.getComputedStyle(C),D=oe!==void 0&&oe!=="scroll";D===!0&&(C.style.overflowY="hidden"),K.marginBottom=C.scrollHeight-1+"px",C.style.height="1px",C.style.height=C.scrollHeight+"px",D===!0&&(C.style.overflowY=parseInt(M,10)<C.scrollHeight?"auto":"hidden"),K.marginBottom="",C.scrollTop=Q}})}function V(C){S(C),f!==null&&(clearTimeout(f),f=null),c!==void 0&&c(),t("change",C.target.value)}function j(C){C!==void 0&&dt(C),f!==null&&(clearTimeout(f),f=null),c!==void 0&&c(),u=!1,d=!1,delete o.value,e.type!=="file"&&setTimeout(()=>{h.value!==null&&(h.value.value=v.value!==void 0?v.value:"")})}function ue(){return o.hasOwnProperty("value")===!0?o.value:v.value!==void 0?v.value:""}Ne(()=>{j()}),ft(()=>{e.autogrow===!0&&E()}),Object.assign(B,{innerValue:v,fieldClass:s(()=>`q-${L.value===!0?"textarea":"input"}`+(e.autogrow===!0?" q-textarea--autogrow":"")),hasShadow:s(()=>e.type!=="file"&&typeof e.shadowText=="string"&&e.shadowText.length!==0),inputRef:h,emitValue:W,hasValue:b,floatingLabel:s(()=>b.value===!0&&(e.type!=="number"||isNaN(v.value)===!1)||In(e.displayValue)),getControl:()=>r(L.value===!0?"textarea":"input",{ref:h,class:["q-field__native q-placeholder",e.inputClass],style:e.inputStyle,...P.value,...A.value,...e.type!=="file"?{value:ue()}:y.value}),getShadowControl:()=>r("div",{class:"q-field__native q-field__shadow absolute-bottom no-pointer-events"+(L.value===!0?"":" text-no-wrap")},[r("span",{class:"invisible"},ue()),r("span",e.shadowText)])});const I=jl(B);return Object.assign(l,{focus:z,select:T,getNativeElement:()=>h.value}),St(l,"nativeEl",()=>h.value),I}}),bi={threshold:0,root:null,rootMargin:"0px"};function yi(e,t,n){let l,a,o;typeof n=="function"?(l=n,a=bi,o=t.cfg===void 0):(l=n.handler,a=Object.assign({},bi,n.cfg),o=t.cfg===void 0||Rt(t.cfg,a)===!1),t.handler!==l&&(t.handler=l),o===!0&&(t.cfg=a,t.observer!==void 0&&t.observer.unobserve(e),t.observer=new IntersectionObserver(([i])=>{if(typeof t.handler=="function"){if(i.rootBounds===null&&document.body.contains(e)===!0){t.observer.unobserve(e),t.observer.observe(e);return}(t.handler(i,t.observer)===!1||t.once===!0&&i.isIntersecting===!0)&&Bs(e)}},a),t.observer.observe(e))}function Bs(e){const t=e.__qvisible;t!==void 0&&(t.observer!==void 0&&t.observer.unobserve(e),delete e.__qvisible)}var kv=Xt({name:"intersection",mounted(e,{modifiers:t,value:n}){const l={once:t.once===!0};yi(e,l,n),e.__qvisible=l},updated(e,t){const n=e.__qvisible;n!==void 0&&yi(e,n,t.value)},beforeUnmount:Bs}),Sh=te({name:"QIntersection",props:{tag:{type:String,default:"div"},once:Boolean,transition:String,transitionDuration:{type:[String,Number],default:300},ssrPrerender:Boolean,margin:String,threshold:[Number,Array],root:{default:null},disable:Boolean,onVisibility:Function},setup(e,{slots:t,emit:n}){const l=O(Pt.value===!0?e.ssrPrerender:!1),a=s(()=>e.root!==void 0||e.margin!==void 0||e.threshold!==void 0?{handler:d,cfg:{root:e.root,rootMargin:e.margin,threshold:e.threshold}}:d),o=s(()=>e.disable!==!0&&(Pt.value!==!0||e.once!==!0||e.ssrPrerender!==!0)),i=s(()=>[[kv,a.value,void 0,{once:e.once}]]),u=s(()=>`--q-transition-duration: ${e.transitionDuration}ms`);function d(c){l.value!==c.isIntersecting&&(l.value=c.isIntersecting,e.onVisibility!==void 0&&n("visibility",l.value))}function f(){if(l.value===!0)return[r("div",{key:"content",style:u.value},Ce(t.default))];if(t.hidden!==void 0)return[r("div",{key:"hidden",style:u.value},t.hidden())]}return()=>{const c=e.transition?[r(_t,{name:"q-transition--"+e.transition},f)]:f();return Dt(e.tag,{class:"q-intersection"},c,"main",o.value,()=>i.value)}}}),qv=te({name:"QList",props:{...Ke,bordered:Boolean,dense:Boolean,separator:Boolean,padding:Boolean,tag:{type:String,default:"div"}},setup(e,{slots:t}){const n=ve(),l=We(e,n.proxy.$q),a=s(()=>"q-list"+(e.bordered===!0?" q-list--bordered":"")+(e.dense===!0?" q-list--dense":"")+(e.separator===!0?" q-list--separator":"")+(l.value===!0?" q-list--dark":"")+(e.padding===!0?" q-list--padding":""));return()=>r(e.tag,{class:a.value},Ce(t.default))}}),pi=[34,37,40,33,39,38],Tv=Object.keys(ur),xh=te({name:"QKnob",props:{...Ht,...ur,modelValue:{type:Number,required:!0},innerMin:Number,innerMax:Number,step:{type:Number,default:1,validator:e=>e>=0},tabindex:{type:[Number,String],default:0},disable:Boolean,readonly:Boolean},emits:["update:modelValue","change","dragValue"],setup(e,{slots:t,emit:n}){const{proxy:l}=ve(),{$q:a}=l,o=O(e.modelValue),i=O(!1),u=s(()=>isNaN(e.innerMin)===!0||e.innerMin<e.min?e.min:e.innerMin),d=s(()=>isNaN(e.innerMax)===!0||e.innerMax>e.max?e.max:e.innerMax);let f;function c(){o.value=e.modelValue===null?u.value:tt(e.modelValue,u.value,d.value),z(!0)}ne(()=>`${e.modelValue}|${u.value}|${d.value}`,c),c();const h=s(()=>e.disable===!1&&e.readonly===!1),m=s(()=>"q-knob non-selectable"+(h.value===!0?" q-knob--editable":e.disable===!0?" disabled":"")),v=s(()=>(String(e.step).trim().split(".")[1]||"").length),g=s(()=>e.step===0?1:e.step),_=s(()=>e.instantFeedback===!0||i.value===!0),p=a.platform.is.mobile===!0?s(()=>h.value===!0?{onClick:L}:{}):s(()=>h.value===!0?{onMousedown:B,onClick:L,onKeydown:R,onKeyup:P}:{}),k=s(()=>h.value===!0?{tabindex:e.tabindex}:{[`aria-${e.disable===!0?"disabled":"readonly"}`]:"true"}),w=s(()=>{const q={};return Tv.forEach(U=>{q[U]=e[U]}),q});function y(q){q.isFinal?(A(q.evt,!0),i.value=!1):(q.isFirst&&(S(),i.value=!0),A(q.evt))}const b=s(()=>[[Ft,y,void 0,{prevent:!0,stop:!0,mouse:!0}]]);function S(){const{top:q,left:U,width:W,height:E}=l.$el.getBoundingClientRect();f={top:q+E/2,left:U+W/2}}function B(q){S(),A(q)}function L(q){S(),A(q,!0)}function R(q){if(!pi.includes(q.keyCode))return;Fe(q);const U=([34,33].includes(q.keyCode)?10:1)*g.value,W=[34,37,40].includes(q.keyCode)?-U:U;o.value=tt(parseFloat((o.value+W).toFixed(v.value)),u.value,d.value),z()}function A(q,U){const W=$t(q),E=Math.abs(W.top-f.top),V=Math.sqrt(E**2+Math.abs(W.left-f.left)**2);let j=Math.asin(E/V)*(180/Math.PI);W.top<f.top?j=f.left<W.left?90-j:270+j:j=f.left<W.left?j+90:270-j,a.lang.rtl===!0?j=Aa(-j-e.angle,0,360):e.angle&&(j=Aa(j-e.angle,0,360)),e.reverse===!0&&(j=360-j);let ue=e.min+j/360*(e.max-e.min);if(g.value!==0){const I=ue%g.value;ue=ue-I+(Math.abs(I)>=g.value/2?(I<0?-1:1)*g.value:0),ue=parseFloat(ue.toFixed(v.value))}ue=tt(ue,u.value,d.value),n("dragValue",ue),o.value!==ue&&(o.value=ue),z(U)}function P(q){pi.includes(q.keyCode)&&z(!0)}function z(q){e.modelValue!==o.value&&n("update:modelValue",o.value),q===!0&&n("change",o.value)}const T=Ka(e);function x(){return r("input",T.value)}return()=>{const q={class:m.value,role:"slider","aria-valuemin":u.value,"aria-valuemax":d.value,"aria-valuenow":e.modelValue,...k.value,...w.value,value:o.value,instantFeedback:_.value,...p.value},U={default:t.default};return h.value===!0&&e.name!==void 0&&(U.internal=x),Dt(Uu,q,U,"knob",h.value,()=>b.value)}}}),{passive:wi}=et,Mv=["both","horizontal","vertical"],Ps=te({name:"QScrollObserver",props:{axis:{type:String,validator:e=>Mv.includes(e),default:"vertical"},debounce:[String,Number],scrollTarget:Qn},emits:["scroll"],setup(e,{emit:t}){const n={position:{top:0,left:0},direction:"down",directionChanged:!1,delta:{top:0,left:0},inflectionPoint:{top:0,left:0}};let l=null,a,o;ne(()=>e.scrollTarget,()=>{d(),u()});function i(){l!==null&&l();const h=Math.max(0,rn(a)),m=ja(a),v={top:h-n.position.top,left:m-n.position.left};if(e.axis==="vertical"&&v.top===0||e.axis==="horizontal"&&v.left===0)return;const g=Math.abs(v.top)>=Math.abs(v.left)?v.top<0?"up":"down":v.left<0?"left":"right";n.position={top:h,left:m},n.directionChanged=n.direction!==g,n.delta=v,n.directionChanged===!0&&(n.direction=g,n.inflectionPoint=n.position),t("scroll",{...n})}function u(){a=Gt(o,e.scrollTarget),a.addEventListener("scroll",f,wi),f(!0)}function d(){a!==void 0&&(a.removeEventListener("scroll",f,wi),a=void 0)}function f(h){if(h===!0||e.debounce===0||e.debounce==="0")i();else if(l===null){const[m,v]=e.debounce?[setTimeout(i,e.debounce),clearTimeout]:[requestAnimationFrame(i),cancelAnimationFrame];l=()=>{v(m),l=null}}}const{proxy:c}=ve();return ne(()=>c.$q.lang.rtl,i),ft(()=>{o=c.$el.parentNode,u()}),Ne(()=>{l!==null&&l(),d()}),Object.assign(c,{trigger:f,getPosition:()=>n}),it}}),Ch=te({name:"QLayout",props:{container:Boolean,view:{type:String,default:"hhh lpr fff",validator:e=>/^(h|l)h(h|r) lpr (f|l)f(f|r)$/.test(e.toLowerCase())},onScroll:Function,onScrollHeight:Function,onResize:Function},setup(e,{slots:t,emit:n}){const{proxy:{$q:l}}=ve(),a=O(null),o=O(l.screen.height),i=O(e.container===!0?0:l.screen.width),u=O({position:0,direction:"down",inflectionPoint:0}),d=O(0),f=O(Pt.value===!0?0:Ma()),c=s(()=>"q-layout q-layout--"+(e.container===!0?"containerized":"standard")),h=s(()=>e.container===!1?{minHeight:l.screen.height+"px"}:null),m=s(()=>f.value!==0?{[l.lang.rtl===!0?"left":"right"]:`${f.value}px`}:null),v=s(()=>f.value!==0?{[l.lang.rtl===!0?"right":"left"]:0,[l.lang.rtl===!0?"left":"right"]:`-${f.value}px`,width:`calc(100% + ${f.value}px)`}:null);function g(b){if(e.container===!0||document.qScrollPrevented!==!0){const S={position:b.position.top,direction:b.direction,directionChanged:b.directionChanged,inflectionPoint:b.inflectionPoint.top,delta:b.delta.top};u.value=S,e.onScroll!==void 0&&n("scroll",S)}}function _(b){const{height:S,width:B}=b;let L=!1;o.value!==S&&(L=!0,o.value=S,e.onScrollHeight!==void 0&&n("scrollHeight",S),k()),i.value!==B&&(L=!0,i.value=B),L===!0&&e.onResize!==void 0&&n("resize",b)}function p({height:b}){d.value!==b&&(d.value=b,k())}function k(){if(e.container===!0){const b=o.value>d.value?Ma():0;f.value!==b&&(f.value=b)}}let w=null;const y={instances:{},view:s(()=>e.view),isContainer:s(()=>e.container),rootRef:a,height:o,containerHeight:d,scrollbarWidth:f,totalWidth:s(()=>i.value+f.value),rows:s(()=>{const b=e.view.toLowerCase().split(" ");return{top:b[0].split(""),middle:b[1].split(""),bottom:b[2].split("")}}),header:ta({size:0,offset:0,space:!1}),right:ta({size:300,offset:0,space:!1}),footer:ta({size:0,offset:0,space:!1}),left:ta({size:300,offset:0,space:!1}),scroll:u,animate(){w!==null?clearTimeout(w):document.body.classList.add("q-body--layout-animate"),w=setTimeout(()=>{w=null,document.body.classList.remove("q-body--layout-animate")},155)},update(b,S,B){y[b][S]=B}};if(yn(Nn,y),Ma()>0){let b=function(){L=null,R.classList.remove("hide-scrollbar")},S=function(){if(L===null){if(R.scrollHeight>l.screen.height)return;R.classList.add("hide-scrollbar")}else clearTimeout(L);L=setTimeout(b,300)},B=function(A){L!==null&&A==="remove"&&(clearTimeout(L),b()),window[`${A}EventListener`]("resize",S)},L=null;const R=document.body;ne(()=>e.container!==!0?"add":"remove",B),e.container!==!0&&B("add"),zo(()=>{B("remove")})}return()=>{const b=mt(t.default,[r(Ps,{onScroll:g}),r(Vn,{onResize:_})]),S=r("div",{class:c.value,style:h.value,ref:e.container===!0?void 0:a,tabindex:-1},b);return e.container===!0?r("div",{class:"q-layout-container overflow-hidden",ref:a},[r(Vn,{onResize:p}),r("div",{class:"absolute-full",style:m.value},[r("div",{class:"scroll",style:v.value},[S])])]):S}}}),$v=["horizontal","vertical","cell","none"],Bv=te({name:"QMarkupTable",props:{...Ke,dense:Boolean,flat:Boolean,bordered:Boolean,square:Boolean,wrapCells:Boolean,separator:{type:String,default:"horizontal",validator:e=>$v.includes(e)}},setup(e,{slots:t}){const n=ve(),l=We(e,n.proxy.$q),a=s(()=>`q-markup-table q-table__container q-table__card q-table--${e.separator}-separator`+(l.value===!0?" q-table--dark q-table__card--dark q-dark":"")+(e.dense===!0?" q-table--dense":"")+(e.flat===!0?" q-table--flat":"")+(e.bordered===!0?" q-table--bordered":"")+(e.square===!0?" q-table--square":"")+(e.wrapCells===!1?" q-table--no-wrap":""));return()=>r("div",{class:a.value},[r("table",{class:"q-table"},Ce(t.default))])}}),kh=te({name:"QNoSsr",props:{tag:{type:String,default:"div"},placeholder:String},setup(e,{slots:t}){const{isHydrated:n}=Zu();return()=>{if(n.value===!0){const o=Ce(t.default);return o===void 0?o:o.length>1?r(e.tag,{},o):o[0]}const l={class:"q-no-ssr-placeholder"},a=Ce(t.placeholder);if(a!==void 0)return a.length>1?r(e.tag,l,a):a[0];if(e.placeholder!==void 0)return r(e.tag,l,e.placeholder)}}}),Pv=r("svg",{key:"svg",class:"q-radio__bg absolute non-selectable",viewBox:"0 0 24 24"},[r("path",{d:"M12,22a10,10 0 0 1 -10,-10a10,10 0 0 1 10,-10a10,10 0 0 1 10,10a10,10 0 0 1 -10,10m0,-22a12,12 0 0 0 -12,12a12,12 0 0 0 12,12a12,12 0 0 0 12,-12a12,12 0 0 0 -12,-12"}),r("path",{class:"q-radio__check",d:"M12,6a6,6 0 0 0 -6,6a6,6 0 0 0 6,6a6,6 0 0 0 6,-6a6,6 0 0 0 -6,-6"})]),Ev=te({name:"QRadio",props:{...Ke,...sn,...Ht,modelValue:{required:!0},val:{required:!0},label:String,leftLabel:Boolean,checkedIcon:String,uncheckedIcon:String,color:String,keepColor:Boolean,dense:Boolean,disable:Boolean,tabindex:[String,Number]},emits:["update:modelValue"],setup(e,{slots:t,emit:n}){const{proxy:l}=ve(),a=We(e,l.$q),o=cn(e,Iu),i=O(null),{refocusTargetEl:u,refocusTarget:d}=Du(e,i),f=s(()=>an(e.modelValue)===an(e.val)),c=s(()=>"q-radio cursor-pointer no-outline row inline no-wrap items-center"+(e.disable===!0?" disabled":"")+(a.value===!0?" q-radio--dark":"")+(e.dense===!0?" q-radio--dense":"")+(e.leftLabel===!0?" reverse":"")),h=s(()=>{const y=e.color!==void 0&&(e.keepColor===!0||f.value===!0)?` text-${e.color}`:"";return`q-radio__inner relative-position q-radio__inner--${f.value===!0?"truthy":"falsy"}${y}`}),m=s(()=>(f.value===!0?e.checkedIcon:e.uncheckedIcon)||null),v=s(()=>e.disable===!0?-1:e.tabindex||0),g=s(()=>{const y={type:"radio"};return e.name!==void 0&&Object.assign(y,{".checked":f.value===!0,"^checked":f.value===!0?"checked":void 0,name:e.name,value:e.val}),y}),_=wn(g);function p(y){y!==void 0&&(Fe(y),d(y)),e.disable!==!0&&f.value!==!0&&n("update:modelValue",e.val,y)}function k(y){(y.keyCode===13||y.keyCode===32)&&Fe(y)}function w(y){(y.keyCode===13||y.keyCode===32)&&p(y)}return Object.assign(l,{set:p}),()=>{const y=m.value!==null?[r("div",{key:"icon",class:"q-radio__icon-container absolute-full flex flex-center no-wrap"},[r(je,{class:"q-radio__icon",name:m.value})])]:[Pv];e.disable!==!0&&_(y,"unshift"," q-radio__native q-ma-none q-pa-none");const b=[r("div",{class:h.value,style:o.value,"aria-hidden":"true"},y)];u.value!==null&&b.push(u.value);const S=e.label!==void 0?mt(t.default,[e.label]):Ce(t.default);return S!==void 0&&b.push(r("div",{class:"q-radio__label q-anchor--skip"},S)),r("div",{ref:i,class:c.value,tabindex:v.value,role:"radio","aria-label":e.label,"aria-checked":f.value===!0?"true":"false","aria-disabled":e.disable===!0?"true":void 0,onClick:p,onKeydown:k,onKeyup:w},b)}}}),Lv=te({name:"QToggle",props:{...Hu,icon:String,iconColor:String},emits:Nu,setup(e){function t(n,l){const a=s(()=>(n.value===!0?e.checkedIcon:l.value===!0?e.indeterminateIcon:e.uncheckedIcon)||e.icon),o=s(()=>n.value===!0?e.iconColor:null);return()=>[r("div",{class:"q-toggle__track"}),r("div",{class:"q-toggle__thumb absolute flex flex-center no-wrap"},a.value!==void 0?[r(je,{name:a.value,color:o.value})]:void 0)]}return Qu("toggle",t)}}),Es={radio:Ev,checkbox:$a,toggle:Lv},Av=Object.keys(Es),zv=te({name:"QOptionGroup",props:{...Ke,modelValue:{required:!0},options:{type:Array,validator:e=>e.every(t=>"value"in t&&"label"in t)},name:String,type:{type:String,default:"radio",validator:e=>Av.includes(e)},color:String,keepColor:Boolean,dense:Boolean,size:String,leftLabel:Boolean,inline:Boolean,disable:Boolean},emits:["update:modelValue"],setup(e,{emit:t,slots:n}){const{proxy:{$q:l}}=ve(),a=Array.isArray(e.modelValue);e.type==="radio"?a===!0&&console.error("q-option-group: model should not be array"):a===!1&&console.error("q-option-group: model should be array in your case");const o=We(e,l),i=s(()=>Es[e.type]),u=s(()=>"q-option-group q-gutter-x-sm"+(e.inline===!0?" q-option-group--inline":"")),d=s(()=>{const c={role:"group"};return e.type==="radio"&&(c.role="radiogroup",e.disable===!0&&(c["aria-disabled"]="true")),c});function f(c){t("update:modelValue",c)}return()=>r("div",{class:u.value,...d.value},e.options.map((c,h)=>{const m=n["label-"+h]!==void 0?()=>n["label-"+h](c):n.label!==void 0?()=>n.label(c):void 0;return r("div",[r(i.value,{modelValue:e.modelValue,val:c.value,name:c.name===void 0?e.name:c.name,disable:e.disable||c.disable,label:m===void 0?c.label:null,leftLabel:c.leftLabel===void 0?e.leftLabel:c.leftLabel,color:c.color===void 0?e.color:c.color,checkedIcon:c.checkedIcon,uncheckedIcon:c.uncheckedIcon,dark:c.dark||o.value,size:c.size===void 0?e.size:c.size,dense:e.dense,keepColor:c.keepColor===void 0?e.keepColor:c.keepColor,"onUpdate:modelValue":f},m)])}))}}),qh=te({name:"QPage",props:{padding:Boolean,styleFn:Function},setup(e,{slots:t}){const{proxy:{$q:n}}=ve(),l=Et(Nn,Je);if(l===Je)return console.error("QPage needs to be a deep child of QLayout"),Je;if(Et(su,Je)===Je)return console.error("QPage needs to be child of QPageContainer"),Je;const o=s(()=>{const u=(l.header.space===!0?l.header.size:0)+(l.footer.space===!0?l.footer.size:0);if(typeof e.styleFn=="function"){const d=l.isContainer.value===!0?l.containerHeight.value:n.screen.height;return e.styleFn(u,d)}return{minHeight:l.isContainer.value===!0?l.containerHeight.value-u+"px":n.screen.height===0?u!==0?`calc(100vh - ${u}px)`:"100vh":n.screen.height-u+"px"}}),i=s(()=>`q-page${e.padding===!0?" q-layout-padding":""}`);return()=>r("main",{class:i.value,style:o.value},Ce(t.default))}}),Th=te({name:"QPageContainer",setup(e,{slots:t}){const{proxy:{$q:n}}=ve(),l=Et(Nn,Je);if(l===Je)return console.error("QPageContainer needs to be child of QLayout"),Je;yn(su,!0);const a=s(()=>{const o={};return l.header.space===!0&&(o.paddingTop=`${l.header.size}px`),l.right.space===!0&&(o[`padding${n.lang.rtl===!0?"Left":"Right"}`]=`${l.right.size}px`),l.footer.space===!0&&(o.paddingBottom=`${l.footer.size}px`),l.left.space===!0&&(o[`padding${n.lang.rtl===!0?"Right":"Left"}`]=`${l.left.size}px`),o});return()=>r("div",{class:"q-page-container",style:a.value},Ce(t.default))}}),Po={position:{type:String,default:"bottom-right",validator:e=>["top-right","top-left","bottom-right","bottom-left","top","right","bottom","left"].includes(e)},offset:{type:Array,validator:e=>e.length===2},expand:Boolean};function Ls(){const{props:e,proxy:{$q:t}}=ve(),n=Et(Nn,Je);if(n===Je)return console.error("QPageSticky needs to be child of QLayout"),Je;const l=s(()=>{const h=e.position;return{top:h.indexOf("top")!==-1,right:h.indexOf("right")!==-1,bottom:h.indexOf("bottom")!==-1,left:h.indexOf("left")!==-1,vertical:h==="top"||h==="bottom",horizontal:h==="left"||h==="right"}}),a=s(()=>n.header.offset),o=s(()=>n.right.offset),i=s(()=>n.footer.offset),u=s(()=>n.left.offset),d=s(()=>{let h=0,m=0;const v=l.value,g=t.lang.rtl===!0?-1:1;v.top===!0&&a.value!==0?m=`${a.value}px`:v.bottom===!0&&i.value!==0&&(m=`${-i.value}px`),v.left===!0&&u.value!==0?h=`${g*u.value}px`:v.right===!0&&o.value!==0&&(h=`${-g*o.value}px`);const _={transform:`translate(${h}, ${m})`};return e.offset&&(_.margin=`${e.offset[1]}px ${e.offset[0]}px`),v.vertical===!0?(u.value!==0&&(_[t.lang.rtl===!0?"right":"left"]=`${u.value}px`),o.value!==0&&(_[t.lang.rtl===!0?"left":"right"]=`${o.value}px`)):v.horizontal===!0&&(a.value!==0&&(_.top=`${a.value}px`),i.value!==0&&(_.bottom=`${i.value}px`)),_}),f=s(()=>`q-page-sticky row flex-center fixed-${e.position} q-page-sticky--${e.expand===!0?"expand":"shrink"}`);function c(h){const m=Ce(h.default);return r("div",{class:f.value,style:d.value},e.expand===!0?m:[r("div",m)])}return{$layout:n,getStickyContent:c}}var Mh=te({name:"QPageScroller",props:{...Po,scrollOffset:{type:Number,default:1e3},reverse:Boolean,duration:{type:Number,default:300},offset:{...Po.offset,default:()=>[18,18]}},emits:["click"],setup(e,{slots:t,emit:n}){const{proxy:{$q:l}}=ve(),{$layout:a,getStickyContent:o}=Ls(),i=O(null);let u;const d=s(()=>a.height.value-(a.isContainer.value===!0?a.containerHeight.value:l.screen.height));function f(){return e.reverse===!0?d.value-a.scroll.value.position>e.scrollOffset:a.scroll.value.position>e.scrollOffset}const c=O(f());function h(){const p=f();c.value!==p&&(c.value=p)}function m(){e.reverse===!0?u===void 0&&(u=ne(d,h)):v()}ne(a.scroll,h),ne(()=>e.reverse,m);function v(){u!==void 0&&(u(),u=void 0)}function g(p){const k=Gt(a.isContainer.value===!0?i.value:a.rootRef.value);Ln(k,e.reverse===!0?a.height.value:0,e.duration),n("click",p)}function _(){return c.value===!0?r("div",{ref:i,class:"q-page-scroller",onClick:g},o(t)):null}return m(),Ne(v),()=>r(_t,{name:"q-transition--fade"},_)}}),$h=te({name:"QPageSticky",props:Po,setup(e,{slots:t}){const{getStickyContent:n}=Ls();return()=>n(t)}});function nl(e,t){return[!0,!1].includes(e)?e:t}var Bh=te({name:"QPagination",props:{...Ke,modelValue:{type:Number,required:!0},min:{type:[Number,String],default:1},max:{type:[Number,String],required:!0},maxPages:{type:[Number,String],default:0,validator:e=>(typeof e=="string"?parseInt(e,10):e)>=0},inputStyle:[Array,String,Object],inputClass:[Array,String,Object],size:String,disable:Boolean,input:Boolean,iconPrev:String,iconNext:String,iconFirst:String,iconLast:String,toFn:Function,boundaryLinks:{type:Boolean,default:null},boundaryNumbers:{type:Boolean,default:null},directionLinks:{type:Boolean,default:null},ellipses:{type:Boolean,default:null},ripple:{type:[Boolean,Object],default:null},round:Boolean,rounded:Boolean,flat:Boolean,outline:Boolean,unelevated:Boolean,push:Boolean,glossy:Boolean,color:{type:String,default:"primary"},textColor:String,activeDesign:{type:String,default:"",values:e=>e===""||ku.includes(e)},activeColor:String,activeTextColor:String,gutter:String,padding:{type:String,default:"3px 2px"}},emits:["update:modelValue"],setup(e,{emit:t}){const{proxy:n}=ve(),{$q:l}=n,a=We(e,l),o=s(()=>parseInt(e.min,10)),i=s(()=>parseInt(e.max,10)),u=s(()=>parseInt(e.maxPages,10)),d=s(()=>g.value+" / "+i.value),f=s(()=>nl(e.boundaryLinks,e.input)),c=s(()=>nl(e.boundaryNumbers,!e.input)),h=s(()=>nl(e.directionLinks,e.input)),m=s(()=>nl(e.ellipses,!e.input)),v=O(null),g=s({get:()=>e.modelValue,set:x=>{if(x=parseInt(x,10),e.disable||isNaN(x))return;const q=tt(x,o.value,i.value);e.modelValue!==q&&t("update:modelValue",q)}});ne(()=>`${o.value}|${i.value}`,()=>{g.value=e.modelValue});const _=s(()=>"q-pagination row no-wrap items-center"+(e.disable===!0?" disabled":"")),p=s(()=>e.gutter in yl?`${yl[e.gutter]}px`:e.gutter||null),k=s(()=>p.value!==null?`--q-pagination-gutter-parent:-${p.value};--q-pagination-gutter-child:${p.value}`:null),w=s(()=>{const x=[e.iconFirst||l.iconSet.pagination.first,e.iconPrev||l.iconSet.pagination.prev,e.iconNext||l.iconSet.pagination.next,e.iconLast||l.iconSet.pagination.last];return l.lang.rtl===!0?x.reverse():x}),y=s(()=>({"aria-disabled":e.disable===!0?"true":"false",role:"navigation"})),b=s(()=>Qo(e,"flat")),S=s(()=>({[b.value]:!0,round:e.round,rounded:e.rounded,padding:e.padding,color:e.color,textColor:e.textColor,size:e.size,ripple:e.ripple!==null?e.ripple:!0})),B=s(()=>{const x={[b.value]:!1};return e.activeDesign!==""&&(x[e.activeDesign]=!0),x}),L=s(()=>({...B.value,color:e.activeColor||e.color,textColor:e.activeTextColor||e.textColor})),R=s(()=>{let x=Math.max(u.value,1+(m.value?2:0)+(c.value?2:0));const q={pgFrom:o.value,pgTo:i.value,ellipsesStart:!1,ellipsesEnd:!1,boundaryStart:!1,boundaryEnd:!1,marginalStyle:{minWidth:`${Math.max(2,String(i.value).length)}em`}};return u.value&&x<i.value-o.value+1&&(x=1+Math.floor(x/2)*2,q.pgFrom=Math.max(o.value,Math.min(i.value-x+1,e.modelValue-Math.floor(x/2))),q.pgTo=Math.min(i.value,q.pgFrom+x-1),c.value&&(q.boundaryStart=!0,q.pgFrom++),m.value&&q.pgFrom>o.value+(c.value?1:0)&&(q.ellipsesStart=!0,q.pgFrom++),c.value&&(q.boundaryEnd=!0,q.pgTo--),m.value&&q.pgTo<i.value-(c.value?1:0)&&(q.ellipsesEnd=!0,q.pgTo--)),q});function A(x){g.value=x}function P(x){g.value=g.value+x}const z=s(()=>{function x(){g.value=v.value,v.value=null}return{"onUpdate:modelValue":q=>{v.value=q},onKeyup:q=>{Vt(q,13)===!0&&x()},onBlur:x}});function T(x,q,U){const W={"aria-label":q,"aria-current":"false",...S.value,...x};return U===!0&&Object.assign(W,{"aria-current":"true",...L.value}),q!==void 0&&(e.toFn!==void 0?W.to=e.toFn(q):W.onClick=()=>{A(q)}),r(Xe,W)}return Object.assign(n,{set:A,setByOffset:P}),()=>{const x=[],q=[];let U;if(f.value===!0&&(x.push(T({key:"bls",disable:e.disable||e.modelValue<=o.value,icon:w.value[0]},o.value)),q.unshift(T({key:"ble",disable:e.disable||e.modelValue>=i.value,icon:w.value[3]},i.value))),h.value===!0&&(x.push(T({key:"bdp",disable:e.disable||e.modelValue<=o.value,icon:w.value[1]},e.modelValue-1)),q.unshift(T({key:"bdn",disable:e.disable||e.modelValue>=i.value,icon:w.value[2]},e.modelValue+1))),e.input!==!0){U=[];const{pgFrom:W,pgTo:E,marginalStyle:V}=R.value;if(R.value.boundaryStart===!0){const j=o.value===e.modelValue;x.push(T({key:"bns",style:V,disable:e.disable,label:o.value},o.value,j))}if(R.value.boundaryEnd===!0){const j=i.value===e.modelValue;q.unshift(T({key:"bne",style:V,disable:e.disable,label:i.value},i.value,j))}R.value.ellipsesStart===!0&&x.push(T({key:"bes",style:V,disable:e.disable,label:"\u2026",ripple:!1},W-1)),R.value.ellipsesEnd===!0&&q.unshift(T({key:"bee",style:V,disable:e.disable,label:"\u2026",ripple:!1},E+1));for(let j=W;j<=E;j++)U.push(T({key:`bpg${j}`,style:V,disable:e.disable,label:j},j,j===e.modelValue))}return r("div",{class:_.value,...y.value},[r("div",{class:"q-pagination__content row no-wrap items-center",style:k.value},[...x,e.input===!0?r($s,{class:"inline",style:{width:`${d.value.length/1.5}em`},type:"number",dense:!0,value:v.value,disable:e.disable,dark:a.value,borderless:!0,inputClass:e.inputClass,inputStyle:e.inputStyle,placeholder:d.value,min:o.value,max:i.value,...z.value}):r("div",{class:"q-pagination__middle row justify-center"},U),...q])])}}});function so(e){let t=!1,n,l;function a(){l=arguments,t!==!0&&(t=!0,n=window.requestAnimationFrame(()=>{e.apply(this,l),l=void 0,t=!1}))}return a.cancel=()=>{window.cancelAnimationFrame(n),t=!1},a}var{passive:al}=et,Ph=te({name:"QParallax",props:{src:String,height:{type:Number,default:500},speed:{type:Number,default:1,validator:e=>e>=0&&e<=1},scrollTarget:Qn,onScroll:Function},setup(e,{slots:t,emit:n}){const l=O(0),a=O(null),o=O(null),i=O(null);let u,d,f,c,h,m;ne(()=>e.height,()=>{u===!0&&g()}),ne(()=>e.scrollTarget,()=>{u===!0&&(w(),k())});let v=y=>{l.value=y,e.onScroll!==void 0&&n("scroll",y)};function g(){let y,b,S;m===window?(y=0,S=b=window.innerHeight):(y=za(m).top,b=Fn(m),S=y+b);const B=za(a.value).top,L=B+e.height;if(h!==void 0||L>y&&B<S){const R=(S-B)/(e.height+b);_((f-e.height)*R*e.speed),v(R)}}let _=y=>{d.style.transform=`translate3d(-50%,${Math.round(y)}px,0)`};function p(){f=d.naturalHeight||d.videoHeight||Fn(d),u===!0&&g()}function k(){u=!0,m=Gt(a.value,e.scrollTarget),m.addEventListener("scroll",g,al),window.addEventListener("resize",c,al),g()}function w(){u===!0&&(u=!1,m.removeEventListener("scroll",g,al),window.removeEventListener("resize",c,al),m=void 0,_.cancel(),v.cancel(),c.cancel())}return ft(()=>{_=so(_),v=so(v),c=so(p),d=t.media!==void 0?o.value.children[0]:i.value,d.onload=d.onloadstart=d.loadedmetadata=p,p(),d.style.display="initial",window.IntersectionObserver!==void 0?(h=new IntersectionObserver(y=>{(y[0].isIntersecting===!0?k:w)()}),h.observe(a.value)):k()}),Ne(()=>{w(),h!==void 0&&h.disconnect(),d.onload=d.onloadstart=d.loadedmetadata=null}),()=>r("div",{ref:a,class:"q-parallax",style:{height:`${e.height}px`}},[r("div",{ref:o,class:"q-parallax__media absolute-full"},t.media!==void 0?t.media():[r("img",{ref:i,src:e.src})]),r("div",{class:"q-parallax__content absolute-full column flex-center"},t.content!==void 0?t.content({percentScrolled:l.value}):Ce(t.default))])}});function Pa(e,t=new WeakMap){if(Object(e)!==e)return e;if(t.has(e))return t.get(e);const n=e instanceof Date?new Date(e):e instanceof RegExp?new RegExp(e.source,e.flags):e instanceof Set?new Set:e instanceof Map?new Map:typeof e.constructor!="function"?Object.create(null):e.prototype!==void 0&&typeof e.prototype.constructor=="function"?e:new e.constructor;if(typeof e.constructor=="function"&&typeof e.valueOf=="function"){const l=e.valueOf();if(Object(l)!==l){const a=new e.constructor(l);return t.set(e,a),a}}return t.set(e,n),e instanceof Set?e.forEach(l=>{n.add(Pa(l,t))}):e instanceof Map&&e.forEach((l,a)=>{n.set(a,Pa(l,t))}),Object.assign(n,...Object.keys(e).map(l=>({[l]:Pa(e[l],t)})))}var Eh=te({name:"QPopupEdit",props:{modelValue:{required:!0},title:String,buttons:Boolean,labelSet:String,labelCancel:String,color:{type:String,default:"primary"},validate:{type:Function,default:()=>!0},autoSave:Boolean,cover:{type:Boolean,default:!0},disable:Boolean},emits:["update:modelValue","save","cancel","beforeShow","show","beforeHide","hide"],setup(e,{slots:t,emit:n}){const{proxy:l}=ve(),{$q:a}=l,o=O(null),i=O(""),u=O("");let d=!1;const f=s(()=>St({initialValue:i.value,validate:e.validate,set:c,cancel:h,updatePosition:m},"value",()=>u.value,b=>{u.value=b}));function c(){e.validate(u.value)!==!1&&(v()===!0&&(n("save",u.value,i.value),n("update:modelValue",u.value)),g())}function h(){v()===!0&&n("cancel",u.value,i.value),g()}function m(){Qe(()=>{o.value.updatePosition()})}function v(){return Rt(u.value,i.value)===!1}function g(){d=!0,o.value.hide()}function _(){d=!1,i.value=Pa(e.modelValue),u.value=Pa(e.modelValue),n("beforeShow")}function p(){n("show")}function k(){d===!1&&v()===!0&&(e.autoSave===!0&&e.validate(u.value)===!0?(n("save",u.value,i.value),n("update:modelValue",u.value)):n("cancel",u.value,i.value)),n("beforeHide")}function w(){n("hide")}function y(){const b=t.default!==void 0?[].concat(t.default(f.value)):[];return e.title&&b.unshift(r("div",{class:"q-dialog__title q-mt-sm q-mb-sm"},e.title)),e.buttons===!0&&b.push(r("div",{class:"q-popup-edit__buttons row justify-center no-wrap"},[r(Xe,{flat:!0,color:e.color,label:e.labelCancel||a.lang.label.cancel,onClick:h}),r(Xe,{flat:!0,color:e.color,label:e.labelSet||a.lang.label.set,onClick:c})])),b}return Object.assign(l,{set:c,cancel:h,show(b){o.value!==null&&o.value.show(b)},hide(b){o.value!==null&&o.value.hide(b)},updatePosition:m}),()=>{if(e.disable!==!0)return r(Fl,{ref:o,class:"q-popup-edit",cover:e.cover,onBeforeShow:_,onShow:p,onBeforeHide:k,onHide:w,onEscapeKey:h},y)}}}),Lh=te({name:"QPopupProxy",props:{...$u,breakpoint:{type:[String,Number],default:450}},emits:["show","hide"],setup(e,{slots:t,emit:n,attrs:l}){const{proxy:a}=ve(),{$q:o}=a,i=O(!1),u=O(null),d=s(()=>parseInt(e.breakpoint,10)),{canShow:f}=Ko({showing:i});function c(){return o.screen.width<d.value||o.screen.height<d.value?"dialog":"menu"}const h=O(c()),m=s(()=>h.value==="menu"?{maxHeight:"99vh"}:{});ne(()=>c(),_=>{i.value!==!0&&(h.value=_)});function v(_){i.value=!0,n("show",_)}function g(_){i.value=!1,h.value=c(),n("hide",_)}return Object.assign(a,{show(_){f(_)===!0&&u.value.show(_)},hide(_){u.value.hide(_)},toggle(_){u.value.toggle(_)}}),St(a,"currentComponent",()=>({type:h.value,ref:u.value})),()=>{const _={ref:u,...m.value,...l,onShow:v,onHide:g};let p;return h.value==="dialog"?p=Dl:(p=Fl,Object.assign(_,{target:e.target,contextMenu:e.contextMenu,noParentEvent:!0,separateClosePopup:!0})),r(p,_,t.default)}}}),Ov={xs:2,sm:4,md:6,lg:10,xl:14};function _i(e,t,n){return{transform:t===!0?`translateX(${n.lang.rtl===!0?"-":""}100%) scale3d(${-e},1,1)`:`scale3d(${e},1,1)`}}var Rv=te({name:"QLinearProgress",props:{...Ke,...sn,value:{type:Number,default:0},buffer:Number,color:String,trackColor:String,reverse:Boolean,stripe:Boolean,indeterminate:Boolean,query:Boolean,rounded:Boolean,animationSpeed:{type:[String,Number],default:2100},instantFeedback:Boolean},setup(e,{slots:t}){const{proxy:n}=ve(),l=We(e,n.$q),a=cn(e,Ov),o=s(()=>e.indeterminate===!0||e.query===!0),i=s(()=>e.reverse!==e.query),u=s(()=>({...a.value!==null?a.value:{},"--q-linear-progress-speed":`${e.animationSpeed}ms`})),d=s(()=>"q-linear-progress"+(e.color!==void 0?` text-${e.color}`:"")+(e.reverse===!0||e.query===!0?" q-linear-progress--reverse":"")+(e.rounded===!0?" rounded-borders":"")),f=s(()=>_i(e.buffer!==void 0?e.buffer:1,i.value,n.$q)),c=s(()=>`with${e.instantFeedback===!0?"out":""}-transition`),h=s(()=>`q-linear-progress__track absolute-full q-linear-progress__track--${c.value} q-linear-progress__track--${l.value===!0?"dark":"light"}`+(e.trackColor!==void 0?` bg-${e.trackColor}`:"")),m=s(()=>_i(o.value===!0?1:e.value,i.value,n.$q)),v=s(()=>`q-linear-progress__model absolute-full q-linear-progress__model--${c.value} q-linear-progress__model--${o.value===!0?"in":""}determinate`),g=s(()=>({width:`${e.value*100}%`})),_=s(()=>`q-linear-progress__stripe absolute-${e.reverse===!0?"right":"left"} q-linear-progress__stripe--${c.value}`);return()=>{const p=[r("div",{class:h.value,style:f.value}),r("div",{class:v.value,style:m.value})];return e.stripe===!0&&o.value===!1&&p.push(r("div",{class:_.value,style:g.value})),r("div",{class:d.value,style:u.value,role:"progressbar","aria-valuemin":0,"aria-valuemax":1,"aria-valuenow":e.indeterminate===!0?void 0:e.value},mt(t.default,p))}}}),Zn=40,co=20,Ah=te({name:"QPullToRefresh",props:{color:String,bgColor:String,icon:String,noMouse:Boolean,disable:Boolean,scrollTarget:Qn},emits:["refresh"],setup(e,{slots:t,emit:n}){const{proxy:l}=ve(),{$q:a}=l,o=O("pull"),i=O(0),u=O(!1),d=O(-Zn),f=O(!1),c=O({}),h=s(()=>({opacity:i.value,transform:`translateY(${d.value}px) rotate(${i.value*360}deg)`})),m=s(()=>"q-pull-to-refresh__puller row flex-center"+(f.value===!0?" q-pull-to-refresh__puller--animating":"")+(e.bgColor!==void 0?` bg-${e.bgColor}`:""));function v(B){if(B.isFinal===!0){u.value===!0&&(u.value=!1,o.value==="pulled"?(o.value="refreshing",b({pos:co}),p()):o.value==="pull"&&b({pos:-Zn,ratio:0}));return}if(f.value===!0||o.value==="refreshing")return!1;if(B.isFirst===!0){if(rn(w)!==0||B.direction!=="down")return u.value===!0&&(u.value=!1,o.value="pull",b({pos:-Zn,ratio:0})),!1;u.value=!0;const{top:A,left:P}=k.getBoundingClientRect();c.value={top:A+"px",left:P+"px",width:window.getComputedStyle(k).getPropertyValue("width")}}wt(B.evt);const L=Math.min(140,Math.max(0,B.distance.y));d.value=L-Zn,i.value=tt(L/(co+Zn),0,1);const R=d.value>co?"pulled":"pull";o.value!==R&&(o.value=R)}const g=s(()=>{const B={down:!0};return e.noMouse!==!0&&(B.mouse=!0),[[Ft,v,void 0,B]]}),_=s(()=>`q-pull-to-refresh__content${u.value===!0?" no-pointer-events":""}`);function p(){n("refresh",()=>{b({pos:-Zn,ratio:0},()=>{o.value="pull"})})}let k,w,y=null;function b({pos:B,ratio:L},R){f.value=!0,d.value=B,L!==void 0&&(i.value=L),y!==null&&clearTimeout(y),y=setTimeout(()=>{y=null,f.value=!1,R&&R()},300)}function S(){w=Gt(k,e.scrollTarget)}return ne(()=>e.scrollTarget,S),ft(()=>{k=l.$el,S()}),Ne(()=>{y!==null&&clearTimeout(y)}),Object.assign(l,{trigger:p,updateScrollTarget:S}),()=>{const B=[r("div",{class:_.value},Ce(t.default)),r("div",{class:"q-pull-to-refresh__puller-container fixed row flex-center no-pointer-events z-top",style:c.value},[r("div",{class:m.value,style:h.value},[o.value!=="refreshing"?r(je,{name:e.icon||a.iconSet.pullToRefresh.icon,color:e.color,size:"32px"}):r(It,{size:"24px",color:e.color})])])];return Dt("div",{class:"q-pull-to-refresh"},B,"main",e.disable===!1,()=>g.value)}}}),fn={MIN:0,RANGE:1,MAX:2},zh=te({name:"QRange",props:{...Yu,modelValue:{type:Object,default:()=>({min:null,max:null}),validator:e=>"min"in e&&"max"in e},dragRange:Boolean,dragOnlyRange:Boolean,leftLabelColor:String,leftLabelTextColor:String,rightLabelColor:String,rightLabelTextColor:String,leftLabelValue:[String,Number],rightLabelValue:[String,Number],leftThumbColor:String,rightThumbColor:String},emits:Xu,setup(e,{emit:t}){const{proxy:{$q:n}}=ve(),{state:l,methods:a}=Gu({updateValue:R,updatePosition:P,getDragging:A,formAttrs:s(()=>({type:"hidden",name:e.name,value:`${e.modelValue.min}|${e.modelValue.max}`}))}),o=O(null),i=O(0),u=O(0),d=O({min:0,max:0});function f(){d.value.min=e.modelValue.min===null?l.innerMin.value:tt(e.modelValue.min,l.innerMin.value,l.innerMax.value),d.value.max=e.modelValue.max===null?l.innerMax.value:tt(e.modelValue.max,l.innerMin.value,l.innerMax.value)}ne(()=>`${e.modelValue.min}|${e.modelValue.max}|${l.innerMin.value}|${l.innerMax.value}`,f),f();const c=s(()=>a.convertModelToRatio(d.value.min)),h=s(()=>a.convertModelToRatio(d.value.max)),m=s(()=>l.active.value===!0?i.value:c.value),v=s(()=>l.active.value===!0?u.value:h.value),g=s(()=>{const T={[l.positionProp.value]:`${100*m.value}%`,[l.sizeProp.value]:`${100*(v.value-m.value)}%`};return e.selectionImg!==void 0&&(T.backgroundImage=`url(${e.selectionImg}) !important`),T}),_=s(()=>{if(l.editable.value!==!0)return{};if(n.platform.is.mobile===!0)return{onClick:a.onMobileClick};const T={onMousedown:a.onActivate};return(e.dragRange===!0||e.dragOnlyRange===!0)&&Object.assign(T,{onFocus:()=>{l.focus.value="both"},onBlur:a.onBlur,onKeydown:z,onKeyup:a.onKeyup}),T});function p(T){return n.platform.is.mobile!==!0&&l.editable.value===!0&&e.dragOnlyRange!==!0?{onFocus:()=>{l.focus.value=T},onBlur:a.onBlur,onKeydown:z,onKeyup:a.onKeyup}:{}}const k=s(()=>e.dragOnlyRange!==!0?l.tabindex.value:null),w=s(()=>n.platform.is.mobile!==!0&&(e.dragRange||e.dragOnlyRange===!0)?l.tabindex.value:null),y=O(null),b=s(()=>p("min")),S=a.getThumbRenderFn({focusValue:"min",getNodeData:()=>({ref:y,key:"tmin",...b.value,tabindex:k.value}),ratio:m,label:s(()=>e.leftLabelValue!==void 0?e.leftLabelValue:d.value.min),thumbColor:s(()=>e.leftThumbColor||e.thumbColor||e.color),labelColor:s(()=>e.leftLabelColor||e.labelColor),labelTextColor:s(()=>e.leftLabelTextColor||e.labelTextColor)}),B=s(()=>p("max")),L=a.getThumbRenderFn({focusValue:"max",getNodeData:()=>({...B.value,key:"tmax",tabindex:k.value}),ratio:v,label:s(()=>e.rightLabelValue!==void 0?e.rightLabelValue:d.value.max),thumbColor:s(()=>e.rightThumbColor||e.thumbColor||e.color),labelColor:s(()=>e.rightLabelColor||e.labelColor),labelTextColor:s(()=>e.rightLabelTextColor||e.labelTextColor)});function R(T){(d.value.min!==e.modelValue.min||d.value.max!==e.modelValue.max)&&t("update:modelValue",{...d.value}),T===!0&&t("change",{...d.value})}function A(T){const{left:x,top:q,width:U,height:W}=o.value.getBoundingClientRect(),E=e.dragOnlyRange===!0?0:e.vertical===!0?y.value.offsetHeight/(2*W):y.value.offsetWidth/(2*U),V={left:x,top:q,width:U,height:W,valueMin:d.value.min,valueMax:d.value.max,ratioMin:c.value,ratioMax:h.value},j=a.getDraggingRatio(T,V);return e.dragOnlyRange!==!0&&j<V.ratioMin+E?V.type=fn.MIN:e.dragOnlyRange===!0||j<V.ratioMax-E?e.dragRange===!0||e.dragOnlyRange===!0?(V.type=fn.RANGE,Object.assign(V,{offsetRatio:j,offsetModel:a.convertRatioToModel(j),rangeValue:V.valueMax-V.valueMin,rangeRatio:V.ratioMax-V.ratioMin})):V.type=V.ratioMax-j<j-V.ratioMin?fn.MAX:fn.MIN:V.type=fn.MAX,V}function P(T,x=l.dragging.value){let q;const U=a.getDraggingRatio(T,x),W=a.convertRatioToModel(U);switch(x.type){case fn.MIN:U<=x.ratioMax?(q={minR:U,maxR:x.ratioMax,min:W,max:x.valueMax},l.focus.value="min"):(q={minR:x.ratioMax,maxR:U,min:x.valueMax,max:W},l.focus.value="max");break;case fn.MAX:U>=x.ratioMin?(q={minR:x.ratioMin,maxR:U,min:x.valueMin,max:W},l.focus.value="max"):(q={minR:U,maxR:x.ratioMin,min:W,max:x.valueMin},l.focus.value="min");break;case fn.RANGE:const E=U-x.offsetRatio,V=tt(x.ratioMin+E,l.innerMinRatio.value,l.innerMaxRatio.value-x.rangeRatio),j=W-x.offsetModel,ue=tt(x.valueMin+j,l.innerMin.value,l.innerMax.value-x.rangeValue);q={minR:V,maxR:V+x.rangeRatio,min:l.roundValueFn.value(ue),max:l.roundValueFn.value(ue+x.rangeValue)},l.focus.value="both";break}d.value=d.value.min===null||d.value.max===null?{min:q.min||e.min,max:q.max||e.max}:{min:q.min,max:q.max},e.snap!==!0||e.step===0?(i.value=q.minR,u.value=q.maxR):(i.value=a.convertModelToRatio(d.value.min),u.value=a.convertModelToRatio(d.value.max))}function z(T){if(!sr.includes(T.keyCode))return;Fe(T);const x=([34,33].includes(T.keyCode)?10:1)*l.keyStep.value,q=([34,37,40].includes(T.keyCode)?-1:1)*(l.isReversed.value===!0?-1:1)*(e.vertical===!0?-1:1)*x;if(l.focus.value==="both"){const U=d.value.max-d.value.min,W=tt(l.roundValueFn.value(d.value.min+q),l.innerMin.value,l.innerMax.value-U);d.value={min:W,max:l.roundValueFn.value(W+U)}}else{if(l.focus.value===!1)return;{const U=l.focus.value;d.value={...d.value,[U]:tt(l.roundValueFn.value(d.value[U]+q),U==="min"?l.innerMin.value:d.value.min,U==="max"?l.innerMax.value:d.value.max)}}}R()}return()=>{const T=a.getContent(g,w,_,x=>{x.push(S(),L())});return r("div",{ref:o,class:"q-range "+l.classes.value+(e.modelValue.min===null||e.modelValue.max===null?" q-slider--no-value":""),...l.attributes.value,"aria-valuenow":e.modelValue.min+"|"+e.modelValue.max},T)}}}),Oh=te({name:"QRating",props:{...sn,...Ht,modelValue:{type:Number,required:!0},max:{type:[String,Number],default:5},icon:[String,Array],iconHalf:[String,Array],iconSelected:[String,Array],iconAriaLabel:[String,Array],color:[String,Array],colorHalf:[String,Array],colorSelected:[String,Array],noReset:Boolean,noDimming:Boolean,readonly:Boolean,disable:Boolean},emits:["update:modelValue"],setup(e,{slots:t,emit:n}){const{proxy:{$q:l}}=ve(),a=cn(e),o=Ka(e),i=wn(o),u=O(0);let d={};const f=s(()=>e.readonly!==!0&&e.disable!==!0),c=s(()=>`q-rating row inline items-center q-rating--${f.value===!0?"":"non-"}editable`+(e.noDimming===!0?" q-rating--no-dimming":"")+(e.disable===!0?" disabled":"")+(e.color!==void 0&&Array.isArray(e.color)===!1?` text-${e.color}`:"")),h=s(()=>{const y=Array.isArray(e.icon)===!0?e.icon.length:0,b=Array.isArray(e.iconSelected)===!0?e.iconSelected.length:0,S=Array.isArray(e.iconHalf)===!0?e.iconHalf.length:0,B=Array.isArray(e.color)===!0?e.color.length:0,L=Array.isArray(e.colorSelected)===!0?e.colorSelected.length:0,R=Array.isArray(e.colorHalf)===!0?e.colorHalf.length:0;return{iconLen:y,icon:y>0?e.icon[y-1]:e.icon,selIconLen:b,selIcon:b>0?e.iconSelected[b-1]:e.iconSelected,halfIconLen:S,halfIcon:S>0?e.iconHalf[b-1]:e.iconHalf,colorLen:B,color:B>0?e.color[B-1]:e.color,selColorLen:L,selColor:L>0?e.colorSelected[L-1]:e.colorSelected,halfColorLen:R,halfColor:R>0?e.colorHalf[R-1]:e.colorHalf}}),m=s(()=>{if(typeof e.iconAriaLabel=="string"){const y=e.iconAriaLabel.length!==0?`${e.iconAriaLabel} `:"";return b=>`${y}${b}`}if(Array.isArray(e.iconAriaLabel)===!0){const y=e.iconAriaLabel.length;if(y>0)return b=>e.iconAriaLabel[Math.min(b,y)-1]}return(y,b)=>`${b} ${y}`}),v=s(()=>{const y=[],b=h.value,S=Math.ceil(e.modelValue),B=f.value===!0?0:null,L=e.iconHalf===void 0||S===e.modelValue?-1:S;for(let R=1;R<=e.max;R++){const A=u.value===0&&e.modelValue>=R||u.value>0&&u.value>=R,P=L===R&&u.value<R,z=u.value>0&&(P===!0?S:e.modelValue)>=R&&u.value<R,T=P===!0?R<=b.halfColorLen?e.colorHalf[R-1]:b.halfColor:b.selColor!==void 0&&A===!0?R<=b.selColorLen?e.colorSelected[R-1]:b.selColor:R<=b.colorLen?e.color[R-1]:b.color,x=(P===!0?R<=b.halfIconLen?e.iconHalf[R-1]:b.halfIcon:b.selIcon!==void 0&&(A===!0||z===!0)?R<=b.selIconLen?e.iconSelected[R-1]:b.selIcon:R<=b.iconLen?e.icon[R-1]:b.icon)||l.iconSet.rating.icon;y.push({name:(P===!0?R<=b.halfIconLen?e.iconHalf[R-1]:b.halfIcon:b.selIcon!==void 0&&(A===!0||z===!0)?R<=b.selIconLen?e.iconSelected[R-1]:b.selIcon:R<=b.iconLen?e.icon[R-1]:b.icon)||l.iconSet.rating.icon,attrs:{tabindex:B,role:"radio","aria-checked":e.modelValue===R?"true":"false","aria-label":m.value(R,x)},iconClass:"q-rating__icon"+(A===!0||P===!0?" q-rating__icon--active":"")+(z===!0?" q-rating__icon--exselected":"")+(u.value===R?" q-rating__icon--hovered":"")+(T!==void 0?` text-${T}`:"")})}return y}),g=s(()=>{const y={role:"radiogroup"};return e.disable===!0&&(y["aria-disabled"]="true"),e.readonly===!0&&(y["aria-readonly"]="true"),y});function _(y){if(f.value===!0){const b=tt(parseInt(y,10),1,parseInt(e.max,10)),S=e.noReset!==!0&&e.modelValue===b?0:b;S!==e.modelValue&&n("update:modelValue",S),u.value=0}}function p(y){f.value===!0&&(u.value=y)}function k(y,b){switch(y.keyCode){case 13:case 32:return _(b),Fe(y);case 37:case 40:return d[`rt${b-1}`]&&d[`rt${b-1}`].focus(),Fe(y);case 39:case 38:return d[`rt${b+1}`]&&d[`rt${b+1}`].focus(),Fe(y)}}function w(){u.value=0}return Da(()=>{d={}}),()=>{const y=[];return v.value.forEach(({iconClass:b,name:S,attrs:B},L)=>{const R=L+1;y.push(r("div",{key:R,ref:A=>{d[`rt${R}`]=A},class:"q-rating__icon-container flex flex-center",...B,onClick(){_(R)},onMouseover(){p(R)},onMouseout:w,onFocus(){p(R)},onBlur:w,onKeyup(A){k(A,R)}},mt(t[`tip-${R}`],[r(je,{class:b,name:S})])))}),e.name!==void 0&&e.disable!==!0&&i(y,"push"),r("div",{class:c.value,style:a.value,...g.value},y)}}}),Rh=te({name:"QResponsive",props:pr,setup(e,{slots:t}){const n=wr(e);return()=>r("div",{class:"q-responsive"},[r("div",{class:"q-responsive__filler overflow-hidden"},[r("div",{style:n.value})]),r("div",{class:"q-responsive__content absolute-full fit"},Ce(t.default))])}}),Si=["vertical","horizontal"],fo={vertical:{offset:"offsetY",scroll:"scrollTop",dir:"down",dist:"y"},horizontal:{offset:"offsetX",scroll:"scrollLeft",dir:"right",dist:"x"}},xi={prevent:!0,mouse:!0,mouseAllDir:!0},Ci=e=>e>=250?50:Math.ceil(e/5),Fh=te({name:"QScrollArea",props:{...Ke,thumbStyle:Object,verticalThumbStyle:Object,horizontalThumbStyle:Object,barStyle:[Array,String,Object],verticalBarStyle:[Array,String,Object],horizontalBarStyle:[Array,String,Object],contentStyle:[Array,String,Object],contentActiveStyle:[Array,String,Object],delay:{type:[String,Number],default:1e3},visible:{type:Boolean,default:null},tabindex:[String,Number],onScroll:Function},setup(e,{slots:t,emit:n}){const l=O(!1),a=O(!1),o=O(!1),i={vertical:O(0),horizontal:O(0)},u={vertical:{ref:O(null),position:O(0),size:O(0)},horizontal:{ref:O(null),position:O(0),size:O(0)}},{proxy:d}=ve(),f=We(e,d.$q);let c=null,h;const m=O(null),v=s(()=>"q-scrollarea"+(f.value===!0?" q-scrollarea--dark":""));u.vertical.percentage=s(()=>{const E=u.vertical.size.value-i.vertical.value;if(E<=0)return 0;const V=tt(u.vertical.position.value/E,0,1);return Math.round(V*1e4)/1e4}),u.vertical.thumbHidden=s(()=>(e.visible===null?o.value:e.visible)!==!0&&l.value===!1&&a.value===!1||u.vertical.size.value<=i.vertical.value+1),u.vertical.thumbStart=s(()=>u.vertical.percentage.value*(i.vertical.value-u.vertical.thumbSize.value)),u.vertical.thumbSize=s(()=>Math.round(tt(i.vertical.value*i.vertical.value/u.vertical.size.value,Ci(i.vertical.value),i.vertical.value))),u.vertical.style=s(()=>({...e.thumbStyle,...e.verticalThumbStyle,top:`${u.vertical.thumbStart.value}px`,height:`${u.vertical.thumbSize.value}px`})),u.vertical.thumbClass=s(()=>"q-scrollarea__thumb q-scrollarea__thumb--v absolute-right"+(u.vertical.thumbHidden.value===!0?" q-scrollarea__thumb--invisible":"")),u.vertical.barClass=s(()=>"q-scrollarea__bar q-scrollarea__bar--v absolute-right"+(u.vertical.thumbHidden.value===!0?" q-scrollarea__bar--invisible":"")),u.horizontal.percentage=s(()=>{const E=u.horizontal.size.value-i.horizontal.value;if(E<=0)return 0;const V=tt(Math.abs(u.horizontal.position.value)/E,0,1);return Math.round(V*1e4)/1e4}),u.horizontal.thumbHidden=s(()=>(e.visible===null?o.value:e.visible)!==!0&&l.value===!1&&a.value===!1||u.horizontal.size.value<=i.horizontal.value+1),u.horizontal.thumbStart=s(()=>u.horizontal.percentage.value*(i.horizontal.value-u.horizontal.thumbSize.value)),u.horizontal.thumbSize=s(()=>Math.round(tt(i.horizontal.value*i.horizontal.value/u.horizontal.size.value,Ci(i.horizontal.value),i.horizontal.value))),u.horizontal.style=s(()=>({...e.thumbStyle,...e.horizontalThumbStyle,[d.$q.lang.rtl===!0?"right":"left"]:`${u.horizontal.thumbStart.value}px`,width:`${u.horizontal.thumbSize.value}px`})),u.horizontal.thumbClass=s(()=>"q-scrollarea__thumb q-scrollarea__thumb--h absolute-bottom"+(u.horizontal.thumbHidden.value===!0?" q-scrollarea__thumb--invisible":"")),u.horizontal.barClass=s(()=>"q-scrollarea__bar q-scrollarea__bar--h absolute-bottom"+(u.horizontal.thumbHidden.value===!0?" q-scrollarea__bar--invisible":""));const g=s(()=>u.vertical.thumbHidden.value===!0&&u.horizontal.thumbHidden.value===!0?e.contentStyle:e.contentActiveStyle),_=[[Ft,E=>{L(E,"vertical")},void 0,{vertical:!0,...xi}]],p=[[Ft,E=>{L(E,"horizontal")},void 0,{horizontal:!0,...xi}]];function k(){const E={};return Si.forEach(V=>{const j=u[V];E[V+"Position"]=j.position.value,E[V+"Percentage"]=j.percentage.value,E[V+"Size"]=j.size.value,E[V+"ContainerSize"]=i[V].value}),E}const w=fa(()=>{const E=k();E.ref=d,n("scroll",E)},0);function y(E,V,j){if(Si.includes(E)===!1){console.error("[QScrollArea]: wrong first param of setScrollPosition (vertical/horizontal)");return}(E==="vertical"?Ln:sl)(m.value,V,j)}function b({height:E,width:V}){let j=!1;i.vertical.value!==E&&(i.vertical.value=E,j=!0),i.horizontal.value!==V&&(i.horizontal.value=V,j=!0),j===!0&&z()}function S({position:E}){let V=!1;u.vertical.position.value!==E.top&&(u.vertical.position.value=E.top,V=!0),u.horizontal.position.value!==E.left&&(u.horizontal.position.value=E.left,V=!0),V===!0&&z()}function B({height:E,width:V}){u.horizontal.size.value!==V&&(u.horizontal.size.value=V,z()),u.vertical.size.value!==E&&(u.vertical.size.value=E,z())}function L(E,V){const j=u[V];if(E.isFirst===!0){if(j.thumbHidden.value===!0)return;h=j.position.value,a.value=!0}else if(a.value!==!0)return;E.isFinal===!0&&(a.value=!1);const ue=fo[V],I=i[V].value,C=(j.size.value-I)/(I-j.thumbSize.value),K=E.distance[ue.dist],Q=h+(E.direction===ue.dir?1:-1)*K*C;T(Q,V)}function R(E,V){const j=u[V];if(j.thumbHidden.value!==!0){const ue=E[fo[V].offset];if(ue<j.thumbStart.value||ue>j.thumbStart.value+j.thumbSize.value){const I=ue-j.thumbSize.value/2;T(I/i[V].value*j.size.value,V)}j.ref.value!==null&&j.ref.value.dispatchEvent(new MouseEvent(E.type,E))}}function A(E){R(E,"vertical")}function P(E){R(E,"horizontal")}function z(){l.value=!0,c!==null&&clearTimeout(c),c=setTimeout(()=>{c=null,l.value=!1},e.delay),e.onScroll!==void 0&&w()}function T(E,V){m.value[fo[V].scroll]=E}let x=null;function q(){x!==null&&clearTimeout(x),x=setTimeout(()=>{x=null,o.value=!0},d.$q.platform.is.ios?50:0)}function U(){x!==null&&(clearTimeout(x),x=null),o.value=!1}let W=null;return ne(()=>d.$q.lang.rtl,E=>{m.value!==null&&sl(m.value,Math.abs(u.horizontal.position.value)*(E===!0?-1:1))}),Yt(()=>{W={top:u.vertical.position.value,left:u.horizontal.position.value}}),bn(()=>{if(W===null)return;const E=m.value;E!==null&&(sl(E,W.left),Ln(E,W.top))}),Ne(w.cancel),Object.assign(d,{getScrollTarget:()=>m.value,getScroll:k,getScrollPosition:()=>({top:u.vertical.position.value,left:u.horizontal.position.value}),getScrollPercentage:()=>({top:u.vertical.percentage.value,left:u.horizontal.percentage.value}),setScrollPosition:y,setScrollPercentage(E,V,j){y(E,V*(u[E].size.value-i[E].value)*(E==="horizontal"&&d.$q.lang.rtl===!0?-1:1),j)}}),()=>r("div",{class:v.value,onMouseenter:q,onMouseleave:U},[r("div",{ref:m,class:"q-scrollarea__container scroll relative-position fit hide-scrollbar",tabindex:e.tabindex!==void 0?e.tabindex:void 0},[r("div",{class:"q-scrollarea__content absolute",style:g.value},mt(t.default,[r(Vn,{debounce:0,onResize:B})])),r(Ps,{axis:"both",onScroll:S})]),r(Vn,{debounce:0,onResize:b}),r("div",{class:u.vertical.barClass.value,style:[e.barStyle,e.verticalBarStyle],"aria-hidden":"true",onMousedown:A}),r("div",{class:u.horizontal.barClass.value,style:[e.barStyle,e.horizontalBarStyle],"aria-hidden":"true",onMousedown:P}),Ut(r("div",{ref:u.vertical.ref,class:u.vertical.thumbClass.value,style:u.vertical.style.value,"aria-hidden":"true"}),_),Ut(r("div",{ref:u.horizontal.ref,class:u.horizontal.thumbClass.value,style:u.horizontal.style.value,"aria-hidden":"true"}),p)])}}),jt=1e3,Fv=["start","center","end","start-force","center-force","end-force"],As=Array.prototype.filter,Vv=window.getComputedStyle(document.body).overflowAnchor===void 0?it:function(e,t){e!==null&&(e._qOverflowAnimationFrame!==void 0&&cancelAnimationFrame(e._qOverflowAnimationFrame),e._qOverflowAnimationFrame=requestAnimationFrame(()=>{if(e===null)return;e._qOverflowAnimationFrame=void 0;const n=e.children||[];As.call(n,a=>a.dataset&&a.dataset.qVsAnchor!==void 0).forEach(a=>{delete a.dataset.qVsAnchor});const l=n[t];l&&l.dataset&&(l.dataset.qVsAnchor="")}))};function ua(e,t){return e+t}function vo(e,t,n,l,a,o,i,u){const d=e===window?document.scrollingElement||document.documentElement:e,f=a===!0?"offsetWidth":"offsetHeight",c={scrollStart:0,scrollViewSize:-i-u,scrollMaxSize:0,offsetStart:-i,offsetEnd:-u};if(a===!0?(e===window?(c.scrollStart=window.pageXOffset||window.scrollX||document.body.scrollLeft||0,c.scrollViewSize+=document.documentElement.clientWidth):(c.scrollStart=d.scrollLeft,c.scrollViewSize+=d.clientWidth),c.scrollMaxSize=d.scrollWidth,o===!0&&(c.scrollStart=(Va===!0?c.scrollMaxSize-c.scrollViewSize:0)-c.scrollStart)):(e===window?(c.scrollStart=window.pageYOffset||window.scrollY||document.body.scrollTop||0,c.scrollViewSize+=document.documentElement.clientHeight):(c.scrollStart=d.scrollTop,c.scrollViewSize+=d.clientHeight),c.scrollMaxSize=d.scrollHeight),n!==null)for(let h=n.previousElementSibling;h!==null;h=h.previousElementSibling)h.classList.contains("q-virtual-scroll--skip")===!1&&(c.offsetStart+=h[f]);if(l!==null)for(let h=l.nextElementSibling;h!==null;h=h.nextElementSibling)h.classList.contains("q-virtual-scroll--skip")===!1&&(c.offsetEnd+=h[f]);if(t!==e){const h=d.getBoundingClientRect(),m=t.getBoundingClientRect();a===!0?(c.offsetStart+=m.left-h.left,c.offsetEnd-=m.width):(c.offsetStart+=m.top-h.top,c.offsetEnd-=m.height),e!==window&&(c.offsetStart+=c.scrollStart),c.offsetEnd+=c.scrollMaxSize-c.offsetStart}return c}function ki(e,t,n,l){t==="end"&&(t=(e===window?document.body:e)[n===!0?"scrollWidth":"scrollHeight"]),e===window?n===!0?(l===!0&&(t=(Va===!0?document.body.scrollWidth-document.documentElement.clientWidth:0)-t),window.scrollTo(t,window.pageYOffset||window.scrollY||document.body.scrollTop||0)):window.scrollTo(window.pageXOffset||window.scrollX||document.body.scrollLeft||0,t):n===!0?(l===!0&&(t=(Va===!0?e.scrollWidth-e.offsetWidth:0)-t),e.scrollLeft=t):e.scrollTop=t}function wa(e,t,n,l){if(n>=l)return 0;const a=t.length,o=Math.floor(n/jt),i=Math.floor((l-1)/jt)+1;let u=e.slice(o,i).reduce(ua,0);return n%jt!==0&&(u-=t.slice(o*jt,n).reduce(ua,0)),l%jt!==0&&l!==a&&(u-=t.slice(l,i*jt).reduce(ua,0)),u}var zs={virtualScrollSliceSize:{type:[Number,String],default:10},virtualScrollSliceRatioBefore:{type:[Number,String],default:1},virtualScrollSliceRatioAfter:{type:[Number,String],default:1},virtualScrollItemSize:{type:[Number,String],default:24},virtualScrollStickySizeStart:{type:[Number,String],default:0},virtualScrollStickySizeEnd:{type:[Number,String],default:0},tableColspan:[Number,String]},Os=Object.keys(zs),Eo={virtualScrollHorizontal:Boolean,onVirtualScroll:Function,...zs};function Rs({virtualScrollLength:e,getVirtualScrollTarget:t,getVirtualScrollEl:n,virtualScrollItemSizeComputed:l}){const a=ve(),{props:o,emit:i,proxy:u}=a,{$q:d}=u;let f,c,h,m=[],v;const g=O(0),_=O(0),p=O({}),k=O(null),w=O(null),y=O(null),b=O({from:0,to:0}),S=s(()=>o.tableColspan!==void 0?o.tableColspan:100);l===void 0&&(l=s(()=>o.virtualScrollItemSize));const B=s(()=>l.value+";"+o.virtualScrollHorizontal),L=s(()=>B.value+";"+o.virtualScrollSliceRatioBefore+";"+o.virtualScrollSliceRatioAfter);ne(L,()=>{W()}),ne(B,R);function R(){U(c,!0)}function A(I){U(I===void 0?c:I)}function P(I,C){const K=t();if(K==null||K.nodeType===8)return;const Q=vo(K,n(),k.value,w.value,o.virtualScrollHorizontal,d.lang.rtl,o.virtualScrollStickySizeStart,o.virtualScrollStickySizeEnd);h!==Q.scrollViewSize&&W(Q.scrollViewSize),T(K,Q,Math.min(e.value-1,Math.max(0,parseInt(I,10)||0)),0,Fv.indexOf(C)!==-1?C:c!==-1&&I>c?"end":"start")}function z(){const I=t();if(I==null||I.nodeType===8)return;const C=vo(I,n(),k.value,w.value,o.virtualScrollHorizontal,d.lang.rtl,o.virtualScrollStickySizeStart,o.virtualScrollStickySizeEnd),K=e.value-1,Q=C.scrollMaxSize-C.offsetStart-C.offsetEnd-_.value;if(f===C.scrollStart)return;if(C.scrollMaxSize<=0){T(I,C,0,0);return}h!==C.scrollViewSize&&W(C.scrollViewSize),x(b.value.from);const oe=Math.floor(C.scrollMaxSize-Math.max(C.scrollViewSize,C.offsetEnd)-Math.min(v[K],C.scrollViewSize/2));if(oe>0&&Math.ceil(C.scrollStart)>=oe){T(I,C,K,C.scrollMaxSize-C.offsetEnd-m.reduce(ua,0));return}let M=0,D=C.scrollStart-C.offsetStart,le=D;if(D<=Q&&D+C.scrollViewSize>=g.value)D-=g.value,M=b.value.from,le=D;else for(let H=0;D>=m[H]&&M<K;H++)D-=m[H],M+=jt;for(;D>0&&M<K;)D-=v[M],D>-C.scrollViewSize?(M++,le=D):le=v[M]+D;T(I,C,M,le)}function T(I,C,K,Q,oe){const M=typeof oe=="string"&&oe.indexOf("-force")!==-1,D=M===!0?oe.replace("-force",""):oe,le=D!==void 0?D:"start";let H=Math.max(0,K-p.value[le]),de=H+p.value.total;de>e.value&&(de=e.value,H=Math.max(0,de-p.value.total)),f=C.scrollStart;const xe=H!==b.value.from||de!==b.value.to;if(xe===!1&&D===void 0){V(K);return}const{activeElement:Y}=document,se=y.value;xe===!0&&se!==null&&se!==Y&&se.contains(Y)===!0&&(se.addEventListener("focusout",q),setTimeout(()=>{se!==null&&se.removeEventListener("focusout",q)})),Vv(se,K-H);const me=D!==void 0?v.slice(H,K).reduce(ua,0):0;if(xe===!0){const we=de>=b.value.from&&H<=b.value.to?b.value.to:de;b.value={from:H,to:we},g.value=wa(m,v,0,H),_.value=wa(m,v,de,e.value),requestAnimationFrame(()=>{b.value.to!==de&&f===C.scrollStart&&(b.value={from:b.value.from,to:de},_.value=wa(m,v,de,e.value))})}requestAnimationFrame(()=>{if(f!==C.scrollStart)return;xe===!0&&x(H);const we=v.slice(H,K).reduce(ua,0),Me=we+C.offsetStart+g.value,ce=Me+v[K];let $e=Me+Q;if(D!==void 0){const ze=we-me,Oe=C.scrollStart+ze;$e=M!==!0&&Oe<Me&&ce<Oe+C.scrollViewSize?Oe:D==="end"?ce-C.scrollViewSize:Me-(D==="start"?0:Math.round((C.scrollViewSize-v[K])/2))}f=$e,ki(I,$e,o.virtualScrollHorizontal,d.lang.rtl),V(K)})}function x(I){const C=y.value;if(C){const K=As.call(C.children,H=>H.classList&&H.classList.contains("q-virtual-scroll--skip")===!1),Q=K.length,oe=o.virtualScrollHorizontal===!0?H=>H.getBoundingClientRect().width:H=>H.offsetHeight;let M=I,D,le;for(let H=0;H<Q;){for(D=oe(K[H]),H++;H<Q&&K[H].classList.contains("q-virtual-scroll--with-prev")===!0;)D+=oe(K[H]),H++;le=D-v[M],le!==0&&(v[M]+=le,m[Math.floor(M/jt)]+=le),M++}}}function q(){y.value!==null&&y.value!==void 0&&y.value.focus()}function U(I,C){const K=1*l.value;(C===!0||Array.isArray(v)===!1)&&(v=[]);const Q=v.length;v.length=e.value;for(let M=e.value-1;M>=Q;M--)v[M]=K;const oe=Math.floor((e.value-1)/jt);m=[];for(let M=0;M<=oe;M++){let D=0;const le=Math.min((M+1)*jt,e.value);for(let H=M*jt;H<le;H++)D+=v[H];m.push(D)}c=-1,f=void 0,g.value=wa(m,v,0,b.value.from),_.value=wa(m,v,b.value.to,e.value),I>=0?(x(b.value.from),Qe(()=>{P(I)})):j()}function W(I){if(I===void 0&&typeof window!="undefined"){const D=t();D!=null&&D.nodeType!==8&&(I=vo(D,n(),k.value,w.value,o.virtualScrollHorizontal,d.lang.rtl,o.virtualScrollStickySizeStart,o.virtualScrollStickySizeEnd).scrollViewSize)}h=I;const C=parseFloat(o.virtualScrollSliceRatioBefore)||0,K=parseFloat(o.virtualScrollSliceRatioAfter)||0,Q=1+C+K,oe=I===void 0||I<=0?1:Math.ceil(I/l.value),M=Math.max(1,oe,Math.ceil((o.virtualScrollSliceSize>0?o.virtualScrollSliceSize:10)/Q));p.value={total:Math.ceil(M*Q),start:Math.ceil(M*C),center:Math.ceil(M*(.5+C)),end:Math.ceil(M*(1+C)),view:oe}}function E(I,C){const K=o.virtualScrollHorizontal===!0?"width":"height",Q={["--q-virtual-scroll-item-"+K]:l.value+"px"};return[I==="tbody"?r(I,{class:"q-virtual-scroll__padding",key:"before",ref:k},[r("tr",[r("td",{style:{[K]:`${g.value}px`,...Q},colspan:S.value})])]):r(I,{class:"q-virtual-scroll__padding",key:"before",ref:k,style:{[K]:`${g.value}px`,...Q}}),r(I,{class:"q-virtual-scroll__content",key:"content",ref:y,tabindex:-1},C.flat()),I==="tbody"?r(I,{class:"q-virtual-scroll__padding",key:"after",ref:w},[r("tr",[r("td",{style:{[K]:`${_.value}px`,...Q},colspan:S.value})])]):r(I,{class:"q-virtual-scroll__padding",key:"after",ref:w,style:{[K]:`${_.value}px`,...Q}})]}function V(I){c!==I&&(o.onVirtualScroll!==void 0&&i("virtualScroll",{index:I,from:b.value.from,to:b.value.to-1,direction:I<c?"decrease":"increase",ref:u}),c=I)}W();const j=fa(z,d.platform.is.ios===!0?120:35);Ro(()=>{W()});let ue=!1;return Yt(()=>{ue=!0}),bn(()=>{if(ue!==!0)return;const I=t();f!==void 0&&I!==void 0&&I!==null&&I.nodeType!==8?ki(I,f,o.virtualScrollHorizontal,d.lang.rtl):P(c)}),Ne(()=>{j.cancel()}),Object.assign(u,{scrollTo:P,reset:R,refresh:A}),{virtualScrollSliceRange:b,virtualScrollSliceSizeComputed:p,setVirtualScrollSize:W,onVirtualScrollEvt:j,localResetVirtualScroll:U,padVirtualScroll:E,scrollTo:P,reset:R,refresh:A}}var qi=e=>["add","add-unique","toggle"].includes(e),Dv=".*+?^${}()|[]\\",Iv=Object.keys(Hl),Hv=te({name:"QSelect",inheritAttrs:!1,props:{...Eo,...Ht,...Hl,modelValue:{required:!0},multiple:Boolean,displayValue:[String,Number],displayValueHtml:Boolean,dropdownIcon:String,options:{type:Array,default:()=>[]},optionValue:[Function,String],optionLabel:[Function,String],optionDisable:[Function,String],hideSelected:Boolean,hideDropdownIcon:Boolean,fillInput:Boolean,maxValues:[Number,String],optionsDense:Boolean,optionsDark:{type:Boolean,default:null},optionsSelectedClass:String,optionsHtml:Boolean,optionsCover:Boolean,menuShrink:Boolean,menuAnchor:String,menuSelf:String,menuOffset:Array,popupContentClass:String,popupContentStyle:[String,Array,Object],popupNoRouteDismiss:Boolean,useInput:Boolean,useChips:Boolean,newValueMode:{type:String,validator:qi},mapOptions:Boolean,emitValue:Boolean,inputDebounce:{type:[Number,String],default:500},inputClass:[Array,String,Object],inputStyle:[Array,String,Object],tabindex:{type:[String,Number],default:0},autocomplete:String,transitionShow:{},transitionHide:{},transitionDuration:{},behavior:{type:String,validator:e=>["default","menu","dialog"].includes(e),default:"default"},virtualScrollItemSize:Eo.virtualScrollItemSize.type,onNewValue:Function,onFilter:Function},emits:[...Nl,"add","remove","inputValue","keyup","keypress","keydown","popupShow","popupHide","filterAbort"],setup(e,{slots:t,emit:n}){const{proxy:l}=ve(),{$q:a}=l,o=O(!1),i=O(!1),u=O(-1),d=O(""),f=O(!1),c=O(!1);let h=null,m=null,v,g,_,p=null,k,w,y,b;const S=O(null),B=O(null),L=O(null),R=O(null),A=O(null),P=Jo(e),z=Ms(ye),T=s(()=>Array.isArray(e.options)?e.options.length:0),x=s(()=>e.virtualScrollItemSize===void 0?e.optionsDense===!0?24:48:e.virtualScrollItemSize),{virtualScrollSliceRange:q,virtualScrollSliceSizeComputed:U,localResetVirtualScroll:W,padVirtualScroll:E,onVirtualScrollEvt:V,scrollTo:j,setVirtualScrollSize:ue}=Rs({virtualScrollLength:T,getVirtualScrollTarget:At,getVirtualScrollEl:Bt,virtualScrollItemSizeComputed:x}),I=Ql(),C=s(()=>{const F=e.mapOptions===!0&&e.multiple!==!0,Se=e.modelValue!==void 0&&(e.modelValue!==null||F===!0)?e.multiple===!0&&Array.isArray(e.modelValue)?e.modelValue:[e.modelValue]:[];if(e.mapOptions===!0&&Array.isArray(e.options)===!0){const pe=e.mapOptions===!0&&v!==void 0?v:[],De=Se.map(rt=>X(rt,pe));return e.modelValue===null&&F===!0?De.filter(rt=>rt!==null):De}return Se}),K=s(()=>{const F={};return Iv.forEach(Se=>{const pe=e[Se];pe!==void 0&&(F[Se]=pe)}),F}),Q=s(()=>e.optionsDark===null?I.isDark.value:e.optionsDark),oe=s(()=>In(C.value)),M=s(()=>{let F="q-field__input q-placeholder col";return e.hideSelected===!0||C.value.length===0?[F,e.inputClass]:(F+=" q-field__input--padding",e.inputClass===void 0?F:[F,e.inputClass])}),D=s(()=>(e.virtualScrollHorizontal===!0?"q-virtual-scroll--horizontal":"")+(e.popupContentClass?" "+e.popupContentClass:"")),le=s(()=>T.value===0),H=s(()=>C.value.map(F=>fe.value(F)).join(", ")),de=s(()=>e.displayValue!==void 0?e.displayValue:H.value),xe=s(()=>e.optionsHtml===!0?()=>!0:F=>F!=null&&F.html===!0),Y=s(()=>e.displayValueHtml===!0||e.displayValue===void 0&&(e.optionsHtml===!0||C.value.some(xe.value))),se=s(()=>I.focused.value===!0?e.tabindex:-1),me=s(()=>{const F={tabindex:e.tabindex,role:"combobox","aria-label":e.label,"aria-readonly":e.readonly===!0?"true":"false","aria-autocomplete":e.useInput===!0?"list":"none","aria-expanded":o.value===!0?"true":"false","aria-controls":`${I.targetUid.value}_lb`};return u.value>=0&&(F["aria-activedescendant"]=`${I.targetUid.value}_${u.value}`),F}),we=s(()=>({id:`${I.targetUid.value}_lb`,role:"listbox","aria-multiselectable":e.multiple===!0?"true":"false"})),Me=s(()=>C.value.map((F,Se)=>({index:Se,opt:F,html:xe.value(F),selected:!0,removeAtIndex:Ge,toggleOption:at,tabindex:se.value}))),ce=s(()=>{if(T.value===0)return[];const{from:F,to:Se}=q.value;return e.options.slice(F,Se).map((pe,De)=>{const rt=G.value(pe)===!0,lt=ke(pe)===!0,Ct=F+De,bt={clickable:!0,active:lt,activeClass:Oe.value,manualFocus:!0,focused:!1,disable:rt,tabindex:-1,dense:e.optionsDense,dark:Q.value,role:"option","aria-selected":lt===!0?"true":"false",id:`${I.targetUid.value}_${Ct}`,onClick:()=>{at(pe)}};return rt!==!0&&(u.value===Ct&&(bt.focused=!0),a.platform.is.desktop===!0&&(bt.onMousemove=()=>{o.value===!0&&J(Ct)})),{index:Ct,opt:pe,html:xe.value(pe),label:fe.value(pe),selected:bt.active,focused:bt.focused,toggleOption:at,setOptionIndex:J,itemProps:bt}})}),$e=s(()=>e.dropdownIcon!==void 0?e.dropdownIcon:a.iconSet.arrow.dropdown),ze=s(()=>e.optionsCover===!1&&e.outlined!==!0&&e.standout!==!0&&e.borderless!==!0&&e.rounded!==!0),Oe=s(()=>e.optionsSelectedClass!==void 0?e.optionsSelectedClass:e.color!==void 0?`text-${e.color}`:""),re=s(()=>ae(e.optionValue,"value")),fe=s(()=>ae(e.optionLabel,"label")),G=s(()=>ae(e.optionDisable,"disable")),be=s(()=>C.value.map(F=>re.value(F))),Be=s(()=>{const F={onInput:ye,onChange:z,onKeydown:ut,onKeyup:He,onKeypress:ot,onFocus:Re,onClick(Se){g===!0&&dt(Se)}};return F.onCompositionstart=F.onCompositionupdate=F.onCompositionend=z,F});ne(C,F=>{v=F,e.useInput===!0&&e.fillInput===!0&&e.multiple!==!0&&I.innerLoading.value!==!0&&(i.value!==!0&&o.value!==!0||oe.value!==!0)&&(_!==!0&&jn(),(i.value===!0||o.value===!0)&&Ve(""))},{immediate:!0}),ne(()=>e.fillInput,jn),ne(o,Kl),ne(T,bc);function Ee(F){return e.emitValue===!0?re.value(F):F}function Pe(F){if(F!==-1&&F<C.value.length)if(e.multiple===!0){const Se=e.modelValue.slice();n("remove",{index:F,value:Se.splice(F,1)[0]}),n("update:modelValue",Se)}else n("update:modelValue",null)}function Ge(F){Pe(F),I.focus()}function nt(F,Se){const pe=Ee(F);if(e.multiple!==!0){e.fillInput===!0&&Le(fe.value(F),!0,!0),n("update:modelValue",pe);return}if(C.value.length===0){n("add",{index:0,value:pe}),n("update:modelValue",e.multiple===!0?[pe]:pe);return}if(Se===!0&&ke(F)===!0||e.maxValues!==void 0&&e.modelValue.length>=e.maxValues)return;const De=e.modelValue.slice();n("add",{index:De.length,value:pe}),De.push(pe),n("update:modelValue",De)}function at(F,Se){if(I.editable.value!==!0||F===void 0||G.value(F)===!0)return;const pe=re.value(F);if(e.multiple!==!0){Se!==!0&&(Le(e.fillInput===!0?fe.value(F):"",!0,!0),_n()),B.value!==null&&B.value.focus(),(C.value.length===0||Rt(re.value(C.value[0]),pe)!==!0)&&n("update:modelValue",e.emitValue===!0?pe:F);return}if((g!==!0||f.value===!0)&&I.focus(),Re(),C.value.length===0){const lt=e.emitValue===!0?pe:F;n("add",{index:0,value:lt}),n("update:modelValue",e.multiple===!0?[lt]:lt);return}const De=e.modelValue.slice(),rt=be.value.findIndex(lt=>Rt(lt,pe));if(rt!==-1)n("remove",{index:rt,value:De.splice(rt,1)[0]});else{if(e.maxValues!==void 0&&De.length>=e.maxValues)return;const lt=e.emitValue===!0?pe:F;n("add",{index:De.length,value:lt}),De.push(lt)}n("update:modelValue",De)}function J(F){if(a.platform.is.desktop!==!0)return;const Se=F!==-1&&F<T.value?F:-1;u.value!==Se&&(u.value=Se)}function ie(F=1,Se){if(o.value===!0){let pe=u.value;do pe=Aa(pe+F,-1,T.value-1);while(pe!==-1&&pe!==u.value&&G.value(e.options[pe])===!0);u.value!==pe&&(J(pe),j(pe),Se!==!0&&e.useInput===!0&&e.fillInput===!0&&qe(pe>=0?fe.value(e.options[pe]):k,!0))}}function X(F,Se){const pe=De=>Rt(re.value(De),F);return e.options.find(pe)||Se.find(pe)||F}function ae(F,Se){const pe=F!==void 0?F:Se;return typeof pe=="function"?pe:De=>De!==null&&typeof De=="object"&&pe in De?De[pe]:De}function ke(F){const Se=re.value(F);return be.value.find(pe=>Rt(pe,Se))!==void 0}function Re(F){e.useInput===!0&&B.value!==null&&(F===void 0||B.value===F.target&&F.target.value===H.value)&&B.value.select()}function _e(F){Vt(F,27)===!0&&o.value===!0&&(dt(F),_n(),jn()),n("keyup",F)}function He(F){const{value:Se}=F.target;if(F.keyCode!==void 0){_e(F);return}if(F.target.value="",h!==null&&(clearTimeout(h),h=null),m!==null&&(clearTimeout(m),m=null),jn(),typeof Se=="string"&&Se.length!==0){const pe=Se.toLocaleLowerCase(),De=lt=>{const Ct=e.options.find(bt=>lt.value(bt).toLocaleLowerCase()===pe);return Ct===void 0?!1:(C.value.indexOf(Ct)===-1?at(Ct):_n(),!0)},rt=lt=>{De(re)!==!0&&(De(fe)===!0||lt===!0||Ve(Se,!0,()=>rt(!0)))};rt()}else I.clearValue(F)}function ot(F){n("keypress",F)}function ut(F){if(n("keydown",F),Hn(F)===!0)return;const Se=d.value.length!==0&&(e.newValueMode!==void 0||e.onNewValue!==void 0),pe=F.shiftKey!==!0&&e.multiple!==!0&&(u.value!==-1||Se===!0);if(F.keyCode===27){wt(F);return}if(F.keyCode===9&&pe===!1){Ze();return}if(F.target===void 0||F.target.id!==I.targetUid.value||I.editable.value!==!0)return;if(F.keyCode===40&&I.innerLoading.value!==!0&&o.value===!1){Fe(F),zt();return}if(F.keyCode===8&&(e.useChips===!0||e.clearable===!0)&&e.hideSelected!==!0&&d.value.length===0){e.multiple===!0&&Array.isArray(e.modelValue)===!0?Pe(e.modelValue.length-1):e.multiple!==!0&&e.modelValue!==null&&n("update:modelValue",null);return}(F.keyCode===35||F.keyCode===36)&&(typeof d.value!="string"||d.value.length===0)&&(Fe(F),u.value=-1,ie(F.keyCode===36?1:-1,e.multiple)),(F.keyCode===33||F.keyCode===34)&&U.value!==void 0&&(Fe(F),u.value=Math.max(-1,Math.min(T.value,u.value+(F.keyCode===33?-1:1)*U.value.view)),ie(F.keyCode===33?1:-1,e.multiple)),(F.keyCode===38||F.keyCode===40)&&(Fe(F),ie(F.keyCode===38?-1:1,e.multiple));const De=T.value;if((y===void 0||b<Date.now())&&(y=""),De>0&&e.useInput!==!0&&F.key!==void 0&&F.key.length===1&&F.altKey===!1&&F.ctrlKey===!1&&F.metaKey===!1&&(F.keyCode!==32||y.length!==0)){o.value!==!0&&zt(F);const rt=F.key.toLocaleLowerCase(),lt=y.length===1&&y[0]===rt;b=Date.now()+1500,lt===!1&&(Fe(F),y+=rt);const Ct=new RegExp("^"+y.split("").map(Wl=>Dv.indexOf(Wl)!==-1?"\\"+Wl:Wl).join(".*"),"i");let bt=u.value;if(lt===!0||bt<0||Ct.test(fe.value(e.options[bt]))!==!0)do bt=Aa(bt+1,-1,De-1);while(bt!==u.value&&(G.value(e.options[bt])===!0||Ct.test(fe.value(e.options[bt]))!==!0));u.value!==bt&&Qe(()=>{J(bt),j(bt),bt>=0&&e.useInput===!0&&e.fillInput===!0&&qe(fe.value(e.options[bt]),!0)});return}if(!(F.keyCode!==13&&(F.keyCode!==32||e.useInput===!0||y!=="")&&(F.keyCode!==9||pe===!1))){if(F.keyCode!==9&&Fe(F),u.value!==-1&&u.value<De){at(e.options[u.value]);return}if(Se===!0){const rt=(lt,Ct)=>{if(Ct){if(qi(Ct)!==!0)return}else Ct=e.newValueMode;if(Le("",e.multiple!==!0,!0),lt==null)return;(Ct==="toggle"?at:nt)(lt,Ct==="add-unique"),e.multiple!==!0&&(B.value!==null&&B.value.focus(),_n())};if(e.onNewValue!==void 0?n("newValue",d.value,rt):rt(d.value),e.multiple!==!0)return}o.value===!0?Ze():I.innerLoading.value!==!0&&zt()}}function Bt(){return g===!0?A.value:L.value!==null&&L.value.contentEl!==null?L.value.contentEl:void 0}function At(){return Bt()}function en(){return e.hideSelected===!0?[]:t["selected-item"]!==void 0?Me.value.map(F=>t["selected-item"](F)).slice():t.selected!==void 0?[].concat(t.selected()):e.useChips===!0?Me.value.map((F,Se)=>r(ju,{key:"option-"+Se,removable:I.editable.value===!0&&G.value(F.opt)!==!0,dense:!0,textColor:e.color,tabindex:se.value,onRemove(){F.removeAtIndex(Se)}},()=>r("span",{class:"ellipsis",[F.html===!0?"innerHTML":"textContent"]:fe.value(F.opt)}))):[r("span",{[Y.value===!0?"innerHTML":"textContent"]:de.value})]}function Zt(){if(le.value===!0)return t["no-option"]!==void 0?t["no-option"]({inputValue:d.value}):void 0;const F=t.option!==void 0?t.option:pe=>r(Il,{key:pe.index,...pe.itemProps},()=>r(gn,()=>r($o,()=>r("span",{[pe.html===!0?"innerHTML":"textContent"]:pe.label}))));let Se=E("div",ce.value.map(F));return t["before-options"]!==void 0&&(Se=t["before-options"]().concat(Se)),mt(t["after-options"],Se)}function ee(F,Se){const pe=Se===!0?{...me.value,...I.splitAttrs.attributes.value}:void 0,De={ref:Se===!0?B:void 0,key:"i_t",class:M.value,style:e.inputStyle,value:d.value!==void 0?d.value:"",type:"search",...pe,id:Se===!0?I.targetUid.value:void 0,maxlength:e.maxlength,autocomplete:e.autocomplete,"data-autofocus":F===!0||e.autofocus===!0||void 0,disabled:e.disable===!0,readonly:e.readonly===!0,...Be.value};return F!==!0&&g===!0&&(Array.isArray(De.class)===!0?De.class=[...De.class,"no-pointer-events"]:De.class+=" no-pointer-events"),r("input",De)}function ye(F){h!==null&&(clearTimeout(h),h=null),m!==null&&(clearTimeout(m),m=null),!(F&&F.target&&F.target.qComposing===!0)&&(qe(F.target.value||""),_=!0,k=d.value,I.focused.value!==!0&&(g!==!0||f.value===!0)&&I.focus(),e.onFilter!==void 0&&(h=setTimeout(()=>{h=null,Ve(d.value)},e.inputDebounce)))}function qe(F,Se){d.value!==F&&(d.value=F,Se===!0||e.inputDebounce===0||e.inputDebounce==="0"?n("inputValue",F):m=setTimeout(()=>{m=null,n("inputValue",F)},e.inputDebounce))}function Le(F,Se,pe){_=pe!==!0,e.useInput===!0&&(qe(F,!0),(Se===!0||pe!==!0)&&(k=F),Se!==!0&&Ve(F))}function Ve(F,Se,pe){if(e.onFilter===void 0||Se!==!0&&I.focused.value!==!0)return;I.innerLoading.value===!0?n("filterAbort"):(I.innerLoading.value=!0,c.value=!0),F!==""&&e.multiple!==!0&&C.value.length!==0&&_!==!0&&F===fe.value(C.value[0])&&(F="");const De=setTimeout(()=>{o.value===!0&&(o.value=!1)},10);p!==null&&clearTimeout(p),p=De,n("filter",F,(rt,lt)=>{(Se===!0||I.focused.value===!0)&&p===De&&(clearTimeout(p),typeof rt=="function"&&rt(),c.value=!1,Qe(()=>{I.innerLoading.value=!1,I.editable.value===!0&&(Se===!0?o.value===!0&&_n():o.value===!0?Kl(!0):o.value=!0),typeof lt=="function"&&Qe(()=>{lt(l)}),typeof pe=="function"&&Qe(()=>{pe(l)})}))},()=>{I.focused.value===!0&&p===De&&(clearTimeout(p),I.innerLoading.value=!1,c.value=!1),o.value===!0&&(o.value=!1)})}function st(){return r(Fl,{ref:L,class:D.value,style:e.popupContentStyle,modelValue:o.value,fit:e.menuShrink!==!0,cover:e.optionsCover===!0&&le.value!==!0&&e.useInput!==!0,anchor:e.menuAnchor,self:e.menuSelf,offset:e.menuOffset,dark:Q.value,noParentEvent:!0,noRefocus:!0,noFocus:!0,noRouteDismiss:e.popupNoRouteDismiss,square:ze.value,transitionShow:e.transitionShow,transitionHide:e.transitionHide,transitionDuration:e.transitionDuration,separateClosePopup:!0,...we.value,onScrollPassive:V,onBeforeShow:xr,onBeforeHide:$,onShow:N},Zt)}function $(F){Cr(F),Ze()}function N(){ue()}function Z(F){dt(F),B.value!==null&&B.value.focus(),f.value=!0,window.scrollTo(window.pageXOffset||window.scrollX||document.body.scrollLeft||0,0)}function ge(F){dt(F),Qe(()=>{f.value=!1})}function he(){const F=[r(hv,{class:`col-auto ${I.fieldClass.value}`,...K.value,for:I.targetUid.value,dark:Q.value,square:!0,loading:c.value,itemAligned:!1,filled:!0,stackLabel:d.value.length!==0,...I.splitAttrs.listeners.value,onFocus:Z,onBlur:ge},{...t,rawControl:()=>I.getControl(!0),before:void 0,after:void 0})];return o.value===!0&&F.push(r("div",{ref:A,class:D.value+" scroll",style:e.popupContentStyle,...we.value,onClick:wt,onScrollPassive:V},Zt())),r(Dl,{ref:R,modelValue:i.value,position:e.useInput===!0?"top":void 0,transitionShow:w,transitionHide:e.transitionHide,transitionDuration:e.transitionDuration,noRouteDismiss:e.popupNoRouteDismiss,onBeforeShow:xr,onBeforeHide:Ue,onHide:Te,onShow:Ie},()=>r("div",{class:"q-select__dialog"+(Q.value===!0?" q-select__dialog--dark q-dark":"")+(f.value===!0?" q-select__dialog--focused":"")},F))}function Ue(F){Cr(F),R.value!==null&&R.value.__updateRefocusTarget(I.rootRef.value.querySelector(".q-field__native > [tabindex]:last-child")),I.focused.value=!1}function Te(F){_n(),I.focused.value===!1&&n("blur",F),jn()}function Ie(){const F=document.activeElement;(F===null||F.id!==I.targetUid.value)&&B.value!==null&&B.value!==F&&B.value.focus(),ue()}function Ze(){i.value!==!0&&(u.value=-1,o.value===!0&&(o.value=!1),I.focused.value===!1&&(p!==null&&(clearTimeout(p),p=null),I.innerLoading.value===!0&&(n("filterAbort"),I.innerLoading.value=!1,c.value=!1)))}function zt(F){I.editable.value===!0&&(g===!0?(I.onControlFocusin(F),i.value=!0,Qe(()=>{I.focus()})):I.focus(),e.onFilter!==void 0?Ve(d.value):(le.value!==!0||t["no-option"]!==void 0)&&(o.value=!0))}function _n(){i.value=!1,Ze()}function jn(){e.useInput===!0&&Le(e.multiple!==!0&&e.fillInput===!0&&C.value.length!==0&&fe.value(C.value[0])||"",!0,!0)}function Kl(F){let Se=-1;if(F===!0){if(C.value.length!==0){const pe=re.value(C.value[0]);Se=e.options.findIndex(De=>Rt(re.value(De),pe))}W(Se)}J(Se)}function bc(F,Se){o.value===!0&&I.innerLoading.value===!1&&(W(-1,!0),Qe(()=>{o.value===!0&&I.innerLoading.value===!1&&(F>Se?W():Kl(!0))}))}function Sr(){i.value===!1&&L.value!==null&&L.value.updatePosition()}function xr(F){F!==void 0&&dt(F),n("popupShow",F),I.hasPopupOpen=!0,I.onControlFocusin(F)}function Cr(F){F!==void 0&&dt(F),n("popupHide",F),I.hasPopupOpen=!1,I.onControlFocusout(F)}function kr(){g=a.platform.is.mobile!==!0&&e.behavior!=="dialog"?!1:e.behavior!=="menu"&&(e.useInput===!0?t["no-option"]!==void 0||e.onFilter!==void 0||le.value===!1:!0),w=a.platform.is.ios===!0&&g===!0&&e.useInput===!0?"fade":e.transitionShow}return Da(kr),pc(Sr),kr(),Ne(()=>{h!==null&&clearTimeout(h),m!==null&&clearTimeout(m)}),Object.assign(l,{showPopup:zt,hidePopup:_n,removeAtIndex:Pe,add:nt,toggleOption:at,getOptionIndex:()=>u.value,setOptionIndex:J,moveOptionSelection:ie,filter:Ve,updateMenuPosition:Sr,updateInputValue:Le,isOptionSelected:ke,getEmittingOptionValue:Ee,isOptionDisabled:(...F)=>G.value.apply(null,F)===!0,getOptionValue:(...F)=>re.value.apply(null,F),getOptionLabel:(...F)=>fe.value.apply(null,F)}),Object.assign(I,{innerValue:C,fieldClass:s(()=>`q-select q-field--auto-height q-select--with${e.useInput!==!0?"out":""}-input q-select--with${e.useChips!==!0?"out":""}-chips q-select--${e.multiple===!0?"multiple":"single"}`),inputRef:S,targetRef:B,hasValue:oe,showPopup:zt,floatingLabel:s(()=>e.hideSelected!==!0&&oe.value===!0||typeof d.value=="number"||d.value.length!==0||In(e.displayValue)),getControlChild:()=>{if(I.editable.value!==!1&&(i.value===!0||le.value!==!0||t["no-option"]!==void 0))return g===!0?he():st();I.hasPopupOpen===!0&&(I.hasPopupOpen=!1)},controlEvents:{onFocusin(F){I.onControlFocusin(F)},onFocusout(F){I.onControlFocusout(F,()=>{jn(),Ze()})},onClick(F){if(wt(F),g!==!0&&o.value===!0){Ze(),B.value!==null&&B.value.focus();return}zt(F)}},getControl:F=>{const Se=en(),pe=F===!0||i.value!==!0||g!==!0;if(e.useInput===!0)Se.push(ee(F,pe));else if(I.editable.value===!0){const rt=pe===!0?me.value:void 0;Se.push(r("input",{ref:pe===!0?B:void 0,key:"d_t",class:"q-select__focus-target",id:pe===!0?I.targetUid.value:void 0,value:de.value,readonly:!0,"data-autofocus":F===!0||e.autofocus===!0||void 0,...rt,onKeydown:ut,onKeyup:_e,onKeypress:ot})),pe===!0&&typeof e.autocomplete=="string"&&e.autocomplete.length!==0&&Se.push(r("input",{class:"q-select__autocomplete-input",autocomplete:e.autocomplete,tabindex:-1,onKeyup:He}))}if(P.value!==void 0&&e.disable!==!0&&be.value.length!==0){const rt=be.value.map(lt=>r("option",{value:lt,selected:!0}));Se.push(r("select",{class:"hidden",name:P.value,multiple:e.multiple},rt))}const De=e.useInput===!0||pe!==!0?void 0:I.splitAttrs.attributes.value;return r("div",{class:"q-field__native row items-center",...De,...I.splitAttrs.listeners.value},Se)},getInnerAppend:()=>e.loading!==!0&&c.value!==!0&&e.hideDropdownIcon!==!0?[r(je,{class:"q-select__dropdown-icon"+(o.value===!0?" rotate-180":""),name:$e.value})]:null}),jl(I)}}),Nv=["text","rect","circle","QBtn","QBadge","QChip","QToolbar","QCheckbox","QRadio","QToggle","QSlider","QRange","QInput","QAvatar"],Qv=["wave","pulse","pulse-x","pulse-y","fade","blink","none"],Vh=te({name:"QSkeleton",props:{...Ke,tag:{type:String,default:"div"},type:{type:String,validator:e=>Nv.includes(e),default:"rect"},animation:{type:String,validator:e=>Qv.includes(e),default:"wave"},animationSpeed:{type:[String,Number],default:1500},square:Boolean,bordered:Boolean,size:String,width:String,height:String},setup(e,{slots:t}){const n=ve(),l=We(e,n.proxy.$q),a=s(()=>{const i=e.size!==void 0?[e.size,e.size]:[e.width,e.height];return{"--q-skeleton-speed":`${e.animationSpeed}ms`,width:i[0],height:i[1]}}),o=s(()=>`q-skeleton q-skeleton--${l.value===!0?"dark":"light"} q-skeleton--type-${e.type}`+(e.animation!=="none"?` q-skeleton--anim q-skeleton--anim-${e.animation}`:"")+(e.square===!0?" q-skeleton--square":"")+(e.bordered===!0?" q-skeleton--bordered":""));return()=>r(e.tag,{class:o.value,style:a.value},Ce(t.default))}}),Ti=[["left","center","start","width"],["right","center","end","width"],["top","start","center","height"],["bottom","end","center","height"]],Dh=te({name:"QSlideItem",props:{...Ke,leftColor:String,rightColor:String,topColor:String,bottomColor:String,onSlide:Function},emits:["action","top","right","bottom","left"],setup(e,{slots:t,emit:n}){const{proxy:l}=ve(),{$q:a}=l,o=We(e,a),{getCache:i}=Wa(),u=O(null);let d=null,f={},c={},h={};const m=s(()=>a.lang.rtl===!0?{left:"right",right:"left"}:{left:"left",right:"right"}),v=s(()=>"q-slide-item q-item-type overflow-hidden"+(o.value===!0?" q-slide-item--dark q-dark":""));function g(){u.value.style.transform="translate(0,0)"}function _(k,w,y){e.onSlide!==void 0&&n("slide",{side:k,ratio:w,isReset:y})}function p(k){const w=u.value;if(k.isFirst)f={dir:null,size:{left:0,right:0,top:0,bottom:0},scale:0},w.classList.add("no-transition"),Ti.forEach(B=>{if(t[B[0]]!==void 0){const L=h[B[0]];L.style.transform="scale(1)",f.size[B[0]]=L.getBoundingClientRect()[B[3]]}}),f.axis=k.direction==="up"||k.direction==="down"?"Y":"X";else if(k.isFinal){w.classList.remove("no-transition"),f.scale===1?(w.style.transform=`translate${f.axis}(${f.dir*100}%)`,d!==null&&clearTimeout(d),d=setTimeout(()=>{d=null,n(f.showing,{reset:g}),n("action",{side:f.showing,reset:g})},230)):(w.style.transform="translate(0,0)",_(f.showing,0,!0));return}else k.direction=f.axis==="X"?k.offset.x<0?"left":"right":k.offset.y<0?"up":"down";if(t.left===void 0&&k.direction===m.value.right||t.right===void 0&&k.direction===m.value.left||t.top===void 0&&k.direction==="down"||t.bottom===void 0&&k.direction==="up"){w.style.transform="translate(0,0)";return}let y,b,S;f.axis==="X"?(b=k.direction==="left"?-1:1,y=b===1?m.value.left:m.value.right,S=k.distance.x):(b=k.direction==="up"?-2:2,y=b===2?"top":"bottom",S=k.distance.y),!(f.dir!==null&&Math.abs(b)!==Math.abs(f.dir))&&(f.dir!==b&&(["left","right","top","bottom"].forEach(B=>{c[B]&&(c[B].style.visibility=y===B?"visible":"hidden")}),f.showing=y,f.dir=b),f.scale=Math.max(0,Math.min(1,(S-40)/f.size[y])),w.style.transform=`translate${f.axis}(${S*b/Math.abs(b)}px)`,h[y].style.transform=`scale(${f.scale})`,_(y,f.scale,!1))}return Da(()=>{c={},h={}}),Ne(()=>{d!==null&&clearTimeout(d)}),Object.assign(l,{reset:g}),()=>{const k=[],w={left:t[m.value.right]!==void 0,right:t[m.value.left]!==void 0,up:t.bottom!==void 0,down:t.top!==void 0},y=Object.keys(w).filter(S=>w[S]===!0);Ti.forEach(S=>{const B=S[0];t[B]!==void 0&&k.push(r("div",{key:B,ref:L=>{c[B]=L},class:`q-slide-item__${B} absolute-full row no-wrap items-${S[1]} justify-${S[2]}`+(e[B+"Color"]!==void 0?` bg-${e[B+"Color"]}`:"")},[r("div",{ref:L=>{h[B]=L}},t[B]())]))});const b=r("div",{key:`${y.length===0?"only-":""} content`,ref:u,class:"q-slide-item__content"},Ce(t.default));return y.length===0?k.push(b):k.push(Ut(b,i("dir#"+y.join(""),()=>{const S={prevent:!0,stop:!0,mouse:!0};return y.forEach(B=>{S[B]=!0}),[[Ft,p,void 0,S]]}))),r("div",{class:v.value},k)}}}),jv=r("div",{class:"q-space"}),Ih=te({name:"QSpace",setup(){return()=>jv}}),Kv=[r("g",{transform:"matrix(1 0 0 -1 0 80)"},[r("rect",{width:"10",height:"20",rx:"3"},[r("animate",{attributeName:"height",begin:"0s",dur:"4.3s",values:"20;45;57;80;64;32;66;45;64;23;66;13;64;56;34;34;2;23;76;79;20",calcMode:"linear",repeatCount:"indefinite"})]),r("rect",{x:"15",width:"10",height:"80",rx:"3"},[r("animate",{attributeName:"height",begin:"0s",dur:"2s",values:"80;55;33;5;75;23;73;33;12;14;60;80",calcMode:"linear",repeatCount:"indefinite"})]),r("rect",{x:"30",width:"10",height:"50",rx:"3"},[r("animate",{attributeName:"height",begin:"0s",dur:"1.4s",values:"50;34;78;23;56;23;34;76;80;54;21;50",calcMode:"linear",repeatCount:"indefinite"})]),r("rect",{x:"45",width:"10",height:"30",rx:"3"},[r("animate",{attributeName:"height",begin:"0s",dur:"2s",values:"30;45;13;80;56;72;45;76;34;23;67;30",calcMode:"linear",repeatCount:"indefinite"})])])],Hh=te({name:"QSpinnerAudio",props:gt,setup(e){const{cSize:t,classes:n}=ht(e);return()=>r("svg",{class:n.value,fill:"currentColor",width:t.value,height:t.value,viewBox:"0 0 55 80",xmlns:"http://www.w3.org/2000/svg"},Kv)}}),Wv=[r("g",{transform:"translate(1 1)","stroke-width":"2",fill:"none","fill-rule":"evenodd"},[r("circle",{cx:"5",cy:"50",r:"5"},[r("animate",{attributeName:"cy",begin:"0s",dur:"2.2s",values:"50;5;50;50",calcMode:"linear",repeatCount:"indefinite"}),r("animate",{attributeName:"cx",begin:"0s",dur:"2.2s",values:"5;27;49;5",calcMode:"linear",repeatCount:"indefinite"})]),r("circle",{cx:"27",cy:"5",r:"5"},[r("animate",{attributeName:"cy",begin:"0s",dur:"2.2s",from:"5",to:"5",values:"5;50;50;5",calcMode:"linear",repeatCount:"indefinite"}),r("animate",{attributeName:"cx",begin:"0s",dur:"2.2s",from:"27",to:"27",values:"27;49;5;27",calcMode:"linear",repeatCount:"indefinite"})]),r("circle",{cx:"49",cy:"50",r:"5"},[r("animate",{attributeName:"cy",begin:"0s",dur:"2.2s",values:"50;50;5;50",calcMode:"linear",repeatCount:"indefinite"}),r("animate",{attributeName:"cx",from:"49",to:"49",begin:"0s",dur:"2.2s",values:"49;5;27;49",calcMode:"linear",repeatCount:"indefinite"})])])],Nh=te({name:"QSpinnerBall",props:gt,setup(e){const{cSize:t,classes:n}=ht(e);return()=>r("svg",{class:n.value,stroke:"currentColor",width:t.value,height:t.value,viewBox:"0 0 57 57",xmlns:"http://www.w3.org/2000/svg"},Wv)}}),Uv=[r("rect",{y:"10",width:"15",height:"120",rx:"6"},[r("animate",{attributeName:"height",begin:"0.5s",dur:"1s",values:"120;110;100;90;80;70;60;50;40;140;120",calcMode:"linear",repeatCount:"indefinite"}),r("animate",{attributeName:"y",begin:"0.5s",dur:"1s",values:"10;15;20;25;30;35;40;45;50;0;10",calcMode:"linear",repeatCount:"indefinite"})]),r("rect",{x:"30",y:"10",width:"15",height:"120",rx:"6"},[r("animate",{attributeName:"height",begin:"0.25s",dur:"1s",values:"120;110;100;90;80;70;60;50;40;140;120",calcMode:"linear",repeatCount:"indefinite"}),r("animate",{attributeName:"y",begin:"0.25s",dur:"1s",values:"10;15;20;25;30;35;40;45;50;0;10",calcMode:"linear",repeatCount:"indefinite"})]),r("rect",{x:"60",width:"15",height:"140",rx:"6"},[r("animate",{attributeName:"height",begin:"0s",dur:"1s",values:"120;110;100;90;80;70;60;50;40;140;120",calcMode:"linear",repeatCount:"indefinite"}),r("animate",{attributeName:"y",begin:"0s",dur:"1s",values:"10;15;20;25;30;35;40;45;50;0;10",calcMode:"linear",repeatCount:"indefinite"})]),r("rect",{x:"90",y:"10",width:"15",height:"120",rx:"6"},[r("animate",{attributeName:"height",begin:"0.25s",dur:"1s",values:"120;110;100;90;80;70;60;50;40;140;120",calcMode:"linear",repeatCount:"indefinite"}),r("animate",{attributeName:"y",begin:"0.25s",dur:"1s",values:"10;15;20;25;30;35;40;45;50;0;10",calcMode:"linear",repeatCount:"indefinite"})]),r("rect",{x:"120",y:"10",width:"15",height:"120",rx:"6"},[r("animate",{attributeName:"height",begin:"0.5s",dur:"1s",values:"120;110;100;90;80;70;60;50;40;140;120",calcMode:"linear",repeatCount:"indefinite"}),r("animate",{attributeName:"y",begin:"0.5s",dur:"1s",values:"10;15;20;25;30;35;40;45;50;0;10",calcMode:"linear",repeatCount:"indefinite"})])],Qh=te({name:"QSpinnerBars",props:gt,setup(e){const{cSize:t,classes:n}=ht(e);return()=>r("svg",{class:n.value,fill:"currentColor",width:t.value,height:t.value,viewBox:"0 0 135 140",xmlns:"http://www.w3.org/2000/svg"},Uv)}}),Yv=[r("rect",{x:"25",y:"25",width:"50",height:"50",fill:"none","stroke-width":"4",stroke:"currentColor"},[r("animateTransform",{id:"spinnerBox",attributeName:"transform",type:"rotate",from:"0 50 50",to:"180 50 50",dur:"0.5s",begin:"rectBox.end"})]),r("rect",{x:"27",y:"27",width:"46",height:"50",fill:"currentColor"},[r("animate",{id:"rectBox",attributeName:"height",begin:"0s;spinnerBox.end",dur:"1.3s",from:"50",to:"0",fill:"freeze"})])],jh=te({name:"QSpinnerBox",props:gt,setup(e){const{cSize:t,classes:n}=ht(e);return()=>r("svg",{class:n.value,width:t.value,height:t.value,viewBox:"0 0 100 100",preserveAspectRatio:"xMidYMid",xmlns:"http://www.w3.org/2000/svg"},Yv)}}),Xv=[r("circle",{cx:"50",cy:"50",r:"48",fill:"none","stroke-width":"4","stroke-miterlimit":"10",stroke:"currentColor"}),r("line",{"stroke-linecap":"round","stroke-width":"4","stroke-miterlimit":"10",stroke:"currentColor",x1:"50",y1:"50",x2:"85",y2:"50.5"},[r("animateTransform",{attributeName:"transform",type:"rotate",from:"0 50 50",to:"360 50 50",dur:"2s",repeatCount:"indefinite"})]),r("line",{"stroke-linecap":"round","stroke-width":"4","stroke-miterlimit":"10",stroke:"currentColor",x1:"50",y1:"50",x2:"49.5",y2:"74"},[r("animateTransform",{attributeName:"transform",type:"rotate",from:"0 50 50",to:"360 50 50",dur:"15s",repeatCount:"indefinite"})])],Kh=te({name:"QSpinnerClock",props:gt,setup(e){const{cSize:t,classes:n}=ht(e);return()=>r("svg",{class:n.value,width:t.value,height:t.value,viewBox:"0 0 100 100",preserveAspectRatio:"xMidYMid",xmlns:"http://www.w3.org/2000/svg"},Xv)}}),Gv=[r("rect",{x:"0",y:"0",width:"100",height:"100",fill:"none"}),r("path",{d:"M78,19H22c-6.6,0-12,5.4-12,12v31c0,6.6,5.4,12,12,12h37.2c0.4,3,1.8,5.6,3.7,7.6c2.4,2.5,5.1,4.1,9.1,4 c-1.4-2.1-2-7.2-2-10.3c0-0.4,0-0.8,0-1.3h8c6.6,0,12-5.4,12-12V31C90,24.4,84.6,19,78,19z",fill:"currentColor"}),r("circle",{cx:"30",cy:"47",r:"5",fill:"#fff"},[r("animate",{attributeName:"opacity",from:"0",to:"1",values:"0;1;1",keyTimes:"0;0.2;1",dur:"1s",repeatCount:"indefinite"})]),r("circle",{cx:"50",cy:"47",r:"5",fill:"#fff"},[r("animate",{attributeName:"opacity",from:"0",to:"1",values:"0;0;1;1",keyTimes:"0;0.2;0.4;1",dur:"1s",repeatCount:"indefinite"})]),r("circle",{cx:"70",cy:"47",r:"5",fill:"#fff"},[r("animate",{attributeName:"opacity",from:"0",to:"1",values:"0;0;1;1",keyTimes:"0;0.4;0.6;1",dur:"1s",repeatCount:"indefinite"})])],Wh=te({name:"QSpinnerComment",props:gt,setup(e){const{cSize:t,classes:n}=ht(e);return()=>r("svg",{class:n.value,width:t.value,height:t.value,xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 100 100",preserveAspectRatio:"xMidYMid"},Gv)}}),Zv=[r("rect",{x:"0",y:"0",width:"100",height:"100",fill:"none"}),r("g",{transform:"translate(25 25)"},[r("rect",{x:"-20",y:"-20",width:"40",height:"40",fill:"currentColor",opacity:"0.9"},[r("animateTransform",{attributeName:"transform",type:"scale",from:"1.5",to:"1",repeatCount:"indefinite",begin:"0s",dur:"1s",calcMode:"spline",keySplines:"0.2 0.8 0.2 0.8",keyTimes:"0;1"})])]),r("g",{transform:"translate(75 25)"},[r("rect",{x:"-20",y:"-20",width:"40",height:"40",fill:"currentColor",opacity:"0.8"},[r("animateTransform",{attributeName:"transform",type:"scale",from:"1.5",to:"1",repeatCount:"indefinite",begin:"0.1s",dur:"1s",calcMode:"spline",keySplines:"0.2 0.8 0.2 0.8",keyTimes:"0;1"})])]),r("g",{transform:"translate(25 75)"},[r("rect",{x:"-20",y:"-20",width:"40",height:"40",fill:"currentColor",opacity:"0.7"},[r("animateTransform",{attributeName:"transform",type:"scale",from:"1.5",to:"1",repeatCount:"indefinite",begin:"0.3s",dur:"1s",calcMode:"spline",keySplines:"0.2 0.8 0.2 0.8",keyTimes:"0;1"})])]),r("g",{transform:"translate(75 75)"},[r("rect",{x:"-20",y:"-20",width:"40",height:"40",fill:"currentColor",opacity:"0.6"},[r("animateTransform",{attributeName:"transform",type:"scale",from:"1.5",to:"1",repeatCount:"indefinite",begin:"0.2s",dur:"1s",calcMode:"spline",keySplines:"0.2 0.8 0.2 0.8",keyTimes:"0;1"})])])],Uh=te({name:"QSpinnerCube",props:gt,setup(e){const{cSize:t,classes:n}=ht(e);return()=>r("svg",{class:n.value,width:t.value,height:t.value,xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 100 100",preserveAspectRatio:"xMidYMid"},Zv)}}),Jv=[r("circle",{cx:"15",cy:"15",r:"15"},[r("animate",{attributeName:"r",from:"15",to:"15",begin:"0s",dur:"0.8s",values:"15;9;15",calcMode:"linear",repeatCount:"indefinite"}),r("animate",{attributeName:"fill-opacity",from:"1",to:"1",begin:"0s",dur:"0.8s",values:"1;.5;1",calcMode:"linear",repeatCount:"indefinite"})]),r("circle",{cx:"60",cy:"15",r:"9","fill-opacity":".3"},[r("animate",{attributeName:"r",from:"9",to:"9",begin:"0s",dur:"0.8s",values:"9;15;9",calcMode:"linear",repeatCount:"indefinite"}),r("animate",{attributeName:"fill-opacity",from:".5",to:".5",begin:"0s",dur:"0.8s",values:".5;1;.5",calcMode:"linear",repeatCount:"indefinite"})]),r("circle",{cx:"105",cy:"15",r:"15"},[r("animate",{attributeName:"r",from:"15",to:"15",begin:"0s",dur:"0.8s",values:"15;9;15",calcMode:"linear",repeatCount:"indefinite"}),r("animate",{attributeName:"fill-opacity",from:"1",to:"1",begin:"0s",dur:"0.8s",values:"1;.5;1",calcMode:"linear",repeatCount:"indefinite"})])],Yh=te({name:"QSpinnerDots",props:gt,setup(e){const{cSize:t,classes:n}=ht(e);return()=>r("svg",{class:n.value,fill:"currentColor",width:t.value,height:t.value,viewBox:"0 0 120 30",xmlns:"http://www.w3.org/2000/svg"},Jv)}}),em=[r("g",{transform:"translate(20 50)"},[r("rect",{x:"-10",y:"-30",width:"20",height:"60",fill:"currentColor",opacity:"0.6"},[r("animateTransform",{attributeName:"transform",type:"scale",from:"2",to:"1",begin:"0s",repeatCount:"indefinite",dur:"1s",calcMode:"spline",keySplines:"0.1 0.9 0.4 1",keyTimes:"0;1",values:"2;1"})])]),r("g",{transform:"translate(50 50)"},[r("rect",{x:"-10",y:"-30",width:"20",height:"60",fill:"currentColor",opacity:"0.8"},[r("animateTransform",{attributeName:"transform",type:"scale",from:"2",to:"1",begin:"0.1s",repeatCount:"indefinite",dur:"1s",calcMode:"spline",keySplines:"0.1 0.9 0.4 1",keyTimes:"0;1",values:"2;1"})])]),r("g",{transform:"translate(80 50)"},[r("rect",{x:"-10",y:"-30",width:"20",height:"60",fill:"currentColor",opacity:"0.9"},[r("animateTransform",{attributeName:"transform",type:"scale",from:"2",to:"1",begin:"0.2s",repeatCount:"indefinite",dur:"1s",calcMode:"spline",keySplines:"0.1 0.9 0.4 1",keyTimes:"0;1",values:"2;1"})])])],Xh=te({name:"QSpinnerFacebook",props:gt,setup(e){const{cSize:t,classes:n}=ht(e);return()=>r("svg",{class:n.value,width:t.value,height:t.value,viewBox:"0 0 100 100",xmlns:"http://www.w3.org/2000/svg",preserveAspectRatio:"xMidYMid"},em)}}),tm=[r("g",{transform:"translate(-20,-20)"},[r("path",{d:"M79.9,52.6C80,51.8,80,50.9,80,50s0-1.8-0.1-2.6l-5.1-0.4c-0.3-2.4-0.9-4.6-1.8-6.7l4.2-2.9c-0.7-1.6-1.6-3.1-2.6-4.5 L70,35c-1.4-1.9-3.1-3.5-4.9-4.9l2.2-4.6c-1.4-1-2.9-1.9-4.5-2.6L59.8,27c-2.1-0.9-4.4-1.5-6.7-1.8l-0.4-5.1C51.8,20,50.9,20,50,20 s-1.8,0-2.6,0.1l-0.4,5.1c-2.4,0.3-4.6,0.9-6.7,1.8l-2.9-4.1c-1.6,0.7-3.1,1.6-4.5,2.6l2.1,4.6c-1.9,1.4-3.5,3.1-5,4.9l-4.5-2.1 c-1,1.4-1.9,2.9-2.6,4.5l4.1,2.9c-0.9,2.1-1.5,4.4-1.8,6.8l-5,0.4C20,48.2,20,49.1,20,50s0,1.8,0.1,2.6l5,0.4 c0.3,2.4,0.9,4.7,1.8,6.8l-4.1,2.9c0.7,1.6,1.6,3.1,2.6,4.5l4.5-2.1c1.4,1.9,3.1,3.5,5,4.9l-2.1,4.6c1.4,1,2.9,1.9,4.5,2.6l2.9-4.1 c2.1,0.9,4.4,1.5,6.7,1.8l0.4,5.1C48.2,80,49.1,80,50,80s1.8,0,2.6-0.1l0.4-5.1c2.3-0.3,4.6-0.9,6.7-1.8l2.9,4.2 c1.6-0.7,3.1-1.6,4.5-2.6L65,69.9c1.9-1.4,3.5-3,4.9-4.9l4.6,2.2c1-1.4,1.9-2.9,2.6-4.5L73,59.8c0.9-2.1,1.5-4.4,1.8-6.7L79.9,52.6 z M50,65c-8.3,0-15-6.7-15-15c0-8.3,6.7-15,15-15s15,6.7,15,15C65,58.3,58.3,65,50,65z",fill:"currentColor"},[r("animateTransform",{attributeName:"transform",type:"rotate",from:"90 50 50",to:"0 50 50",dur:"1s",repeatCount:"indefinite"})])]),r("g",{transform:"translate(20,20) rotate(15 50 50)"},[r("path",{d:"M79.9,52.6C80,51.8,80,50.9,80,50s0-1.8-0.1-2.6l-5.1-0.4c-0.3-2.4-0.9-4.6-1.8-6.7l4.2-2.9c-0.7-1.6-1.6-3.1-2.6-4.5 L70,35c-1.4-1.9-3.1-3.5-4.9-4.9l2.2-4.6c-1.4-1-2.9-1.9-4.5-2.6L59.8,27c-2.1-0.9-4.4-1.5-6.7-1.8l-0.4-5.1C51.8,20,50.9,20,50,20 s-1.8,0-2.6,0.1l-0.4,5.1c-2.4,0.3-4.6,0.9-6.7,1.8l-2.9-4.1c-1.6,0.7-3.1,1.6-4.5,2.6l2.1,4.6c-1.9,1.4-3.5,3.1-5,4.9l-4.5-2.1 c-1,1.4-1.9,2.9-2.6,4.5l4.1,2.9c-0.9,2.1-1.5,4.4-1.8,6.8l-5,0.4C20,48.2,20,49.1,20,50s0,1.8,0.1,2.6l5,0.4 c0.3,2.4,0.9,4.7,1.8,6.8l-4.1,2.9c0.7,1.6,1.6,3.1,2.6,4.5l4.5-2.1c1.4,1.9,3.1,3.5,5,4.9l-2.1,4.6c1.4,1,2.9,1.9,4.5,2.6l2.9-4.1 c2.1,0.9,4.4,1.5,6.7,1.8l0.4,5.1C48.2,80,49.1,80,50,80s1.8,0,2.6-0.1l0.4-5.1c2.3-0.3,4.6-0.9,6.7-1.8l2.9,4.2 c1.6-0.7,3.1-1.6,4.5-2.6L65,69.9c1.9-1.4,3.5-3,4.9-4.9l4.6,2.2c1-1.4,1.9-2.9,2.6-4.5L73,59.8c0.9-2.1,1.5-4.4,1.8-6.7L79.9,52.6 z M50,65c-8.3,0-15-6.7-15-15c0-8.3,6.7-15,15-15s15,6.7,15,15C65,58.3,58.3,65,50,65z",fill:"currentColor"},[r("animateTransform",{attributeName:"transform",type:"rotate",from:"0 50 50",to:"90 50 50",dur:"1s",repeatCount:"indefinite"})])])],Gh=te({name:"QSpinnerGears",props:gt,setup(e){const{cSize:t,classes:n}=ht(e);return()=>r("svg",{class:n.value,width:t.value,height:t.value,viewBox:"0 0 100 100",preserveAspectRatio:"xMidYMid",xmlns:"http://www.w3.org/2000/svg"},tm)}}),nm=[r("circle",{cx:"12.5",cy:"12.5",r:"12.5"},[r("animate",{attributeName:"fill-opacity",begin:"0s",dur:"1s",values:"1;.2;1",calcMode:"linear",repeatCount:"indefinite"})]),r("circle",{cx:"12.5",cy:"52.5",r:"12.5","fill-opacity":".5"},[r("animate",{attributeName:"fill-opacity",begin:"100ms",dur:"1s",values:"1;.2;1",calcMode:"linear",repeatCount:"indefinite"})]),r("circle",{cx:"52.5",cy:"12.5",r:"12.5"},[r("animate",{attributeName:"fill-opacity",begin:"300ms",dur:"1s",values:"1;.2;1",calcMode:"linear",repeatCount:"indefinite"})]),r("circle",{cx:"52.5",cy:"52.5",r:"12.5"},[r("animate",{attributeName:"fill-opacity",begin:"600ms",dur:"1s",values:"1;.2;1",calcMode:"linear",repeatCount:"indefinite"})]),r("circle",{cx:"92.5",cy:"12.5",r:"12.5"},[r("animate",{attributeName:"fill-opacity",begin:"800ms",dur:"1s",values:"1;.2;1",calcMode:"linear",repeatCount:"indefinite"})]),r("circle",{cx:"92.5",cy:"52.5",r:"12.5"},[r("animate",{attributeName:"fill-opacity",begin:"400ms",dur:"1s",values:"1;.2;1",calcMode:"linear",repeatCount:"indefinite"})]),r("circle",{cx:"12.5",cy:"92.5",r:"12.5"},[r("animate",{attributeName:"fill-opacity",begin:"700ms",dur:"1s",values:"1;.2;1",calcMode:"linear",repeatCount:"indefinite"})]),r("circle",{cx:"52.5",cy:"92.5",r:"12.5"},[r("animate",{attributeName:"fill-opacity",begin:"500ms",dur:"1s",values:"1;.2;1",calcMode:"linear",repeatCount:"indefinite"})]),r("circle",{cx:"92.5",cy:"92.5",r:"12.5"},[r("animate",{attributeName:"fill-opacity",begin:"200ms",dur:"1s",values:"1;.2;1",calcMode:"linear",repeatCount:"indefinite"})])],Zh=te({name:"QSpinnerGrid",props:gt,setup(e){const{cSize:t,classes:n}=ht(e);return()=>r("svg",{class:n.value,fill:"currentColor",width:t.value,height:t.value,viewBox:"0 0 105 105",xmlns:"http://www.w3.org/2000/svg"},nm)}}),am=[r("path",{d:"M30.262 57.02L7.195 40.723c-5.84-3.976-7.56-12.06-3.842-18.063 3.715-6 11.467-7.65 17.306-3.68l4.52 3.76 2.6-5.274c3.716-6.002 11.47-7.65 17.304-3.68 5.84 3.97 7.56 12.054 3.842 18.062L34.49 56.118c-.897 1.512-2.793 1.915-4.228.9z","fill-opacity":".5"},[r("animate",{attributeName:"fill-opacity",begin:"0s",dur:"1.4s",values:"0.5;1;0.5",calcMode:"linear",repeatCount:"indefinite"})]),r("path",{d:"M105.512 56.12l-14.44-24.272c-3.716-6.008-1.996-14.093 3.843-18.062 5.835-3.97 13.588-2.322 17.306 3.68l2.6 5.274 4.52-3.76c5.84-3.97 13.593-2.32 17.308 3.68 3.718 6.003 1.998 14.088-3.842 18.064L109.74 57.02c-1.434 1.014-3.33.61-4.228-.9z","fill-opacity":".5"},[r("animate",{attributeName:"fill-opacity",begin:"0.7s",dur:"1.4s",values:"0.5;1;0.5",calcMode:"linear",repeatCount:"indefinite"})]),r("path",{d:"M67.408 57.834l-23.01-24.98c-5.864-6.15-5.864-16.108 0-22.248 5.86-6.14 15.37-6.14 21.234 0L70 16.168l4.368-5.562c5.863-6.14 15.375-6.14 21.235 0 5.863 6.14 5.863 16.098 0 22.247l-23.007 24.98c-1.43 1.556-3.757 1.556-5.188 0z"})],Jh=te({name:"QSpinnerHearts",props:gt,setup(e){const{cSize:t,classes:n}=ht(e);return()=>r("svg",{class:n.value,fill:"currentColor",width:t.value,height:t.value,viewBox:"0 0 140 64",xmlns:"http://www.w3.org/2000/svg"},am)}}),lm=[r("g",[r("path",{fill:"none",stroke:"currentColor","stroke-width":"5","stroke-miterlimit":"10",d:"M58.4,51.7c-0.9-0.9-1.4-2-1.4-2.3s0.5-0.4,1.4-1.4 C70.8,43.8,79.8,30.5,80,15.5H70H30H20c0.2,15,9.2,28.1,21.6,32.3c0.9,0.9,1.4,1.2,1.4,1.5s-0.5,1.6-1.4,2.5 C29.2,56.1,20.2,69.5,20,85.5h10h40h10C79.8,69.5,70.8,55.9,58.4,51.7z"}),r("clipPath",{id:"uil-hourglass-clip1"},[r("rect",{x:"15",y:"20",width:"70",height:"25"},[r("animate",{attributeName:"height",from:"25",to:"0",dur:"1s",repeatCount:"indefinite",values:"25;0;0",keyTimes:"0;0.5;1"}),r("animate",{attributeName:"y",from:"20",to:"45",dur:"1s",repeatCount:"indefinite",values:"20;45;45",keyTimes:"0;0.5;1"})])]),r("clipPath",{id:"uil-hourglass-clip2"},[r("rect",{x:"15",y:"55",width:"70",height:"25"},[r("animate",{attributeName:"height",from:"0",to:"25",dur:"1s",repeatCount:"indefinite",values:"0;25;25",keyTimes:"0;0.5;1"}),r("animate",{attributeName:"y",from:"80",to:"55",dur:"1s",repeatCount:"indefinite",values:"80;55;55",keyTimes:"0;0.5;1"})])]),r("path",{d:"M29,23c3.1,11.4,11.3,19.5,21,19.5S67.9,34.4,71,23H29z","clip-path":"url(#uil-hourglass-clip1)",fill:"currentColor"}),r("path",{d:"M71.6,78c-3-11.6-11.5-20-21.5-20s-18.5,8.4-21.5,20H71.6z","clip-path":"url(#uil-hourglass-clip2)",fill:"currentColor"}),r("animateTransform",{attributeName:"transform",type:"rotate",from:"0 50 50",to:"180 50 50",repeatCount:"indefinite",dur:"1s",values:"0 50 50;0 50 50;180 50 50",keyTimes:"0;0.7;1"})])],e0=te({name:"QSpinnerHourglass",props:gt,setup(e){const{cSize:t,classes:n}=ht(e);return()=>r("svg",{class:n.value,width:t.value,height:t.value,viewBox:"0 0 100 100",preserveAspectRatio:"xMidYMid",xmlns:"http://www.w3.org/2000/svg"},lm)}}),om=[r("path",{d:"M24.3,30C11.4,30,5,43.3,5,50s6.4,20,19.3,20c19.3,0,32.1-40,51.4-40C88.6,30,95,43.3,95,50s-6.4,20-19.3,20C56.4,70,43.6,30,24.3,30z",fill:"none",stroke:"currentColor","stroke-width":"8","stroke-dasharray":"10.691205342610678 10.691205342610678","stroke-dashoffset":"0"},[r("animate",{attributeName:"stroke-dashoffset",from:"0",to:"21.382410685221355",begin:"0",dur:"2s",repeatCount:"indefinite",fill:"freeze"})])],t0=te({name:"QSpinnerInfinity",props:gt,setup(e){const{cSize:t,classes:n}=ht(e);return()=>r("svg",{class:n.value,width:t.value,height:t.value,viewBox:"0 0 100 100",preserveAspectRatio:"xMidYMid"},om)}}),rm=[r("g",{"stroke-width":"4","stroke-linecap":"round"},[r("line",{y1:"17",y2:"29",transform:"translate(32,32) rotate(180)"},[r("animate",{attributeName:"stroke-opacity",dur:"750ms",values:"1;.85;.7;.65;.55;.45;.35;.25;.15;.1;0;1",repeatCount:"indefinite"})]),r("line",{y1:"17",y2:"29",transform:"translate(32,32) rotate(210)"},[r("animate",{attributeName:"stroke-opacity",dur:"750ms",values:"0;1;.85;.7;.65;.55;.45;.35;.25;.15;.1;0",repeatCount:"indefinite"})]),r("line",{y1:"17",y2:"29",transform:"translate(32,32) rotate(240)"},[r("animate",{attributeName:"stroke-opacity",dur:"750ms",values:".1;0;1;.85;.7;.65;.55;.45;.35;.25;.15;.1",repeatCount:"indefinite"})]),r("line",{y1:"17",y2:"29",transform:"translate(32,32) rotate(270)"},[r("animate",{attributeName:"stroke-opacity",dur:"750ms",values:".15;.1;0;1;.85;.7;.65;.55;.45;.35;.25;.15",repeatCount:"indefinite"})]),r("line",{y1:"17",y2:"29",transform:"translate(32,32) rotate(300)"},[r("animate",{attributeName:"stroke-opacity",dur:"750ms",values:".25;.15;.1;0;1;.85;.7;.65;.55;.45;.35;.25",repeatCount:"indefinite"})]),r("line",{y1:"17",y2:"29",transform:"translate(32,32) rotate(330)"},[r("animate",{attributeName:"stroke-opacity",dur:"750ms",values:".35;.25;.15;.1;0;1;.85;.7;.65;.55;.45;.35",repeatCount:"indefinite"})]),r("line",{y1:"17",y2:"29",transform:"translate(32,32) rotate(0)"},[r("animate",{attributeName:"stroke-opacity",dur:"750ms",values:".45;.35;.25;.15;.1;0;1;.85;.7;.65;.55;.45",repeatCount:"indefinite"})]),r("line",{y1:"17",y2:"29",transform:"translate(32,32) rotate(30)"},[r("animate",{attributeName:"stroke-opacity",dur:"750ms",values:".55;.45;.35;.25;.15;.1;0;1;.85;.7;.65;.55",repeatCount:"indefinite"})]),r("line",{y1:"17",y2:"29",transform:"translate(32,32) rotate(60)"},[r("animate",{attributeName:"stroke-opacity",dur:"750ms",values:".65;.55;.45;.35;.25;.15;.1;0;1;.85;.7;.65",repeatCount:"indefinite"})]),r("line",{y1:"17",y2:"29",transform:"translate(32,32) rotate(90)"},[r("animate",{attributeName:"stroke-opacity",dur:"750ms",values:".7;.65;.55;.45;.35;.25;.15;.1;0;1;.85;.7",repeatCount:"indefinite"})]),r("line",{y1:"17",y2:"29",transform:"translate(32,32) rotate(120)"},[r("animate",{attributeName:"stroke-opacity",dur:"750ms",values:".85;.7;.65;.55;.45;.35;.25;.15;.1;0;1;.85",repeatCount:"indefinite"})]),r("line",{y1:"17",y2:"29",transform:"translate(32,32) rotate(150)"},[r("animate",{attributeName:"stroke-opacity",dur:"750ms",values:"1;.85;.7;.65;.55;.45;.35;.25;.15;.1;0;1",repeatCount:"indefinite"})])])],n0=te({name:"QSpinnerIos",props:gt,setup(e){const{cSize:t,classes:n}=ht(e);return()=>r("svg",{class:n.value,width:t.value,height:t.value,stroke:"currentColor",fill:"currentColor",viewBox:"0 0 64 64"},rm)}}),im=[r("circle",{cx:"50",cy:"50",r:"44",fill:"none","stroke-width":"4","stroke-opacity":".5",stroke:"currentColor"}),r("circle",{cx:"8",cy:"54",r:"6",fill:"currentColor","stroke-width":"3",stroke:"currentColor"},[r("animateTransform",{attributeName:"transform",type:"rotate",from:"0 50 48",to:"360 50 52",dur:"2s",repeatCount:"indefinite"})])],a0=te({name:"QSpinnerOrbit",props:gt,setup(e){const{cSize:t,classes:n}=ht(e);return()=>r("svg",{class:n.value,width:t.value,height:t.value,viewBox:"0 0 100 100",preserveAspectRatio:"xMidYMid",xmlns:"http://www.w3.org/2000/svg"},im)}}),um=[r("g",{transform:"translate(1 1)","stroke-width":"2",fill:"none","fill-rule":"evenodd"},[r("circle",{"stroke-opacity":".5",cx:"18",cy:"18",r:"18"}),r("path",{d:"M36 18c0-9.94-8.06-18-18-18"},[r("animateTransform",{attributeName:"transform",type:"rotate",from:"0 18 18",to:"360 18 18",dur:"1s",repeatCount:"indefinite"})])])],l0=te({name:"QSpinnerOval",props:gt,setup(e){const{cSize:t,classes:n}=ht(e);return()=>r("svg",{class:n.value,stroke:"currentColor",width:t.value,height:t.value,viewBox:"0 0 38 38",xmlns:"http://www.w3.org/2000/svg"},um)}}),sm=[r("path",{d:"M0 50A50 50 0 0 1 50 0L50 50L0 50",fill:"currentColor",opacity:"0.5"},[r("animateTransform",{attributeName:"transform",type:"rotate",from:"0 50 50",to:"360 50 50",dur:"0.8s",repeatCount:"indefinite"})]),r("path",{d:"M50 0A50 50 0 0 1 100 50L50 50L50 0",fill:"currentColor",opacity:"0.5"},[r("animateTransform",{attributeName:"transform",type:"rotate",from:"0 50 50",to:"360 50 50",dur:"1.6s",repeatCount:"indefinite"})]),r("path",{d:"M100 50A50 50 0 0 1 50 100L50 50L100 50",fill:"currentColor",opacity:"0.5"},[r("animateTransform",{attributeName:"transform",type:"rotate",from:"0 50 50",to:"360 50 50",dur:"2.4s",repeatCount:"indefinite"})]),r("path",{d:"M50 100A50 50 0 0 1 0 50L50 50L50 100",fill:"currentColor",opacity:"0.5"},[r("animateTransform",{attributeName:"transform",type:"rotate",from:"0 50 50",to:"360 50 50",dur:"3.2s",repeatCount:"indefinite"})])],o0=te({name:"QSpinnerPie",props:gt,setup(e){const{cSize:t,classes:n}=ht(e);return()=>r("svg",{class:n.value,width:t.value,height:t.value,viewBox:"0 0 100 100",preserveAspectRatio:"xMidYMid",xmlns:"http://www.w3.org/2000/svg"},sm)}}),cm=[r("g",{fill:"none","fill-rule":"evenodd","stroke-width":"2"},[r("circle",{cx:"22",cy:"22",r:"1"},[r("animate",{attributeName:"r",begin:"0s",dur:"1.8s",values:"1; 20",calcMode:"spline",keyTimes:"0; 1",keySplines:"0.165, 0.84, 0.44, 1",repeatCount:"indefinite"}),r("animate",{attributeName:"stroke-opacity",begin:"0s",dur:"1.8s",values:"1; 0",calcMode:"spline",keyTimes:"0; 1",keySplines:"0.3, 0.61, 0.355, 1",repeatCount:"indefinite"})]),r("circle",{cx:"22",cy:"22",r:"1"},[r("animate",{attributeName:"r",begin:"-0.9s",dur:"1.8s",values:"1; 20",calcMode:"spline",keyTimes:"0; 1",keySplines:"0.165, 0.84, 0.44, 1",repeatCount:"indefinite"}),r("animate",{attributeName:"stroke-opacity",begin:"-0.9s",dur:"1.8s",values:"1; 0",calcMode:"spline",keyTimes:"0; 1",keySplines:"0.3, 0.61, 0.355, 1",repeatCount:"indefinite"})])])],r0=te({name:"QSpinnerPuff",props:gt,setup(e){const{cSize:t,classes:n}=ht(e);return()=>r("svg",{class:n.value,stroke:"currentColor",width:t.value,height:t.value,viewBox:"0 0 44 44",xmlns:"http://www.w3.org/2000/svg"},cm)}}),dm=[r("g",{transform:"scale(0.55)"},[r("circle",{cx:"30",cy:"150",r:"30",fill:"currentColor"},[r("animate",{attributeName:"opacity",from:"0",to:"1",dur:"1s",begin:"0",repeatCount:"indefinite",keyTimes:"0;0.5;1",values:"0;1;1"})]),r("path",{d:"M90,150h30c0-49.7-40.3-90-90-90v30C63.1,90,90,116.9,90,150z",fill:"currentColor"},[r("animate",{attributeName:"opacity",from:"0",to:"1",dur:"1s",begin:"0.1",repeatCount:"indefinite",keyTimes:"0;0.5;1",values:"0;1;1"})]),r("path",{d:"M150,150h30C180,67.2,112.8,0,30,0v30C96.3,30,150,83.7,150,150z",fill:"currentColor"},[r("animate",{attributeName:"opacity",from:"0",to:"1",dur:"1s",begin:"0.2",repeatCount:"indefinite",keyTimes:"0;0.5;1",values:"0;1;1"})])])],i0=te({name:"QSpinnerRadio",props:gt,setup(e){const{cSize:t,classes:n}=ht(e);return()=>r("svg",{class:n.value,width:t.value,height:t.value,viewBox:"0 0 100 100",preserveAspectRatio:"xMidYMid",xmlns:"http://www.w3.org/2000/svg"},dm)}}),fm=[r("g",{fill:"none","fill-rule":"evenodd",transform:"translate(1 1)","stroke-width":"2"},[r("circle",{cx:"22",cy:"22",r:"6"},[r("animate",{attributeName:"r",begin:"1.5s",dur:"3s",values:"6;22",calcMode:"linear",repeatCount:"indefinite"}),r("animate",{attributeName:"stroke-opacity",begin:"1.5s",dur:"3s",values:"1;0",calcMode:"linear",repeatCount:"indefinite"}),r("animate",{attributeName:"stroke-width",begin:"1.5s",dur:"3s",values:"2;0",calcMode:"linear",repeatCount:"indefinite"})]),r("circle",{cx:"22",cy:"22",r:"6"},[r("animate",{attributeName:"r",begin:"3s",dur:"3s",values:"6;22",calcMode:"linear",repeatCount:"indefinite"}),r("animate",{attributeName:"stroke-opacity",begin:"3s",dur:"3s",values:"1;0",calcMode:"linear",repeatCount:"indefinite"}),r("animate",{attributeName:"stroke-width",begin:"3s",dur:"3s",values:"2;0",calcMode:"linear",repeatCount:"indefinite"})]),r("circle",{cx:"22",cy:"22",r:"8"},[r("animate",{attributeName:"r",begin:"0s",dur:"1.5s",values:"6;1;2;3;4;5;6",calcMode:"linear",repeatCount:"indefinite"})])])],u0=te({name:"QSpinnerRings",props:gt,setup(e){const{cSize:t,classes:n}=ht(e);return()=>r("svg",{class:n.value,stroke:"currentColor",width:t.value,height:t.value,viewBox:"0 0 45 45",xmlns:"http://www.w3.org/2000/svg"},fm)}}),vm=[r("defs",[r("linearGradient",{x1:"8.042%",y1:"0%",x2:"65.682%",y2:"23.865%",id:"a"},[r("stop",{"stop-color":"currentColor","stop-opacity":"0",offset:"0%"}),r("stop",{"stop-color":"currentColor","stop-opacity":".631",offset:"63.146%"}),r("stop",{"stop-color":"currentColor",offset:"100%"})])]),r("g",{transform:"translate(1 1)",fill:"none","fill-rule":"evenodd"},[r("path",{d:"M36 18c0-9.94-8.06-18-18-18",stroke:"url(#a)","stroke-width":"2"},[r("animateTransform",{attributeName:"transform",type:"rotate",from:"0 18 18",to:"360 18 18",dur:"0.9s",repeatCount:"indefinite"})]),r("circle",{fill:"currentColor",cx:"36",cy:"18",r:"1"},[r("animateTransform",{attributeName:"transform",type:"rotate",from:"0 18 18",to:"360 18 18",dur:"0.9s",repeatCount:"indefinite"})])])],s0=te({name:"QSpinnerTail",props:gt,setup(e){const{cSize:t,classes:n}=ht(e);return()=>r("svg",{class:n.value,width:t.value,height:t.value,viewBox:"0 0 38 38",xmlns:"http://www.w3.org/2000/svg"},vm)}}),c0=te({name:"QSplitter",props:{...Ke,modelValue:{type:Number,required:!0},reverse:Boolean,unit:{type:String,default:"%",validator:e=>["%","px"].includes(e)},limits:{type:Array,validator:e=>e.length!==2||typeof e[0]!="number"||typeof e[1]!="number"?!1:e[0]>=0&&e[0]<=e[1]},emitImmediately:Boolean,horizontal:Boolean,disable:Boolean,beforeClass:[Array,String,Object],afterClass:[Array,String,Object],separatorClass:[Array,String,Object],separatorStyle:[Array,String,Object]},emits:["update:modelValue"],setup(e,{slots:t,emit:n}){const{proxy:{$q:l}}=ve(),a=We(e,l),o=O(null),i={before:O(null),after:O(null)},u=s(()=>`q-splitter no-wrap ${e.horizontal===!0?"q-splitter--horizontal column":"q-splitter--vertical row"} q-splitter--${e.disable===!0?"disabled":"workable"}`+(a.value===!0?" q-splitter--dark":"")),d=s(()=>e.horizontal===!0?"height":"width"),f=s(()=>e.reverse!==!0?"before":"after"),c=s(()=>e.limits!==void 0?e.limits:e.unit==="%"?[10,90]:[50,1/0]);function h(S){return(e.unit==="%"?S:Math.round(S))+e.unit}const m=s(()=>({[f.value]:{[d.value]:h(e.modelValue)}}));let v,g,_,p,k;function w(S){if(S.isFirst===!0){const L=o.value.getBoundingClientRect()[d.value];v=e.horizontal===!0?"up":"left",g=e.unit==="%"?100:L,_=Math.min(g,c.value[1],Math.max(c.value[0],e.modelValue)),p=(e.reverse!==!0?1:-1)*(e.horizontal===!0?1:l.lang.rtl===!0?-1:1)*(e.unit==="%"?L===0?0:100/L:1),o.value.classList.add("q-splitter--active");return}if(S.isFinal===!0){k!==e.modelValue&&n("update:modelValue",k),o.value.classList.remove("q-splitter--active");return}const B=_+p*(S.direction===v?-1:1)*S.distance[e.horizontal===!0?"y":"x"];k=Math.min(g,c.value[1],Math.max(c.value[0],B)),i[f.value].value.style[d.value]=h(k),e.emitImmediately===!0&&e.modelValue!==k&&n("update:modelValue",k)}const y=s(()=>[[Ft,w,void 0,{[e.horizontal===!0?"vertical":"horizontal"]:!0,prevent:!0,stop:!0,mouse:!0,mouseAllDir:!0}]]);function b(S,B){S<B[0]?n("update:modelValue",B[0]):S>B[1]&&n("update:modelValue",B[1])}return ne(()=>e.modelValue,S=>{b(S,c.value)}),ne(()=>e.limits,()=>{Qe(()=>{b(e.modelValue,c.value)})}),()=>{const S=[r("div",{ref:i.before,class:["q-splitter__panel q-splitter__before"+(e.reverse===!0?" col":""),e.beforeClass],style:m.value.before},Ce(t.before)),r("div",{class:["q-splitter__separator",e.separatorClass],style:e.separatorStyle,"aria-disabled":e.disable===!0?"true":void 0},[Dt("div",{class:"q-splitter__separator-area absolute-full"},Ce(t.separator),"sep",e.disable!==!0,()=>y.value)]),r("div",{ref:i.after,class:["q-splitter__panel q-splitter__after"+(e.reverse===!0?"":" col"),e.afterClass],style:m.value.after},Ce(t.after))];return r("div",{class:u.value,ref:o},mt(t.default,S))}}}),Fs=te({name:"StepHeader",props:{stepper:{},step:{},goToPanel:Function},setup(e,{attrs:t}){const{proxy:{$q:n}}=ve(),l=O(null),a=s(()=>e.stepper.modelValue===e.step.name),o=s(()=>{const p=e.step.disable;return p===!0||p===""}),i=s(()=>{const p=e.step.error;return p===!0||p===""}),u=s(()=>{const p=e.step.done;return o.value===!1&&(p===!0||p==="")}),d=s(()=>{const p=e.step.headerNav,k=p===!0||p===""||p===void 0;return o.value===!1&&e.stepper.headerNav&&k}),f=s(()=>e.step.prefix&&(a.value===!1||e.stepper.activeIcon==="none")&&(i.value===!1||e.stepper.errorIcon==="none")&&(u.value===!1||e.stepper.doneIcon==="none")),c=s(()=>{const p=e.step.icon||e.stepper.inactiveIcon;if(a.value===!0){const k=e.step.activeIcon||e.stepper.activeIcon;return k==="none"?p:k||n.iconSet.stepper.active}if(i.value===!0){const k=e.step.errorIcon||e.stepper.errorIcon;return k==="none"?p:k||n.iconSet.stepper.error}if(o.value===!1&&u.value===!0){const k=e.step.doneIcon||e.stepper.doneIcon;return k==="none"?p:k||n.iconSet.stepper.done}return p}),h=s(()=>{const p=i.value===!0?e.step.errorColor||e.stepper.errorColor:void 0;if(a.value===!0){const k=e.step.activeColor||e.stepper.activeColor||e.step.color;return k!==void 0?k:p}return p!==void 0?p:o.value===!1&&u.value===!0?e.step.doneColor||e.stepper.doneColor||e.step.color||e.stepper.inactiveColor:e.step.color||e.stepper.inactiveColor}),m=s(()=>"q-stepper__tab col-grow flex items-center no-wrap relative-position"+(h.value!==void 0?` text-${h.value}`:"")+(i.value===!0?" q-stepper__tab--error q-stepper__tab--error-with-"+(f.value===!0?"prefix":"icon"):"")+(a.value===!0?" q-stepper__tab--active":"")+(u.value===!0?" q-stepper__tab--done":"")+(d.value===!0?" q-stepper__tab--navigation q-focusable q-hoverable":"")+(o.value===!0?" q-stepper__tab--disabled":"")),v=s(()=>e.stepper.headerNav!==!0?!1:d.value);function g(){l.value!==null&&l.value.focus(),a.value===!1&&e.goToPanel(e.step.name)}function _(p){p.keyCode===13&&a.value===!1&&e.goToPanel(e.step.name)}return()=>{const p={class:m.value};d.value===!0&&(p.onClick=g,p.onKeyup=_,Object.assign(p,o.value===!0?{tabindex:-1,"aria-disabled":"true"}:{tabindex:t.tabindex||0}));const k=[r("div",{class:"q-focus-helper",tabindex:-1,ref:l}),r("div",{class:"q-stepper__dot row flex-center q-stepper__line relative-position"},[r("span",{class:"row flex-center"},[f.value===!0?e.step.prefix:r(je,{name:c.value})])])];if(e.step.title!==void 0&&e.step.title!==null){const w=[r("div",{class:"q-stepper__title"},e.step.title)];e.step.caption!==void 0&&e.step.caption!==null&&w.push(r("div",{class:"q-stepper__caption"},e.step.caption)),k.push(r("div",{class:"q-stepper__label q-stepper__line relative-position"},w))}return Ut(r("div",p,k),[[Ol,v.value]])}}});function Vs(e){return r("div",{class:"q-stepper__step-content"},[r("div",{class:"q-stepper__step-inner"},Ce(e.default))])}var Mi={setup(e,{slots:t}){return()=>Vs(t)}},d0=te({name:"QStep",props:{...tr,icon:String,color:String,title:{type:String,required:!0},caption:String,prefix:[String,Number],doneIcon:String,doneColor:String,activeIcon:String,activeColor:String,errorIcon:String,errorColor:String,headerNav:{type:Boolean,default:!0},done:Boolean,error:Boolean,onScroll:[Function,Array]},setup(e,{slots:t,emit:n}){const{proxy:{$q:l}}=ve(),a=Et(uu,Je);if(a===Je)return console.error("QStep needs to be a child of QStepper"),Je;const{getCache:o}=Wa(),i=O(null),u=s(()=>a.value.modelValue===e.name),d=s(()=>l.platform.is.ios!==!0&&l.platform.is.chrome===!0||u.value!==!0||a.value.vertical!==!0?{}:{onScroll(h){const{target:m}=h;m.scrollTop>0&&(m.scrollTop=0),e.onScroll!==void 0&&n("scroll",h)}}),f=s(()=>typeof e.name=="string"||typeof e.name=="number"?e.name:String(e.name));function c(){const h=a.value.vertical;return h===!0&&a.value.keepAlive===!0?r(Gi,a.value.keepAliveProps.value,u.value===!0?[r(a.value.needsUniqueKeepAliveWrapper.value===!0?o(f.value,()=>({...Mi,name:f.value})):Mi,{key:f.value},t.default)]:void 0):h!==!0||u.value===!0?Vs(t):void 0}return()=>r("div",{ref:i,class:"q-stepper__step",role:"tabpanel",...d.value},a.value.vertical===!0?[r(Fs,{stepper:a.value,step:e,goToPanel:a.value.goToPanel}),a.value.animated===!0?r(yr,c):c()]:[c()])}}),mm=/(-\w)/g;function gm(e){const t={};for(const n in e){const l=n.replace(mm,a=>a[1].toUpperCase());t[l]=e[n]}return t}var f0=te({name:"QStepper",props:{...Ke,...nr,flat:Boolean,bordered:Boolean,alternativeLabels:Boolean,headerNav:Boolean,contracted:Boolean,headerClass:String,inactiveColor:String,inactiveIcon:String,doneIcon:String,doneColor:String,activeIcon:String,activeColor:String,errorIcon:String,errorColor:String},emits:ar,setup(e,{slots:t}){const n=ve(),l=We(e,n.proxy.$q),{updatePanelsList:a,isValidPanelName:o,updatePanelIndex:i,getPanelContent:u,getPanels:d,panelDirectives:f,goToPanel:c,keepAliveProps:h,needsUniqueKeepAliveWrapper:m}=lr();yn(uu,s(()=>({goToPanel:c,keepAliveProps:h,needsUniqueKeepAliveWrapper:m,...e})));const v=s(()=>`q-stepper q-stepper--${e.vertical===!0?"vertical":"horizontal"}`+(e.flat===!0?" q-stepper--flat":"")+(e.bordered===!0?" q-stepper--bordered":"")+(l.value===!0?" q-stepper--dark q-dark":"")),g=s(()=>`q-stepper__header row items-stretch justify-between q-stepper__header--${e.alternativeLabels===!0?"alternative":"standard"}-labels`+(e.flat===!1||e.bordered===!0?" q-stepper__header--border":"")+(e.contracted===!0?" q-stepper__header--contracted":"")+(e.headerClass!==void 0?` ${e.headerClass}`:""));function _(){const p=Ce(t.message,[]);if(e.vertical===!0){o(e.modelValue)&&i();const k=r("div",{class:"q-stepper__content"},Ce(t.default));return p===void 0?[k]:p.concat(k)}return[r("div",{class:g.value},d().map(k=>{const w=gm(k.props);return r(Fs,{key:w.name,stepper:e,step:w,goToPanel:c})})),p,Dt("div",{class:"q-stepper__content q-panel-parent"},u(),"cont",e.swipeable,()=>f.value)]}return()=>(a(t),r("div",{class:v.value},mt(t.navigation,_())))}}),v0=te({name:"QStepperNavigation",setup(e,{slots:t}){return()=>r("div",{class:"q-stepper__nav"},Ce(t.default))}}),hm=te({name:"QTh",props:{props:Object,autoWidth:Boolean},emits:["click"],setup(e,{slots:t,emit:n}){const l=ve(),{proxy:{$q:a}}=l,o=i=>{n("click",i)};return()=>{if(e.props===void 0)return r("th",{class:e.autoWidth===!0?"q-table--col-auto-width":"",onClick:o},Ce(t.default));let i,u;const d=l.vnode.key;if(d){if(i=e.props.colsMap[d],i===void 0)return}else i=e.props.col;if(i.sortable===!0){const c=i.align==="right"?"unshift":"push";u=Ha(t.default,[]),u[c](r(je,{class:i.__iconClass,name:a.iconSet.table.arrowUp}))}else u=Ce(t.default);const f={class:i.__thClass+(e.autoWidth===!0?" q-table--col-auto-width":""),style:i.headerStyle,onClick:c=>{i.sortable===!0&&e.props.sort(i),o(c)}};return r("th",f,u)}}});function Ds(e,t){return r("div",e,[r("table",{class:"q-table"},t)])}var bm={list:qv,table:Bv},ym=["list","table","__qtable"],pm=te({name:"QVirtualScroll",props:{...Eo,type:{type:String,default:"list",validator:e=>ym.includes(e)},items:{type:Array,default:()=>[]},itemsFn:Function,itemsSize:Number,scrollTarget:Qn},setup(e,{slots:t,attrs:n}){let l;const a=O(null),o=s(()=>e.itemsSize>=0&&e.itemsFn!==void 0?parseInt(e.itemsSize,10):Array.isArray(e.items)?e.items.length:0),{virtualScrollSliceRange:i,localResetVirtualScroll:u,padVirtualScroll:d,onVirtualScrollEvt:f}=Rs({virtualScrollLength:o,getVirtualScrollTarget:g,getVirtualScrollEl:v}),c=s(()=>{if(o.value===0)return[];const w=(y,b)=>({index:i.value.from+b,item:y});return e.itemsFn===void 0?e.items.slice(i.value.from,i.value.to).map(w):e.itemsFn(i.value.from,i.value.to-i.value.from).map(w)}),h=s(()=>"q-virtual-scroll q-virtual-scroll"+(e.virtualScrollHorizontal===!0?"--horizontal":"--vertical")+(e.scrollTarget!==void 0?"":" scroll")),m=s(()=>e.scrollTarget!==void 0?{}:{tabindex:0});ne(o,()=>{u()}),ne(()=>e.scrollTarget,()=>{p(),_()});function v(){return a.value.$el||a.value}function g(){return l}function _(){l=Gt(v(),e.scrollTarget),l.addEventListener("scroll",f,et.passive)}function p(){l!==void 0&&(l.removeEventListener("scroll",f,et.passive),l=void 0)}function k(){let w=d(e.type==="list"?"div":"tbody",c.value.map(t.default));return t.before!==void 0&&(w=t.before().concat(w)),mt(t.after,w)}return Ro(()=>{u()}),ft(()=>{_()}),bn(()=>{_()}),Yt(()=>{p()}),Ne(()=>{p()}),()=>{if(t.default===void 0){console.error("QVirtualScroll: default scoped slot is required for rendering");return}return e.type==="__qtable"?Ds({ref:a,class:"q-table__middle "+h.value},k()):r(bm[e.type],{...n,ref:a,class:[n.class,h.value],...m.value},k)}}});function wm(e,t){return new Date(e)-new Date(t)}var _m={sortMethod:Function,binaryStateSort:Boolean,columnSortOrder:{type:String,validator:e=>e==="ad"||e==="da",default:"ad"}};function Sm(e,t,n,l){const a=s(()=>{const{sortBy:u}=t.value;return u&&n.value.find(d=>d.name===u)||null}),o=s(()=>e.sortMethod!==void 0?e.sortMethod:(u,d,f)=>{const c=n.value.find(v=>v.name===d);if(c===void 0||c.field===void 0)return u;const h=f===!0?-1:1,m=typeof c.field=="function"?v=>c.field(v):v=>v[c.field];return u.sort((v,g)=>{let _=m(v),p=m(g);return c.rawSort!==void 0?c.rawSort(_,p,v,g)*h:_==null?-1*h:p==null?1*h:c.sort!==void 0?c.sort(_,p,v,g)*h:Rn(_)===!0&&Rn(p)===!0?(_-p)*h:sa(_)===!0&&sa(p)===!0?wm(_,p)*h:typeof _=="boolean"&&typeof p=="boolean"?(_-p)*h:([_,p]=[_,p].map(k=>(k+"").toLocaleString().toLowerCase()),_<p?-1*h:_===p?0:h)})});function i(u){let d=e.columnSortOrder;if(yt(u)===!0)u.sortOrder&&(d=u.sortOrder),u=u.name;else{const h=n.value.find(m=>m.name===u);h!==void 0&&h.sortOrder&&(d=h.sortOrder)}let{sortBy:f,descending:c}=t.value;f!==u?(f=u,c=d==="da"):e.binaryStateSort===!0?c=!c:c===!0?d==="ad"?f=null:c=!1:d==="ad"?c=!0:f=null,l({sortBy:f,descending:c,page:1})}return{columnToSort:a,computedSortMethod:o,sort:i}}var xm={filter:[String,Object],filterMethod:Function};function Cm(e,t){const n=s(()=>e.filterMethod!==void 0?e.filterMethod:(l,a,o,i)=>{const u=a?a.toLowerCase():"";return l.filter(d=>o.some(f=>{const c=i(f,d)+"";return(c==="undefined"||c==="null"?"":c.toLowerCase()).indexOf(u)!==-1}))});return ne(()=>e.filter,()=>{Qe(()=>{t({page:1},!0)})},{deep:!0}),{computedFilterMethod:n}}function km(e,t){for(const n in t)if(t[n]!==e[n])return!1;return!0}function $i(e){return e.page<1&&(e.page=1),e.rowsPerPage!==void 0&&e.rowsPerPage<1&&(e.rowsPerPage=0),e}var qm={pagination:Object,rowsPerPageOptions:{type:Array,default:()=>[5,7,10,15,20,25,50,0]},"onUpdate:pagination":[Function,Array]};function Tm(e,t){const{props:n,emit:l}=e,a=O(Object.assign({sortBy:null,descending:!1,page:1,rowsPerPage:n.rowsPerPageOptions.length!==0?n.rowsPerPageOptions[0]:5},n.pagination)),o=s(()=>{const c=n["onUpdate:pagination"]!==void 0?{...a.value,...n.pagination}:a.value;return $i(c)}),i=s(()=>o.value.rowsNumber!==void 0);function u(c){d({pagination:c,filter:n.filter})}function d(c={}){Qe(()=>{l("request",{pagination:c.pagination||o.value,filter:c.filter||n.filter,getCellValue:t})})}function f(c,h){const m=$i({...o.value,...c});if(km(o.value,m)===!0){i.value===!0&&h===!0&&u(m);return}if(i.value===!0){u(m);return}n.pagination!==void 0&&n["onUpdate:pagination"]!==void 0?l("update:pagination",m):a.value=m}return{innerPagination:a,computedPagination:o,isServerSide:i,requestServerInteraction:d,setPagination:f}}function Mm(e,t,n,l,a,o){const{props:i,emit:u,proxy:{$q:d}}=e,f=s(()=>l.value===!0?n.value.rowsNumber||0:o.value),c=s(()=>{const{page:b,rowsPerPage:S}=n.value;return(b-1)*S}),h=s(()=>{const{page:b,rowsPerPage:S}=n.value;return b*S}),m=s(()=>n.value.page===1),v=s(()=>n.value.rowsPerPage===0?1:Math.max(1,Math.ceil(f.value/n.value.rowsPerPage))),g=s(()=>h.value===0?!0:n.value.page>=v.value),_=s(()=>(i.rowsPerPageOptions.includes(t.value.rowsPerPage)?i.rowsPerPageOptions:[t.value.rowsPerPage].concat(i.rowsPerPageOptions)).map(S=>({label:S===0?d.lang.table.allRows:""+S,value:S})));ne(v,(b,S)=>{if(b===S)return;const B=n.value.page;b&&!B?a({page:1}):b<B&&a({page:b})});function p(){a({page:1})}function k(){const{page:b}=n.value;b>1&&a({page:b-1})}function w(){const{page:b,rowsPerPage:S}=n.value;h.value>0&&b*S<f.value&&a({page:b+1})}function y(){a({page:v.value})}return i["onUpdate:pagination"]!==void 0&&u("update:pagination",{...n.value}),{firstRowIndex:c,lastRowIndex:h,isFirstPage:m,isLastPage:g,pagesNumber:v,computedRowsPerPageOptions:_,computedRowsNumber:f,firstPage:p,prevPage:k,nextPage:w,lastPage:y}}var $m={selection:{type:String,default:"none",validator:e=>["single","multiple","none"].includes(e)},selected:{type:Array,default:()=>[]}},Bm=["update:selected","selection"];function Pm(e,t,n,l){const a=s(()=>{const g={};return e.selected.map(l.value).forEach(_=>{g[_]=!0}),g}),o=s(()=>e.selection!=="none"),i=s(()=>e.selection==="single"),u=s(()=>e.selection==="multiple"),d=s(()=>n.value.length!==0&&n.value.every(g=>a.value[l.value(g)]===!0)),f=s(()=>d.value!==!0&&n.value.some(g=>a.value[l.value(g)]===!0)),c=s(()=>e.selected.length);function h(g){return a.value[g]===!0}function m(){t("update:selected",[])}function v(g,_,p,k){t("selection",{rows:_,added:p,keys:g,evt:k});const w=i.value===!0?p===!0?_:[]:p===!0?e.selected.concat(_):e.selected.filter(y=>g.includes(l.value(y))===!1);t("update:selected",w)}return{hasSelectionMode:o,singleSelection:i,multipleSelection:u,allRowsSelected:d,someRowsSelected:f,rowsSelectedNumber:c,isRowSelected:h,clearSelection:m,updateSelection:v}}function Bi(e){return Array.isArray(e)?e.slice():[]}var Em={expanded:Array},Lm=["update:expanded"];function Am(e,t){const n=O(Bi(e.expanded));ne(()=>e.expanded,i=>{n.value=Bi(i)});function l(i){return n.value.includes(i)}function a(i){e.expanded!==void 0?t("update:expanded",i):n.value=i}function o(i,u){const d=n.value.slice(),f=d.indexOf(i);u===!0?f===-1&&(d.push(i),a(d)):f!==-1&&(d.splice(f,1),a(d))}return{isRowExpanded:l,setExpanded:a,updateExpanded:o}}var zm={visibleColumns:Array};function Om(e,t,n){const l=s(()=>{if(e.columns!==void 0)return e.columns;const u=e.rows[0];return u!==void 0?Object.keys(u).map(d=>({name:d,label:d.toUpperCase(),field:d,align:Rn(u[d])?"right":"left",sortable:!0})):[]}),a=s(()=>{const{sortBy:u,descending:d}=t.value;return(e.visibleColumns!==void 0?l.value.filter(c=>c.required===!0||e.visibleColumns.includes(c.name)===!0):l.value).map(c=>{const h=c.align||"right",m=`text-${h}`;return{...c,align:h,__iconClass:`q-table__sort-icon q-table__sort-icon--${h}`,__thClass:m+(c.headerClasses!==void 0?" "+c.headerClasses:"")+(c.sortable===!0?" sortable":"")+(c.name===u?` sorted ${d===!0?"sort-desc":""}`:""),__tdStyle:c.style!==void 0?typeof c.style!="function"?()=>c.style:c.style:()=>null,__tdClass:c.classes!==void 0?typeof c.classes!="function"?()=>m+" "+c.classes:v=>m+" "+c.classes(v):()=>m}})}),o=s(()=>{const u={};return a.value.forEach(d=>{u[d.name]=d}),u}),i=s(()=>e.tableColspan!==void 0?e.tableColspan:a.value.length+(n.value===!0?1:0));return{colList:l,computedCols:a,computedColsMap:o,computedColspan:i}}var ll="q-table__bottom row items-center",Is={};Os.forEach(e=>{Is[e]={}});var m0=te({name:"QTable",props:{rows:{type:Array,required:!0},rowKey:{type:[String,Function],default:"id"},columns:Array,loading:Boolean,iconFirstPage:String,iconPrevPage:String,iconNextPage:String,iconLastPage:String,title:String,hideHeader:Boolean,grid:Boolean,gridHeader:Boolean,dense:Boolean,flat:Boolean,bordered:Boolean,square:Boolean,separator:{type:String,default:"horizontal",validator:e=>["horizontal","vertical","cell","none"].includes(e)},wrapCells:Boolean,virtualScroll:Boolean,virtualScrollTarget:{},...Is,noDataLabel:String,noResultsLabel:String,loadingLabel:String,selectedRowsLabel:Function,rowsPerPageLabel:String,paginationLabel:Function,color:{type:String,default:"grey-8"},titleClass:[String,Array,Object],tableStyle:[String,Array,Object],tableClass:[String,Array,Object],tableHeaderStyle:[String,Array,Object],tableHeaderClass:[String,Array,Object],cardContainerClass:[String,Array,Object],cardContainerStyle:[String,Array,Object],cardStyle:[String,Array,Object],cardClass:[String,Array,Object],hideBottom:Boolean,hideSelectedBanner:Boolean,hideNoData:Boolean,hidePagination:Boolean,onRowClick:Function,onRowDblclick:Function,onRowContextmenu:Function,...Ke,...or,...zm,...xm,...qm,...Em,...$m,..._m},emits:["request","virtualScroll",...rr,...Lm,...Bm],setup(e,{slots:t,emit:n}){const l=ve(),{proxy:{$q:a}}=l,o=We(e,a),{inFullscreen:i,toggleFullscreen:u}=ir(),d=s(()=>typeof e.rowKey=="function"?e.rowKey:ee=>ee[e.rowKey]),f=O(null),c=O(null),h=s(()=>e.grid!==!0&&e.virtualScroll===!0),m=s(()=>" q-table__card"+(o.value===!0?" q-table__card--dark q-dark":"")+(e.square===!0?" q-table--square":"")+(e.flat===!0?" q-table--flat":"")+(e.bordered===!0?" q-table--bordered":"")),v=s(()=>`q-table__container q-table--${e.separator}-separator column no-wrap`+(e.grid===!0?" q-table--grid":m.value)+(o.value===!0?" q-table--dark":"")+(e.dense===!0?" q-table--dense":"")+(e.wrapCells===!1?" q-table--no-wrap":"")+(i.value===!0?" fullscreen scroll":"")),g=s(()=>v.value+(e.loading===!0?" q-table--loading":""));ne(()=>e.tableStyle+e.tableClass+e.tableHeaderStyle+e.tableHeaderClass+v.value,()=>{h.value===!0&&c.value!==null&&c.value.reset()});const{innerPagination:_,computedPagination:p,isServerSide:k,requestServerInteraction:w,setPagination:y}=Tm(l,J),{computedFilterMethod:b}=Cm(e,y),{isRowExpanded:S,setExpanded:B,updateExpanded:L}=Am(e,n),R=s(()=>{let ee=e.rows;if(k.value===!0||ee.length===0)return ee;const{sortBy:ye,descending:qe}=p.value;return e.filter&&(ee=b.value(ee,e.filter,I.value,J)),Q.value!==null&&(ee=oe.value(e.rows===ee?ee.slice():ee,ye,qe)),ee}),A=s(()=>R.value.length),P=s(()=>{let ee=R.value;if(k.value===!0)return ee;const{rowsPerPage:ye}=p.value;return ye!==0&&(D.value===0&&e.rows!==ee?ee.length>le.value&&(ee=ee.slice(0,le.value)):ee=ee.slice(D.value,le.value)),ee}),{hasSelectionMode:z,singleSelection:T,multipleSelection:x,allRowsSelected:q,someRowsSelected:U,rowsSelectedNumber:W,isRowSelected:E,clearSelection:V,updateSelection:j}=Pm(e,n,P,d),{colList:ue,computedCols:I,computedColsMap:C,computedColspan:K}=Om(e,p,z),{columnToSort:Q,computedSortMethod:oe,sort:M}=Sm(e,p,ue,y),{firstRowIndex:D,lastRowIndex:le,isFirstPage:H,isLastPage:de,pagesNumber:xe,computedRowsPerPageOptions:Y,computedRowsNumber:se,firstPage:me,prevPage:we,nextPage:Me,lastPage:ce}=Mm(l,_,p,k,y,A),$e=s(()=>P.value.length===0),ze=s(()=>{const ee={};return Os.forEach(ye=>{ee[ye]=e[ye]}),ee.virtualScrollItemSize===void 0&&(ee.virtualScrollItemSize=e.dense===!0?28:48),ee});function Oe(){h.value===!0&&c.value.reset()}function re(){if(e.grid===!0)return Zt();const ee=e.hideHeader!==!0?ke:null;if(h.value===!0){const qe=t["top-row"],Le=t["bottom-row"],Ve={default:st=>Be(st.item,t.body,st.index)};if(qe!==void 0){const st=r("tbody",qe({cols:I.value}));Ve.before=ee===null?()=>st:()=>[ee()].concat(st)}else ee!==null&&(Ve.before=ee);return Le!==void 0&&(Ve.after=()=>r("tbody",Le({cols:I.value}))),r(pm,{ref:c,class:e.tableClass,style:e.tableStyle,...ze.value,scrollTarget:e.virtualScrollTarget,items:P.value,type:"__qtable",tableColspan:K.value,onVirtualScroll:G},Ve)}const ye=[Ee()];return ee!==null&&ye.unshift(ee()),Ds({class:["q-table__middle scroll",e.tableClass],style:e.tableStyle},ye)}function fe(ee,ye){if(c.value!==null){c.value.scrollTo(ee,ye);return}ee=parseInt(ee,10);const qe=f.value.querySelector(`tbody tr:nth-of-type(${ee+1})`);if(qe!==null){const Le=f.value.querySelector(".q-table__middle.scroll"),Ve=qe.offsetTop-e.virtualScrollStickySizeStart,st=Ve<Le.scrollTop?"decrease":"increase";Le.scrollTop=Ve,n("virtualScroll",{index:ee,from:0,to:_.value.rowsPerPage-1,direction:st})}}function G(ee){n("virtualScroll",ee)}function be(){return[r(Rv,{class:"q-table__linear-progress",color:e.color,dark:o.value,indeterminate:!0,trackColor:"transparent"})]}function Be(ee,ye,qe){const Le=d.value(ee),Ve=E(Le);if(ye!==void 0)return ye(Pe({key:Le,row:ee,pageIndex:qe,__trClass:Ve?"selected":""}));const st=t["body-cell"],$=I.value.map(Z=>{const ge=t[`body-cell-${Z.name}`],he=ge!==void 0?ge:st;return he!==void 0?he(Ge({key:Le,row:ee,pageIndex:qe,col:Z})):r("td",{class:Z.__tdClass(ee),style:Z.__tdStyle(ee)},J(Z,ee))});if(z.value===!0){const Z=t["body-selection"],ge=Z!==void 0?Z(nt({key:Le,row:ee,pageIndex:qe})):[r($a,{modelValue:Ve,color:e.color,dark:o.value,dense:e.dense,"onUpdate:modelValue":(he,Ue)=>{j([Le],[ee],he,Ue)}})];$.unshift(r("td",{class:"q-table--col-auto-width"},ge))}const N={key:Le,class:{selected:Ve}};return e.onRowClick!==void 0&&(N.class["cursor-pointer"]=!0,N.onClick=Z=>{n("rowClick",Z,ee,qe)}),e.onRowDblclick!==void 0&&(N.class["cursor-pointer"]=!0,N.onDblclick=Z=>{n("rowDblclick",Z,ee,qe)}),e.onRowContextmenu!==void 0&&(N.class["cursor-pointer"]=!0,N.onContextmenu=Z=>{n("rowContextmenu",Z,ee,qe)}),r("tr",N,$)}function Ee(){const ee=t.body,ye=t["top-row"],qe=t["bottom-row"];let Le=P.value.map((Ve,st)=>Be(Ve,ee,st));return ye!==void 0&&(Le=ye({cols:I.value}).concat(Le)),qe!==void 0&&(Le=Le.concat(qe({cols:I.value}))),r("tbody",Le)}function Pe(ee){return at(ee),ee.cols=ee.cols.map(ye=>St({...ye},"value",()=>J(ye,ee.row))),ee}function Ge(ee){return at(ee),St(ee,"value",()=>J(ee.col,ee.row)),ee}function nt(ee){return at(ee),ee}function at(ee){Object.assign(ee,{cols:I.value,colsMap:C.value,sort:M,rowIndex:D.value+ee.pageIndex,color:e.color,dark:o.value,dense:e.dense}),z.value===!0&&St(ee,"selected",()=>E(ee.key),(ye,qe)=>{j([ee.key],[ee.row],ye,qe)}),St(ee,"expand",()=>S(ee.key),ye=>{L(ee.key,ye)})}function J(ee,ye){const qe=typeof ee.field=="function"?ee.field(ye):ye[ee.field];return ee.format!==void 0?ee.format(qe,ye):qe}const ie=s(()=>({pagination:p.value,pagesNumber:xe.value,isFirstPage:H.value,isLastPage:de.value,firstPage:me,prevPage:we,nextPage:Me,lastPage:ce,inFullscreen:i.value,toggleFullscreen:u}));function X(){const ee=t.top,ye=t["top-left"],qe=t["top-right"],Le=t["top-selection"],Ve=z.value===!0&&Le!==void 0&&W.value>0,st="q-table__top relative-position row items-center";if(ee!==void 0)return r("div",{class:st},[ee(ie.value)]);let $;if(Ve===!0?$=Le(ie.value).slice():($=[],ye!==void 0?$.push(r("div",{class:"q-table__control"},[ye(ie.value)])):e.title&&$.push(r("div",{class:"q-table__control"},[r("div",{class:["q-table__title",e.titleClass]},e.title)]))),qe!==void 0&&($.push(r("div",{class:"q-table__separator col"})),$.push(r("div",{class:"q-table__control"},[qe(ie.value)]))),$.length!==0)return r("div",{class:st},$)}const ae=s(()=>U.value===!0?null:q.value);function ke(){const ee=Re();return e.loading===!0&&t.loading===void 0&&ee.push(r("tr",{class:"q-table__progress"},[r("th",{class:"relative-position",colspan:K.value},be())])),r("thead",ee)}function Re(){const ee=t.header,ye=t["header-cell"];if(ee!==void 0)return ee(_e({header:!0})).slice();const qe=I.value.map(Le=>{const Ve=t[`header-cell-${Le.name}`],st=Ve!==void 0?Ve:ye,$=_e({col:Le});return st!==void 0?st($):r(hm,{key:Le.name,props:$},()=>Le.label)});if(T.value===!0&&e.grid!==!0)qe.unshift(r("th",{class:"q-table--col-auto-width"}," "));else if(x.value===!0){const Le=t["header-selection"],Ve=Le!==void 0?Le(_e({})):[r($a,{color:e.color,modelValue:ae.value,dark:o.value,dense:e.dense,"onUpdate:modelValue":He})];qe.unshift(r("th",{class:"q-table--col-auto-width"},Ve))}return[r("tr",{class:e.tableHeaderClass,style:e.tableHeaderStyle},qe)]}function _e(ee){return Object.assign(ee,{cols:I.value,sort:M,colsMap:C.value,color:e.color,dark:o.value,dense:e.dense}),x.value===!0&&St(ee,"selected",()=>ae.value,He),ee}function He(ee){U.value===!0&&(ee=!1),j(P.value.map(d.value),P.value,ee)}const ot=s(()=>{const ee=[e.iconFirstPage||a.iconSet.table.firstPage,e.iconPrevPage||a.iconSet.table.prevPage,e.iconNextPage||a.iconSet.table.nextPage,e.iconLastPage||a.iconSet.table.lastPage];return a.lang.rtl===!0?ee.reverse():ee});function ut(){if(e.hideBottom===!0)return;if($e.value===!0){if(e.hideNoData===!0)return;const qe=e.loading===!0?e.loadingLabel||a.lang.table.loading:e.filter?e.noResultsLabel||a.lang.table.noResults:e.noDataLabel||a.lang.table.noData,Le=t["no-data"],Ve=Le!==void 0?[Le({message:qe,icon:a.iconSet.table.warning,filter:e.filter})]:[r(je,{class:"q-table__bottom-nodata-icon",name:a.iconSet.table.warning}),qe];return r("div",{class:ll+" q-table__bottom--nodata"},Ve)}const ee=t.bottom;if(ee!==void 0)return r("div",{class:ll},[ee(ie.value)]);const ye=e.hideSelectedBanner!==!0&&z.value===!0&&W.value>0?[r("div",{class:"q-table__control"},[r("div",[(e.selectedRowsLabel||a.lang.table.selectedRecords)(W.value)])])]:[];if(e.hidePagination!==!0)return r("div",{class:ll+" justify-end"},At(ye));if(ye.length!==0)return r("div",{class:ll},ye)}function Bt(ee){y({page:1,rowsPerPage:ee.value})}function At(ee){let ye;const{rowsPerPage:qe}=p.value,Le=e.paginationLabel||a.lang.table.pagination,Ve=t.pagination,st=e.rowsPerPageOptions.length>1;if(ee.push(r("div",{class:"q-table__separator col"})),st===!0&&ee.push(r("div",{class:"q-table__control"},[r("span",{class:"q-table__bottom-item"},[e.rowsPerPageLabel||a.lang.table.recordsPerPage]),r(Hv,{class:"q-table__select inline q-table__bottom-item",color:e.color,modelValue:qe,options:Y.value,displayValue:qe===0?a.lang.table.allRows:qe,dark:o.value,borderless:!0,dense:!0,optionsDense:!0,optionsCover:!0,"onUpdate:modelValue":Bt})])),Ve!==void 0)ye=Ve(ie.value);else if(ye=[r("span",qe!==0?{class:"q-table__bottom-item"}:{},[qe?Le(D.value+1,Math.min(le.value,se.value),se.value):Le(1,A.value,se.value)])],qe!==0&&xe.value>1){const $={color:e.color,round:!0,dense:!0,flat:!0};e.dense===!0&&($.size="sm"),xe.value>2&&ye.push(r(Xe,{key:"pgFirst",...$,icon:ot.value[0],disable:H.value,onClick:me})),ye.push(r(Xe,{key:"pgPrev",...$,icon:ot.value[1],disable:H.value,onClick:we}),r(Xe,{key:"pgNext",...$,icon:ot.value[2],disable:de.value,onClick:Me})),xe.value>2&&ye.push(r(Xe,{key:"pgLast",...$,icon:ot.value[3],disable:de.value,onClick:ce}))}return ee.push(r("div",{class:"q-table__control"},ye)),ee}function en(){const ee=e.gridHeader===!0?[r("table",{class:"q-table"},[ke()])]:e.loading===!0&&t.loading===void 0?be():void 0;return r("div",{class:"q-table__middle"},ee)}function Zt(){const ee=t.item!==void 0?t.item:ye=>{const qe=ye.cols.map(Ve=>r("div",{class:"q-table__grid-item-row"},[r("div",{class:"q-table__grid-item-title"},[Ve.label]),r("div",{class:"q-table__grid-item-value"},[Ve.value])]));if(z.value===!0){const Ve=t["body-selection"],st=Ve!==void 0?Ve(ye):[r($a,{modelValue:ye.selected,color:e.color,dark:o.value,dense:e.dense,"onUpdate:modelValue":($,N)=>{j([ye.key],[ye.row],$,N)}})];qe.unshift(r("div",{class:"q-table__grid-item-row"},st),r(Dn,{dark:o.value}))}const Le={class:["q-table__grid-item-card"+m.value,e.cardClass],style:e.cardStyle};return(e.onRowClick!==void 0||e.onRowDblclick!==void 0)&&(Le.class[0]+=" cursor-pointer",e.onRowClick!==void 0&&(Le.onClick=Ve=>{n("RowClick",Ve,ye.row,ye.pageIndex)}),e.onRowDblclick!==void 0&&(Le.onDblclick=Ve=>{n("RowDblclick",Ve,ye.row,ye.pageIndex)})),r("div",{class:"q-table__grid-item col-xs-12 col-sm-6 col-md-4 col-lg-3"+(ye.selected===!0?" q-table__grid-item--selected":"")},[r("div",Le,qe)])};return r("div",{class:["q-table__grid-content row",e.cardContainerClass],style:e.cardContainerStyle},P.value.map((ye,qe)=>ee(Pe({key:d.value(ye),row:ye,pageIndex:qe}))))}return Object.assign(l.proxy,{requestServerInteraction:w,setPagination:y,firstPage:me,prevPage:we,nextPage:Me,lastPage:ce,isRowSelected:E,clearSelection:V,isRowExpanded:S,setExpanded:B,sort:M,resetVirtualScroll:Oe,scrollTo:fe,getCellValue:J}),Ji(l.proxy,{filteredSortedRows:()=>R.value,computedRows:()=>P.value,computedRowsNumber:()=>se.value}),()=>{const ee=[X()],ye={ref:f,class:g.value};return e.grid===!0?ee.push(en()):Object.assign(ye,{class:[ye.class,e.cardClass],style:e.cardStyle}),ee.push(re(),ut()),e.loading===!0&&t.loading!==void 0&&ee.push(t.loading()),r("div",ye,ee)}}}),g0=te({name:"QTr",props:{props:Object,noHover:Boolean},setup(e,{slots:t}){const n=s(()=>"q-tr"+(e.props===void 0||e.props.header===!0?"":" "+e.props.__trClass)+(e.noHover===!0?" q-tr--no-hover":""));return()=>r("tr",{class:n.value},Ce(t.default))}}),h0=te({name:"QTd",props:{props:Object,autoWidth:Boolean,noHover:Boolean},setup(e,{slots:t}){const n=ve(),l=s(()=>"q-td"+(e.autoWidth===!0?" q-table--col-auto-width":"")+(e.noHover===!0?" q-td--no-hover":"")+" ");return()=>{if(e.props===void 0)return r("td",{class:l.value},Ce(t.default));const a=n.vnode.key,o=(e.props.colsMap!==void 0?e.props.colsMap[a]:null)||e.props.col;if(o===void 0)return;const{row:i}=e.props;return r("td",{class:l.value+o.__tdClass(i),style:o.__tdStyle(i)},Ce(t.default))}}}),b0=te({name:"QRouteTab",props:{...Na,...ts},emits:es,setup(e,{slots:t,emit:n}){const l=zl({useDisableForRouterLinkProps:!1}),{renderTab:a,$tabs:o}=ns(e,t,n,{exact:s(()=>e.exact),...l});return ne(()=>`${e.name} | ${e.exact} | ${(l.resolvedLink.value||{}).href}`,()=>{o.verifyRouteModel()}),()=>a(l.linkTag.value,l.linkAttrs.value)}});function Rm(e,t){if(e.hour!==null){if(e.minute===null)return"minute";if(t===!0&&e.second===null)return"second"}return"hour"}function Fm(){const e=new Date;return{hour:e.getHours(),minute:e.getMinutes(),second:e.getSeconds(),millisecond:e.getMilliseconds()}}var y0=te({name:"QTime",props:{...Ke,...Ht,...Ml,modelValue:{required:!0,validator:e=>typeof e=="string"||e===null},mask:{...Ml.mask,default:null},format24h:{type:Boolean,default:null},defaultDate:{type:String,validator:e=>/^-?[\d]+\/[0-1]\d\/[0-3]\d$/.test(e)},options:Function,hourOptions:Array,minuteOptions:Array,secondOptions:Array,withSeconds:Boolean,nowBtn:Boolean},emits:rs,setup(e,{slots:t,emit:n}){const l=ve(),{$q:a}=l.proxy,o=We(e,a),{tabindex:i,headerClass:u,getLocale:d,getCurrentDate:f}=is(e,a),c=Ka(e),h=wn(c);let m,v;const g=O(null),_=s(()=>oe()),p=s(()=>d()),k=s(()=>M()),w=aa(e.modelValue,_.value,p.value,e.calendar,k.value),y=O(Rm(w)),b=O(w),S=O(w.hour===null||w.hour<12),B=s(()=>`q-time q-time--${e.landscape===!0?"landscape":"portrait"}`+(o.value===!0?" q-time--dark q-dark":"")+(e.disable===!0?" disabled":e.readonly===!0?" q-time--readonly":"")+(e.bordered===!0?" q-time--bordered":"")+(e.square===!0?" q-time--square no-border-radius":"")+(e.flat===!0?" q-time--flat no-shadow":"")),L=s(()=>{const J=b.value;return{hour:J.hour===null?"--":R.value===!0?Ye(J.hour):String(S.value===!0?J.hour===0?12:J.hour:J.hour>12?J.hour-12:J.hour),minute:J.minute===null?"--":Ye(J.minute),second:J.second===null?"--":Ye(J.second)}}),R=s(()=>e.format24h!==null?e.format24h:a.lang.date.format24h),A=s(()=>{const J=y.value==="hour",ie=J===!0?12:60,X=b.value[y.value];let ke=`rotate(${Math.round(X*(360/ie))-180}deg) translateX(-50%)`;return J===!0&&R.value===!0&&b.value.hour>=12&&(ke+=" scale(.7)"),{transform:ke}}),P=s(()=>b.value.hour!==null),z=s(()=>P.value===!0&&b.value.minute!==null),T=s(()=>e.hourOptions!==void 0?J=>e.hourOptions.includes(J):e.options!==void 0?J=>e.options(J,null,null):null),x=s(()=>e.minuteOptions!==void 0?J=>e.minuteOptions.includes(J):e.options!==void 0?J=>e.options(b.value.hour,J,null):null),q=s(()=>e.secondOptions!==void 0?J=>e.secondOptions.includes(J):e.options!==void 0?J=>e.options(b.value.hour,b.value.minute,J):null),U=s(()=>{if(T.value===null)return null;const J=C(0,11,T.value),ie=C(12,11,T.value);return{am:J,pm:ie,values:J.values.concat(ie.values)}}),W=s(()=>x.value!==null?C(0,59,x.value):null),E=s(()=>q.value!==null?C(0,59,q.value):null),V=s(()=>{switch(y.value){case"hour":return U.value;case"minute":return W.value;case"second":return E.value}}),j=s(()=>{let J,ie,X=0,ae=1;const ke=V.value!==null?V.value.values:void 0;y.value==="hour"?R.value===!0?(J=0,ie=23):(J=0,ie=11,S.value===!1&&(X=12)):(J=0,ie=55,ae=5);const Re=[];for(let _e=J,He=J;_e<=ie;_e+=ae,He++){const ot=_e+X,ut=ke!==void 0&&ke.includes(ot)===!1,Bt=y.value==="hour"&&_e===0?R.value===!0?"00":"12":_e;Re.push({val:ot,index:He,disable:ut,label:Bt})}return Re}),ue=s(()=>[[Ft,H,void 0,{stop:!0,prevent:!0,mouse:!0}]]);ne(()=>e.modelValue,J=>{const ie=aa(J,_.value,p.value,e.calendar,k.value);(ie.dateHash!==b.value.dateHash||ie.timeHash!==b.value.timeHash)&&(b.value=ie,ie.hour===null?y.value="hour":S.value=ie.hour<12)}),ne([_,p],()=>{Qe(()=>{Ge()})});function I(){const J={...f(),...Fm()};Ge(J),Object.assign(b.value,J),y.value="hour"}function C(J,ie,X){const ae=Array.apply(null,{length:ie+1}).map((ke,Re)=>{const _e=Re+J;return{index:_e,val:X(_e)===!0}}).filter(ke=>ke.val===!0).map(ke=>ke.index);return{min:ae[0],max:ae[ae.length-1],values:ae,threshold:ie+1}}function K(J,ie,X){const ae=Math.abs(J-ie);return Math.min(ae,X-ae)}function Q(J,{min:ie,max:X,values:ae,threshold:ke}){if(J===ie)return ie;if(J<ie||J>X)return K(J,ie,ke)<=K(J,X,ke)?ie:X;const Re=ae.findIndex(ot=>J<=ot),_e=ae[Re-1],He=ae[Re];return J-_e<=He-J?_e:He}function oe(){return e.calendar!=="persian"&&e.mask!==null?e.mask:`HH:mm${e.withSeconds===!0?":ss":""}`}function M(){if(typeof e.defaultDate!="string"){const J=f(!0);return J.dateHash=nn(J),J}return aa(e.defaultDate,"YYYY/MM/DD",void 0,e.calendar)}function D(){return on(l)===!0||V.value!==null&&(V.value.values.length===0||y.value==="hour"&&R.value!==!0&&U.value[S.value===!0?"am":"pm"].values.length===0)}function le(){const J=g.value,{top:ie,left:X,width:ae}=J.getBoundingClientRect(),ke=ae/2;return{top:ie+ke,left:X+ke,dist:ke*.7}}function H(J){if(D()!==!0){if(J.isFirst===!0){m=le(),v=xe(J.evt,m);return}v=xe(J.evt,m,v),J.isFinal===!0&&(m=!1,v=null,de())}}function de(){y.value==="hour"?y.value="minute":e.withSeconds&&y.value==="minute"&&(y.value="second")}function xe(J,ie,X){const ae=$t(J),ke=Math.abs(ae.top-ie.top),Re=Math.sqrt(Math.pow(Math.abs(ae.top-ie.top),2)+Math.pow(Math.abs(ae.left-ie.left),2));let _e,He=Math.asin(ke/Re)*(180/Math.PI);if(ae.top<ie.top?He=ie.left<ae.left?90-He:270+He:He=ie.left<ae.left?He+90:270-He,y.value==="hour"){if(_e=He/30,U.value!==null){const ot=R.value!==!0?S.value===!0:U.value.am.values.length!==0&&U.value.pm.values.length!==0?Re>=ie.dist:U.value.am.values.length!==0;_e=Q(_e+(ot===!0?0:12),U.value[ot===!0?"am":"pm"])}else _e=Math.round(_e),R.value===!0?Re<ie.dist?_e<12&&(_e+=12):_e===12&&(_e=0):S.value===!0&&_e===12?_e=0:S.value===!1&&_e!==12&&(_e+=12);R.value===!0&&(S.value=_e<12)}else _e=Math.round(He/6)%60,y.value==="minute"&&W.value!==null?_e=Q(_e,W.value):y.value==="second"&&E.value!==null&&(_e=Q(_e,E.value));return X!==_e&&G[y.value](_e),_e}const Y={hour(){y.value="hour"},minute(){y.value="minute"},second(){y.value="second"}};function se(J){J.keyCode===13&&be()}function me(J){J.keyCode===13&&Be()}function we(J){D()!==!0&&(a.platform.is.desktop!==!0&&xe(J,le()),de())}function Me(J){D()!==!0&&xe(J,le())}function ce(J){if(J.keyCode===13)y.value="hour";else if([37,39].includes(J.keyCode)){const ie=J.keyCode===37?-1:1;if(U.value!==null){const X=R.value===!0?U.value.values:U.value[S.value===!0?"am":"pm"].values;if(X.length===0)return;if(b.value.hour===null)Oe(X[0]);else{const ae=(X.length+X.indexOf(b.value.hour)+ie)%X.length;Oe(X[ae])}}else{const X=R.value===!0?24:12,ae=R.value!==!0&&S.value===!1?12:0,ke=b.value.hour===null?-ie:b.value.hour;Oe(ae+(24+ke+ie)%X)}}}function $e(J){if(J.keyCode===13)y.value="minute";else if([37,39].includes(J.keyCode)){const ie=J.keyCode===37?-1:1;if(W.value!==null){const X=W.value.values;if(X.length===0)return;if(b.value.minute===null)re(X[0]);else{const ae=(X.length+X.indexOf(b.value.minute)+ie)%X.length;re(X[ae])}}else{const X=b.value.minute===null?-ie:b.value.minute;re((60+X+ie)%60)}}}function ze(J){if(J.keyCode===13)y.value="second";else if([37,39].includes(J.keyCode)){const ie=J.keyCode===37?-1:1;if(E.value!==null){const X=E.value.values;if(X.length===0)return;if(b.value.seconds===null)fe(X[0]);else{const ae=(X.length+X.indexOf(b.value.second)+ie)%X.length;fe(X[ae])}}else{const X=b.value.second===null?-ie:b.value.second;fe((60+X+ie)%60)}}}function Oe(J){b.value.hour!==J&&(b.value.hour=J,Pe())}function re(J){b.value.minute!==J&&(b.value.minute=J,Pe())}function fe(J){b.value.second!==J&&(b.value.second=J,Pe())}const G={hour:Oe,minute:re,second:fe};function be(){S.value===!1&&(S.value=!0,b.value.hour!==null&&(b.value.hour-=12,Pe()))}function Be(){S.value===!0&&(S.value=!1,b.value.hour!==null&&(b.value.hour+=12,Pe()))}function Ee(J){const ie=e.modelValue;y.value!==J&&ie!==void 0&&ie!==null&&ie!==""&&typeof ie!="string"&&(y.value=J)}function Pe(){if(T.value!==null&&T.value(b.value.hour)!==!0){b.value=aa(),Ee("hour");return}if(x.value!==null&&x.value(b.value.minute)!==!0){b.value.minute=null,b.value.second=null,Ee("minute");return}if(e.withSeconds===!0&&q.value!==null&&q.value(b.value.second)!==!0){b.value.second=null,Ee("second");return}b.value.hour===null||b.value.minute===null||e.withSeconds===!0&&b.value.second===null||Ge()}function Ge(J){const ie=Object.assign({...b.value},J),X=e.calendar==="persian"?Ye(ie.hour)+":"+Ye(ie.minute)+(e.withSeconds===!0?":"+Ye(ie.second):""):gr(new Date(ie.year,ie.month===null?null:ie.month-1,ie.day,ie.hour,ie.minute,ie.second,ie.millisecond),_.value,p.value,ie.year,ie.timezoneOffset);ie.changed=X!==e.modelValue,n("update:modelValue",X,ie)}function nt(){const J=[r("div",{class:"q-time__link "+(y.value==="hour"?"q-time__link--active":"cursor-pointer"),tabindex:i.value,onClick:Y.hour,onKeyup:ce},L.value.hour),r("div",":"),r("div",P.value===!0?{class:"q-time__link "+(y.value==="minute"?"q-time__link--active":"cursor-pointer"),tabindex:i.value,onKeyup:$e,onClick:Y.minute}:{class:"q-time__link"},L.value.minute)];e.withSeconds===!0&&J.push(r("div",":"),r("div",z.value===!0?{class:"q-time__link "+(y.value==="second"?"q-time__link--active":"cursor-pointer"),tabindex:i.value,onKeyup:ze,onClick:Y.second}:{class:"q-time__link"},L.value.second));const ie=[r("div",{class:"q-time__header-label row items-center no-wrap",dir:"ltr"},J)];return R.value===!1&&ie.push(r("div",{class:"q-time__header-ampm column items-between no-wrap"},[r("div",{class:"q-time__link "+(S.value===!0?"q-time__link--active":"cursor-pointer"),tabindex:i.value,onClick:be,onKeyup:se},"AM"),r("div",{class:"q-time__link "+(S.value!==!0?"q-time__link--active":"cursor-pointer"),tabindex:i.value,onClick:Be,onKeyup:me},"PM")])),r("div",{class:"q-time__header flex flex-center no-wrap "+u.value},ie)}function at(){const J=b.value[y.value];return r("div",{class:"q-time__content col relative-position"},[r(_t,{name:"q-transition--scale"},()=>r("div",{key:"clock"+y.value,class:"q-time__container-parent absolute-full"},[r("div",{ref:g,class:"q-time__container-child fit overflow-hidden"},[Ut(r("div",{class:"q-time__clock cursor-pointer non-selectable",onClick:we,onMousedown:Me},[r("div",{class:"q-time__clock-circle fit"},[r("div",{class:"q-time__clock-pointer"+(b.value[y.value]===null?" hidden":e.color!==void 0?` text-${e.color}`:""),style:A.value}),j.value.map(ie=>r("div",{class:`q-time__clock-position row flex-center q-time__clock-pos-${ie.index}`+(ie.val===J?" q-time__clock-position--active "+u.value:ie.disable===!0?" q-time__clock-position--disable":"")},[r("span",ie.label)]))])]),ue.value)])])),e.nowBtn===!0?r(Xe,{class:"q-time__now-button absolute",icon:a.iconSet.datetime.now,unelevated:!0,size:"sm",round:!0,color:e.color,textColor:e.textColor,tabindex:i.value,onClick:I}):null])}return l.proxy.setNow=I,()=>{const J=[at()],ie=Ce(t.default);return ie!==void 0&&J.push(r("div",{class:"q-time__actions"},ie)),e.name!==void 0&&e.disable!==!0&&h(J,"push"),r("div",{class:B.value,tabindex:-1},[nt(),r("div",{class:"q-time__main col overflow-auto"},J)])}}}),p0=te({name:"QTimeline",props:{...Ke,color:{type:String,default:"primary"},side:{type:String,default:"right",validator:e=>["left","right"].includes(e)},layout:{type:String,default:"dense",validator:e=>["dense","comfortable","loose"].includes(e)}},setup(e,{slots:t}){const n=ve(),l=We(e,n.proxy.$q);yn(iu,e);const a=s(()=>`q-timeline q-timeline--${e.layout} q-timeline--${e.layout}--${e.side}`+(l.value===!0?" q-timeline--dark":""));return()=>r("ul",{class:a.value},Ce(t.default))}}),w0=te({name:"QTimelineEntry",props:{heading:Boolean,tag:{type:String,default:"h3"},side:{type:String,default:"right",validator:e=>["left","right"].includes(e)},icon:String,avatar:String,color:String,title:String,subtitle:String,body:String},setup(e,{slots:t}){const n=Et(iu,Je);if(n===Je)return console.error("QTimelineEntry needs to be child of QTimeline"),Je;const l=s(()=>`q-timeline__entry q-timeline__entry--${e.side}`+(e.icon!==void 0||e.avatar!==void 0?" q-timeline__entry--icon":"")),a=s(()=>`q-timeline__dot text-${e.color||n.color}`),o=s(()=>n.layout==="comfortable"&&n.side==="left");return()=>{const i=Ha(t.default,[]);if(e.body!==void 0&&i.unshift(e.body),e.heading===!0){const f=[r("div"),r("div"),r(e.tag,{class:"q-timeline__heading-title"},i)];return r("div",{class:"q-timeline__heading"},o.value===!0?f.reverse():f)}let u;e.icon!==void 0?u=[r(je,{class:"row items-center justify-center",name:e.icon})]:e.avatar!==void 0&&(u=[r("img",{class:"q-timeline__dot-img",src:e.avatar})]);const d=[r("div",{class:"q-timeline__subtitle"},[r("span",{},Ce(t.subtitle,[e.subtitle]))]),r("div",{class:a.value},u),r("div",{class:"q-timeline__content"},[r("h6",{class:"q-timeline__title"},Ce(t.title,[e.title]))].concat(i))];return r("li",{class:l.value},o.value===!0?d.reverse():d)}}}),_0=te({name:"QToolbar",props:{inset:Boolean},setup(e,{slots:t}){const n=s(()=>"q-toolbar row no-wrap items-center"+(e.inset===!0?" q-toolbar--inset":""));return()=>r("div",{class:n.value,role:"toolbar"},Ce(t.default))}}),S0=te({name:"QToolbarTitle",props:{shrink:Boolean},setup(e,{slots:t}){const n=s(()=>"q-toolbar__title ellipsis"+(e.shrink===!0?" col-shrink":""));return()=>r("div",{class:n.value},Ce(t.default))}}),Vm=["none","strict","leaf","leaf-filtered"],x0=te({name:"QTree",props:{...Ke,nodes:{type:Array,required:!0},nodeKey:{type:String,required:!0},labelKey:{type:String,default:"label"},childrenKey:{type:String,default:"children"},dense:Boolean,color:String,controlColor:String,textColor:String,selectedColor:String,icon:String,tickStrategy:{type:String,default:"none",validator:e=>Vm.includes(e)},ticked:Array,expanded:Array,selected:{},noSelectionUnset:Boolean,defaultExpandAll:Boolean,accordion:Boolean,filter:String,filterMethod:Function,duration:{},noConnectors:Boolean,noTransition:Boolean,noNodesLabel:String,noResultsLabel:String},emits:["update:expanded","update:ticked","update:selected","lazyLoad","afterShow","afterHide"],setup(e,{slots:t,emit:n}){const{proxy:l}=ve(),{$q:a}=l,o=We(e,a),i=O({}),u=O(e.ticked||[]),d=O(e.expanded||[]);let f={};Da(()=>{f={}});const c=s(()=>`q-tree q-tree--${e.dense===!0?"dense":"standard"}`+(e.noConnectors===!0?" q-tree--no-connectors":"")+(o.value===!0?" q-tree--dark":"")+(e.color!==void 0?` text-${e.color}`:"")),h=s(()=>e.selected!==void 0),m=s(()=>e.icon||a.iconSet.tree.icon),v=s(()=>e.controlColor||e.color),g=s(()=>e.textColor!==void 0?` text-${e.textColor}`:""),_=s(()=>{const C=e.selectedColor||e.color;return C?` text-${C}`:""}),p=s(()=>e.filterMethod!==void 0?e.filterMethod:(C,K)=>{const Q=K.toLowerCase();return C[e.labelKey]&&C[e.labelKey].toLowerCase().indexOf(Q)!==-1}),k=s(()=>{const C={},K=(Q,oe)=>{const M=Q.tickStrategy||(oe?oe.tickStrategy:e.tickStrategy),D=Q[e.nodeKey],le=Q[e.childrenKey]&&Array.isArray(Q[e.childrenKey])&&Q[e.childrenKey].length!==0,H=Q.disabled!==!0&&h.value===!0&&Q.selectable!==!1,de=Q.disabled!==!0&&Q.expandable!==!1,xe=M!=="none",Y=M==="strict",se=M==="leaf-filtered",me=M==="leaf"||M==="leaf-filtered";let we=Q.disabled!==!0&&Q.tickable!==!1;me===!0&&we===!0&&oe&&oe.tickable!==!0&&(we=!1);let Me=Q.lazy;Me===!0&&i.value[D]!==void 0&&Array.isArray(Q[e.childrenKey])===!0&&(Me=i.value[D]);const ce={key:D,parent:oe,isParent:le,lazy:Me,disabled:Q.disabled,link:Q.disabled!==!0&&(H===!0||de===!0&&(le===!0||Me===!0)),children:[],matchesFilter:e.filter?p.value(Q,e.filter):!0,selected:D===e.selected&&H===!0,selectable:H,expanded:le===!0?d.value.includes(D):!1,expandable:de,noTick:Q.noTick===!0||Y!==!0&&Me&&Me!=="loaded",tickable:we,tickStrategy:M,hasTicking:xe,strictTicking:Y,leafFilteredTicking:se,leafTicking:me,ticked:Y===!0?u.value.includes(D):le===!0?!1:u.value.includes(D)};if(C[D]=ce,le===!0&&(ce.children=Q[e.childrenKey].map($e=>K($e,ce)),e.filter&&(ce.matchesFilter!==!0?ce.matchesFilter=ce.children.some($e=>$e.matchesFilter):ce.noTick!==!0&&ce.disabled!==!0&&ce.tickable===!0&&se===!0&&ce.children.every($e=>$e.matchesFilter!==!0||$e.noTick===!0||$e.tickable!==!0)===!0&&(ce.tickable=!1)),ce.matchesFilter===!0&&(ce.noTick!==!0&&Y!==!0&&ce.children.every($e=>$e.noTick)===!0&&(ce.noTick=!0),me))){if(ce.ticked=!1,ce.indeterminate=ce.children.some($e=>$e.indeterminate===!0),ce.tickable=ce.tickable===!0&&ce.children.some($e=>$e.tickable),ce.indeterminate!==!0){const $e=ce.children.reduce((ze,Oe)=>Oe.ticked===!0?ze+1:ze,0);$e===ce.children.length?ce.ticked=!0:$e>0&&(ce.indeterminate=!0)}ce.indeterminate===!0&&(ce.indeterminateNextState=ce.children.every($e=>$e.tickable!==!0||$e.ticked!==!0))}return ce};return e.nodes.forEach(Q=>K(Q,null)),C});ne(()=>e.ticked,C=>{u.value=C}),ne(()=>e.expanded,C=>{d.value=C});function w(C){const K=[].reduce,Q=(oe,M)=>{if(oe||!M)return oe;if(Array.isArray(M)===!0)return K.call(Object(M),Q,oe);if(M[e.nodeKey]===C)return M;if(M[e.childrenKey])return Q(null,M[e.childrenKey])};return Q(null,e.nodes)}function y(){return u.value.map(C=>w(C))}function b(){return d.value.map(C=>w(C))}function S(C){return C&&k.value[C]?k.value[C].expanded:!1}function B(){e.expanded!==void 0?n("update:expanded",[]):d.value=[]}function L(){const C=[],K=Q=>{Q[e.childrenKey]&&Q[e.childrenKey].length!==0&&Q.expandable!==!1&&Q.disabled!==!0&&(C.push(Q[e.nodeKey]),Q[e.childrenKey].forEach(K))};e.nodes.forEach(K),e.expanded!==void 0?n("update:expanded",C):d.value=C}function R(C,K,Q=w(C),oe=k.value[C]){if(oe.lazy&&oe.lazy!=="loaded"){if(oe.lazy==="loading")return;i.value[C]="loading",Array.isArray(Q[e.childrenKey])!==!0&&(Q[e.childrenKey]=[]),n("lazyLoad",{node:Q,key:C,done:M=>{i.value[C]="loaded",Q[e.childrenKey]=Array.isArray(M)===!0?M:[],Qe(()=>{const D=k.value[C];D&&D.isParent===!0&&A(C,!0)})},fail:()=>{delete i.value[C],Q[e.childrenKey].length===0&&delete Q[e.childrenKey]}})}else oe.isParent===!0&&oe.expandable===!0&&A(C,K)}function A(C,K){let Q=d.value;const oe=e.expanded!==void 0;if(oe===!0&&(Q=Q.slice()),K){if(e.accordion&&k.value[C]){const M=[];k.value[C].parent?k.value[C].parent.children.forEach(D=>{D.key!==C&&D.expandable===!0&&M.push(D.key)}):e.nodes.forEach(D=>{const le=D[e.nodeKey];le!==C&&M.push(le)}),M.length!==0&&(Q=Q.filter(D=>M.includes(D)===!1))}Q=Q.concat([C]).filter((M,D,le)=>le.indexOf(M)===D)}else Q=Q.filter(M=>M!==C);oe===!0?n("update:expanded",Q):d.value=Q}function P(C){return C&&k.value[C]?k.value[C].ticked:!1}function z(C,K){let Q=u.value;const oe=e.ticked!==void 0;oe===!0&&(Q=Q.slice()),K?Q=Q.concat(C).filter((M,D,le)=>le.indexOf(M)===D):Q=Q.filter(M=>C.includes(M)===!1),oe===!0&&n("update:ticked",Q)}function T(C,K,Q){const oe={tree:l,node:C,key:Q,color:e.color,dark:o.value};return St(oe,"expanded",()=>K.expanded,M=>{M!==K.expanded&&R(Q,M)}),St(oe,"ticked",()=>K.ticked,M=>{M!==K.ticked&&z([Q],M)}),oe}function x(C){return(e.filter?C.filter(K=>k.value[K[e.nodeKey]].matchesFilter):C).map(K=>E(K))}function q(C){if(C.icon!==void 0)return r(je,{class:"q-tree__icon q-mr-sm",name:C.icon,color:C.iconColor});const K=C.img||C.avatar;if(K)return r("img",{class:`q-tree__${C.img?"img":"avatar"} q-mr-sm`,src:K})}function U(){n("afterShow")}function W(){n("afterHide")}function E(C){const K=C[e.nodeKey],Q=k.value[K],oe=C.header&&t[`header-${C.header}`]||t["default-header"],M=Q.isParent===!0?x(C[e.childrenKey]):[],D=M.length!==0||Q.lazy&&Q.lazy!=="loaded";let le=C.body&&t[`body-${C.body}`]||t["default-body"];const H=oe!==void 0||le!==void 0?T(C,Q,K):null;return le!==void 0&&(le=r("div",{class:"q-tree__node-body relative-position"},[r("div",{class:g.value},[le(H)])])),r("div",{key:K,class:`q-tree__node relative-position q-tree__node--${D===!0?"parent":"child"}`},[r("div",{class:"q-tree__node-header relative-position row no-wrap items-center"+(Q.link===!0?" q-tree__node--link q-hoverable q-focusable":"")+(Q.selected===!0?" q-tree__node--selected":"")+(Q.disabled===!0?" q-tree__node--disabled":""),tabindex:Q.link===!0?0:-1,ariaExpanded:M.length>0?Q.expanded:null,role:"treeitem",onClick:de=>{j(C,Q,de)},onKeypress(de){Hn(de)!==!0&&(de.keyCode===13?j(C,Q,de,!0):de.keyCode===32&&ue(C,Q,de,!0))}},[r("div",{class:"q-focus-helper",tabindex:-1,ref:de=>{f[Q.key]=de}}),Q.lazy==="loading"?r(It,{class:"q-tree__spinner",color:v.value}):D===!0?r(je,{class:"q-tree__arrow"+(Q.expanded===!0?" q-tree__arrow--rotate":""),name:m.value,onClick(de){ue(C,Q,de)}}):null,Q.hasTicking===!0&&Q.noTick!==!0?r($a,{class:"q-tree__tickbox",modelValue:Q.indeterminate===!0?null:Q.ticked,color:v.value,dark:o.value,dense:!0,keepColor:!0,disable:Q.tickable!==!0,onKeydown:Fe,"onUpdate:modelValue":de=>{I(Q,de)}}):null,r("div",{class:"q-tree__node-header-content col row no-wrap items-center"+(Q.selected===!0?_.value:g.value)},[oe?oe(H):[q(C),r("div",C[e.labelKey])]])]),D===!0?e.noTransition===!0?Q.expanded===!0?r("div",{class:"q-tree__node-collapsible"+g.value,key:`${K}__q`},[le,r("div",{class:"q-tree__children"+(Q.disabled===!0?" q-tree__node--disabled":""),role:"group"},M)]):null:r(yr,{duration:e.duration,onShow:U,onHide:W},()=>Ut(r("div",{class:"q-tree__node-collapsible"+g.value,key:`${K}__q`},[le,r("div",{class:"q-tree__children"+(Q.disabled===!0?" q-tree__node--disabled":""),role:"group"},M)]),[[Zi,Q.expanded]])):le])}function V(C){const K=f[C];K&&K.focus()}function j(C,K,Q,oe){oe!==!0&&K.selectable!==!1&&V(K.key),h.value&&K.selectable?e.noSelectionUnset===!1?n("update:selected",K.key!==e.selected?K.key:null):K.key!==e.selected&&n("update:selected",K.key===void 0?null:K.key):ue(C,K,Q,oe),typeof C.handler=="function"&&C.handler(C)}function ue(C,K,Q,oe){Q!==void 0&&Fe(Q),oe!==!0&&K.selectable!==!1&&V(K.key),R(K.key,!K.expanded,C,K)}function I(C,K){if(C.indeterminate===!0&&(K=C.indeterminateNextState),C.strictTicking)z([C.key],K);else if(C.leafTicking){const Q=[],oe=M=>{M.isParent?(K!==!0&&M.noTick!==!0&&M.tickable===!0&&Q.push(M.key),M.leafTicking===!0&&M.children.forEach(oe)):M.noTick!==!0&&M.tickable===!0&&(M.leafFilteredTicking!==!0||M.matchesFilter===!0)&&Q.push(M.key)};oe(C),z(Q,K)}}return e.defaultExpandAll===!0&&L(),Object.assign(l,{getNodeByKey:w,getTickedNodes:y,getExpandedNodes:b,isExpanded:S,collapseAll:B,expandAll:L,setExpanded:R,isTicked:P,setTicked:z}),()=>{const C=x(e.nodes);return r("div",{class:c.value,role:"tree"},C.length===0?e.filter?e.noResultsLabel||a.lang.tree.noResults:e.noNodesLabel||a.lang.tree.noNodes:C)}}});function Pi(e){return(e*100).toFixed(2)+"%"}var Dm={...Ke,...xs,label:String,color:String,textColor:String,square:Boolean,flat:Boolean,bordered:Boolean,noThumbnails:Boolean,autoUpload:Boolean,hideUploadBtn:Boolean,disable:Boolean,readonly:Boolean},Hs=[...Cs,"start","finish","added","removed"];function Im(e,t){const n=ve(),{props:l,slots:a,emit:o,proxy:i}=n,{$q:u}=i,d=We(l,u);function f(H,de,xe){if(H.__status=de,de==="idle"){H.__uploaded=0,H.__progress=0,H.__sizeLabel=hl(H.size),H.__progressLabel="0.00%";return}if(de==="failed"){i.$forceUpdate();return}H.__uploaded=de==="uploaded"?H.size:xe,H.__progress=de==="uploaded"?1:Math.min(.9999,H.__uploaded/H.size),H.__progressLabel=Pi(H.__progress),i.$forceUpdate()}const c=s(()=>l.disable!==!0&&l.readonly!==!0),h=O(!1),m=O(null),v=O(null),g={files:O([]),queuedFiles:O([]),uploadedFiles:O([]),uploadedSize:O(0),updateFileStatus:f,isAlive:()=>on(n)===!1},{pickFiles:_,addFiles:p,onDragover:k,onDragleave:w,processFiles:y,getDndNode:b,maxFilesNumber:S,maxTotalSizeNumber:B}=ks({editable:c,dnd:h,getFileInput:I,addFilesToQueue:C});Object.assign(g,e({props:l,slots:a,emit:o,helpers:g,exposeApi:H=>{Object.assign(g,H)}})),g.isBusy===void 0&&(g.isBusy=O(!1));const L=O(0),R=s(()=>L.value===0?0:g.uploadedSize.value/L.value),A=s(()=>Pi(R.value)),P=s(()=>hl(L.value)),z=s(()=>c.value===!0&&g.isUploading.value!==!0&&(l.multiple===!0||g.queuedFiles.value.length===0)&&(l.maxFiles===void 0||g.files.value.length<S.value)&&(l.maxTotalSize===void 0||L.value<B.value)),T=s(()=>c.value===!0&&g.isBusy.value!==!0&&g.isUploading.value!==!0&&g.queuedFiles.value.length!==0);yn(fu,oe);const x=s(()=>"q-uploader column no-wrap"+(d.value===!0?" q-uploader--dark q-dark":"")+(l.bordered===!0?" q-uploader--bordered":"")+(l.square===!0?" q-uploader--square no-border-radius":"")+(l.flat===!0?" q-uploader--flat no-shadow":"")+(l.disable===!0?" disabled q-uploader--disable":"")+(h.value===!0?" q-uploader--dnd":"")),q=s(()=>"q-uploader__header"+(l.color!==void 0?` bg-${l.color}`:"")+(l.textColor!==void 0?` text-${l.textColor}`:""));ne(g.isUploading,(H,de)=>{de===!1&&H===!0?o("start"):de===!0&&H===!1&&o("finish")});function U(){l.disable===!1&&(g.abort(),g.uploadedSize.value=0,L.value=0,ue(),g.files.value=[],g.queuedFiles.value=[],g.uploadedFiles.value=[])}function W(){l.disable===!1&&V(["uploaded"],()=>{g.uploadedFiles.value=[]})}function E(){V(["idle","failed"],({size:H})=>{L.value-=H,g.queuedFiles.value=[]})}function V(H,de){if(l.disable===!0)return;const xe={files:[],size:0},Y=g.files.value.filter(se=>H.indexOf(se.__status)===-1?!0:(xe.size+=se.size,xe.files.push(se),se.__img!==void 0&&window.URL.revokeObjectURL(se.__img.src),!1));xe.files.length!==0&&(g.files.value=Y,de(xe),o("removed",xe.files))}function j(H){l.disable||(H.__status==="uploaded"?g.uploadedFiles.value=g.uploadedFiles.value.filter(de=>de.__key!==H.__key):H.__status==="uploading"?H.__abort():L.value-=H.size,g.files.value=g.files.value.filter(de=>de.__key!==H.__key?!0:(de.__img!==void 0&&window.URL.revokeObjectURL(de.__img.src),!1)),g.queuedFiles.value=g.queuedFiles.value.filter(de=>de.__key!==H.__key),o("removed",[H]))}function ue(){g.files.value.forEach(H=>{H.__img!==void 0&&window.URL.revokeObjectURL(H.__img.src)})}function I(){return v.value||m.value.getElementsByClassName("q-uploader__input")[0]}function C(H,de){const xe=y(H,de,g.files.value,!0),Y=I();Y!=null&&(Y.value=""),xe!==void 0&&(xe.forEach(se=>{if(g.updateFileStatus(se,"idle"),L.value+=se.size,l.noThumbnails!==!0&&se.type.toUpperCase().startsWith("IMAGE")){const me=new Image;me.src=window.URL.createObjectURL(se),se.__img=me}}),g.files.value=g.files.value.concat(xe),g.queuedFiles.value=g.queuedFiles.value.concat(xe),o("added",xe),l.autoUpload===!0&&g.upload())}function K(){T.value===!0&&g.upload()}function Q(H,de,xe){if(H===!0){const Y={type:"a",key:de,icon:u.iconSet.uploader[de],flat:!0,dense:!0};let se;return de==="add"?(Y.onClick=_,se=oe):Y.onClick=xe,r(Xe,Y,se)}}function oe(){return r("input",{ref:v,class:"q-uploader__input overflow-hidden absolute-full",tabindex:-1,type:"file",title:"",accept:l.accept,multiple:l.multiple===!0?"multiple":void 0,capture:l.capture,onMousedown:dt,onClick:_,onChange:C})}function M(){return a.header!==void 0?a.header(le):[r("div",{class:"q-uploader__header-content column"},[r("div",{class:"flex flex-center no-wrap q-gutter-xs"},[Q(g.queuedFiles.value.length!==0,"removeQueue",E),Q(g.uploadedFiles.value.length!==0,"removeUploaded",W),g.isUploading.value===!0?r(It,{class:"q-uploader__spinner"}):null,r("div",{class:"col column justify-center"},[l.label!==void 0?r("div",{class:"q-uploader__title"},[l.label]):null,r("div",{class:"q-uploader__subtitle"},[P.value+" / "+A.value])]),Q(z.value,"add"),Q(l.hideUploadBtn===!1&&T.value===!0,"upload",g.upload),Q(g.isUploading.value,"clear",g.abort)])])]}function D(){return a.list!==void 0?a.list(le):g.files.value.map(H=>r("div",{key:H.__key,class:"q-uploader__file relative-position"+(l.noThumbnails!==!0&&H.__img!==void 0?" q-uploader__file--img":"")+(H.__status==="failed"?" q-uploader__file--failed":H.__status==="uploaded"?" q-uploader__file--uploaded":""),style:l.noThumbnails!==!0&&H.__img!==void 0?{backgroundImage:'url("'+H.__img.src+'")'}:null},[r("div",{class:"q-uploader__file-header row flex-center no-wrap"},[H.__status==="failed"?r(je,{class:"q-uploader__file-status",name:u.iconSet.type.negative,color:"negative"}):null,r("div",{class:"q-uploader__file-header-content col"},[r("div",{class:"q-uploader__title"},[H.name]),r("div",{class:"q-uploader__subtitle row items-center no-wrap"},[H.__sizeLabel+" / "+H.__progressLabel])]),H.__status==="uploading"?r(Uu,{value:H.__progress,min:0,max:1,indeterminate:H.__progress===0}):r(Xe,{round:!0,dense:!0,flat:!0,icon:u.iconSet.uploader[H.__status==="uploaded"?"done":"clear"],onClick:()=>{j(H)}})])]))}Ne(()=>{g.isUploading.value===!0&&g.abort(),g.files.value.length!==0&&ue()});const le={};for(const H in g)xc(g[H])===!0?St(le,H,()=>g[H].value):le[H]=g[H];return Object.assign(le,{upload:K,reset:U,removeUploadedFiles:W,removeQueuedFiles:E,removeFile:j,pickFiles:_,addFiles:p}),Ji(le,{canAddFiles:()=>z.value,canUpload:()=>T.value,uploadSizeLabel:()=>P.value,uploadProgressLabel:()=>A.value}),t({...g,upload:K,reset:U,removeUploadedFiles:W,removeQueuedFiles:E,removeFile:j,pickFiles:_,addFiles:p,canAddFiles:z,canUpload:T,uploadSizeLabel:P,uploadProgressLabel:A}),()=>{const H=[r("div",{class:q.value},M()),r("div",{class:"q-uploader__list scroll"},D()),b("uploader")];g.isBusy.value===!0&&H.push(r("div",{class:"q-uploader__overlay absolute-full flex flex-center"},[r(It)]));const de={ref:m,class:x.value};return z.value===!0&&Object.assign(de,{onDragover:k,onDragleave:w}),r("div",de,H)}}var Hm=()=>!0;function Ns(e){const t={};return e.forEach(n=>{t[n]=Hm}),t}var Nm=Ns(Hs),Qm=({name:e,props:t,emits:n,injectPlugin:l})=>te({name:e,props:{...Dm,...t},emits:yt(n)===!0?{...Nm,...n}:[...Hs,...n],setup(a,{expose:o}){return Im(l,o)}});function tn(e){return typeof e=="function"?e:()=>e}var jm="QUploader",Km={url:[Function,String],method:{type:[Function,String],default:"POST"},fieldName:{type:[Function,String],default:()=>e=>e.name},headers:[Function,Array],formFields:[Function,Array],withCredentials:[Function,Boolean],sendRaw:[Function,Boolean],batch:[Function,Boolean],factory:Function},Wm=["factoryFailed","uploaded","failed","uploading"];function Um({props:e,emit:t,helpers:n}){const l=O([]),a=O([]),o=O(0),i=s(()=>({url:tn(e.url),method:tn(e.method),headers:tn(e.headers),formFields:tn(e.formFields),fieldName:tn(e.fieldName),withCredentials:tn(e.withCredentials),sendRaw:tn(e.sendRaw),batch:tn(e.batch)})),u=s(()=>o.value>0),d=s(()=>a.value.length!==0);let f;function c(){l.value.forEach(g=>{g.abort()}),a.value.length!==0&&(f=!0)}function h(){const g=n.queuedFiles.value.slice(0);n.queuedFiles.value=[],i.value.batch(g)?m(g):g.forEach(_=>{m([_])})}function m(g){if(o.value++,typeof e.factory!="function"){v(g,{});return}const _=e.factory(g);if(!_)t("factoryFailed",new Error("QUploader: factory() does not return properly"),g),o.value--;else if(typeof _.catch=="function"&&typeof _.then=="function"){a.value.push(_);const p=k=>{n.isAlive()===!0&&(a.value=a.value.filter(w=>w!==_),a.value.length===0&&(f=!1),n.queuedFiles.value=n.queuedFiles.value.concat(g),g.forEach(w=>{n.updateFileStatus(w,"failed")}),t("factoryFailed",k,g),o.value--)};_.then(k=>{f===!0?p(new Error("Aborted")):n.isAlive()===!0&&(a.value=a.value.filter(w=>w!==_),v(g,k))}).catch(p)}else v(g,_||{})}function v(g,_){const p=new FormData,k=new XMLHttpRequest,w=(T,x)=>_[T]!==void 0?tn(_[T])(x):i.value[T](x),y=w("url",g);if(!y){console.error("q-uploader: invalid or no URL specified"),o.value--;return}const b=w("formFields",g);b!==void 0&&b.forEach(T=>{p.append(T.name,T.value)});let S=0,B=0,L=0,R=0,A;k.upload.addEventListener("progress",T=>{if(A===!0)return;const x=Math.min(R,T.loaded);n.uploadedSize.value+=x-L,L=x;let q=L-B;for(let U=S;q>0&&U<g.length;U++){const W=g[U];if(q>W.size)q-=W.size,S++,B+=W.size,n.updateFileStatus(W,"uploading",W.size);else{n.updateFileStatus(W,"uploading",q);return}}},!1),k.onreadystatechange=()=>{k.readyState<4||(k.status&&k.status<400?(n.uploadedFiles.value=n.uploadedFiles.value.concat(g),g.forEach(T=>{n.updateFileStatus(T,"uploaded")}),t("uploaded",{files:g,xhr:k})):(A=!0,n.uploadedSize.value-=L,n.queuedFiles.value=n.queuedFiles.value.concat(g),g.forEach(T=>{n.updateFileStatus(T,"failed")}),t("failed",{files:g,xhr:k})),o.value--,l.value=l.value.filter(T=>T!==k))},k.open(w("method",g),y),w("withCredentials",g)===!0&&(k.withCredentials=!0);const P=w("headers",g);P!==void 0&&P.forEach(T=>{k.setRequestHeader(T.name,T.value)});const z=w("sendRaw",g);g.forEach(T=>{n.updateFileStatus(T,"uploading",0),z!==!0&&p.append(w("fieldName",T),T,T.name),T.xhr=k,T.__abort=()=>{k.abort()},R+=T.size}),t("uploading",{files:g,xhr:k}),l.value.push(k),z===!0?k.send(new Blob(g)):k.send(p)}return{isUploading:u,isBusy:d,abort:c,upload:h}}var Ym={name:jm,props:Km,emits:Wm,injectPlugin:Um},C0=Qm(Ym),k0=te({name:"QUploaderAddTrigger",setup(){const e=Et(fu,Je);return e===Je&&console.error("QUploaderAddTrigger needs to be child of QUploader"),e}}),q0=te({name:"QVideo",props:{...pr,src:{type:String,required:!0},title:String,fetchpriority:{type:String,default:"auto"},loading:{type:String,default:"eager"},referrerpolicy:{type:String,default:"strict-origin-when-cross-origin"}},setup(e){const t=wr(e),n=s(()=>"q-video"+(e.ratio!==void 0?" q-video--responsive":""));return()=>r("div",{class:n.value,style:t.value},[r("iframe",{src:e.src,title:e.title,fetchpriority:e.fetchpriority,loading:e.loading,referrerpolicy:e.referrerpolicy,frameborder:"0",allowfullscreen:!0})])}});function Ei(e){if(e===!1)return 0;if(e===!0||e===void 0)return 1;const t=parseInt(e,10);return isNaN(t)?0:t}var T0=Xt({name:"close-popup",beforeMount(e,{value:t}){const n={depth:Ei(t),handler(l){n.depth!==0&&setTimeout(()=>{const a=Bd(e);a!==void 0&&Pd(a,l,n.depth)})},handlerKey(l){Vt(l,13)===!0&&n.handler(l)}};e.__qclosepopup=n,e.addEventListener("click",n.handler),e.addEventListener("keyup",n.handlerKey)},updated(e,{value:t,oldValue:n}){t!==n&&(e.__qclosepopup.depth=Ei(t))},beforeUnmount(e){const t=e.__qclosepopup;e.removeEventListener("click",t.handler),e.removeEventListener("keyup",t.handlerKey),delete e.__qclosepopup}}),Xm=0,_a=void 0;function Li(e,t){_a===void 0&&(_a=document.createElement("div"),_a.style.cssText="position: absolute; left: 0; top: 0",document.body.appendChild(_a));const n=e.getBoundingClientRect(),l=_a.getBoundingClientRect(),{marginLeft:a,marginRight:o,marginTop:i,marginBottom:u}=window.getComputedStyle(e),d=parseInt(a,10)+parseInt(o,10),f=parseInt(i,10)+parseInt(u,10);return{left:n.left-l.left,top:n.top-l.top,width:n.right-n.left,height:n.bottom-n.top,widthM:n.right-n.left+(t===!0?0:d),heightM:n.bottom-n.top+(t===!0?0:f),marginH:t===!0?d:0,marginV:t===!0?f:0}}function ol(e){return{width:e.scrollWidth,height:e.scrollHeight}}var Ai=["Top","Right","Bottom","Left"],zi=["borderTopLeftRadius","borderTopRightRadius","borderBottomRightRadius","borderBottomLeftRadius"],Gm=/-block|-inline|block-|inline-/,Zm=/(-block|-inline|block-|inline-).*:/;function Oi(e,t){const n=window.getComputedStyle(e),l={};for(let a=0;a<t.length;a++){const o=t[a];if(n[o]==="")if(o==="cssText"){const i=n.length;let u="";for(let d=0;d<i;d++)Gm.test(n[d])!==!0&&(u+=n[d]+": "+n[n[d]]+"; ");l[o]=u}else if(["borderWidth","borderStyle","borderColor"].indexOf(o)!==-1){const i=o.replace("border","");let u="";for(let d=0;d<Ai.length;d++){const f="border"+Ai[d]+i;u+=n[f]+" "}l[o]=u}else if(o==="borderRadius"){let i="",u="";for(let d=0;d<zi.length;d++){const f=n[zi[d]].split(" ");i+=f[0]+" ",u+=(f[1]===void 0?f[0]:f[1])+" "}l[o]=i+"/ "+u}else l[o]=n[o];else o==="cssText"?l[o]=n[o].split(";").filter(i=>Zm.test(i)!==!0).join(";"):l[o]=n[o]}return l}var Jm=["absolute","fixed","relative","sticky"];function Ri(e){let t=e,n=0;for(;t!==null&&t!==document;){const{position:l,zIndex:a}=window.getComputedStyle(t),o=Number(a);o>n&&(t===e||Jm.includes(l)===!0)&&(n=o),t=t.parentNode}return n}function eg(e){return{from:e.from,to:e.to!==void 0?e.to:e.from}}function tg(e){return typeof e=="number"?e={duration:e}:typeof e=="function"&&(e={onEnd:e}),{...e,waitFor:e.waitFor===void 0?0:e.waitFor,duration:isNaN(e.duration)===!0?300:parseInt(e.duration,10),easing:typeof e.easing=="string"&&e.easing.length!==0?e.easing:"ease-in-out",delay:isNaN(e.delay)===!0?0:parseInt(e.delay,10),fill:typeof e.fill=="string"&&e.fill.length!==0?e.fill:"none",resize:e.resize===!0,useCSS:e.useCSS===!0||e.usecss===!0,hideFromClone:e.hideFromClone===!0||e.hidefromclone===!0,keepToClone:e.keepToClone===!0||e.keeptoclone===!0,tween:e.tween===!0,tweenFromOpacity:isNaN(e.tweenFromOpacity)===!0?.6:parseFloat(e.tweenFromOpacity),tweenToOpacity:isNaN(e.tweenToOpacity)===!0?.5:parseFloat(e.tweenToOpacity)}}function Fi(e){const t=typeof e;return t==="function"?e():t==="string"?document.querySelector(e):e}function Vi(e){return e&&e.ownerDocument===document&&e.parentNode!==null}function ng(e){let t=()=>!1,n=!1,l=!0;const a=eg(e),o=tg(e),i=Fi(a.from);if(Vi(i)!==!0)return t;typeof i.qMorphCancel=="function"&&i.qMorphCancel();let u,d,f,c;const h=i.parentNode,m=i.nextElementSibling,v=Li(i,o.resize),{width:g,height:_}=ol(h),{borderWidth:p,borderStyle:k,borderColor:w,borderRadius:y,backgroundColor:b,transform:S,position:B,cssText:L}=Oi(i,["borderWidth","borderStyle","borderColor","borderRadius","backgroundColor","transform","position","cssText"]),R=i.classList.toString(),A=i.style.cssText,P=i.cloneNode(!0),z=o.tween===!0?i.cloneNode(!0):void 0;z!==void 0&&(z.className=z.classList.toString().split(" ").filter(x=>/^bg-/.test(x)===!1).join(" ")),o.hideFromClone===!0&&P.classList.add("q-morph--internal"),P.setAttribute("aria-hidden","true"),P.style.transition="none",P.style.animation="none",P.style.pointerEvents="none",h.insertBefore(P,m),i.qMorphCancel=()=>{n=!0,P.remove(),z!==void 0&&z.remove(),o.hideFromClone===!0&&P.classList.remove("q-morph--internal"),i.qMorphCancel=void 0};const T=()=>{const x=Fi(a.to);if(n===!0||Vi(x)!==!0){typeof i.qMorphCancel=="function"&&i.qMorphCancel();return}i!==x&&typeof x.qMorphCancel=="function"&&x.qMorphCancel(),o.keepToClone!==!0&&x.classList.add("q-morph--internal"),P.classList.add("q-morph--internal");const{width:q,height:U}=ol(h),{width:W,height:E}=ol(x.parentNode);o.hideFromClone!==!0&&P.classList.remove("q-morph--internal"),x.qMorphCancel=()=>{n=!0,P.remove(),z!==void 0&&z.remove(),o.hideFromClone===!0&&P.classList.remove("q-morph--internal"),o.keepToClone!==!0&&x.classList.remove("q-morph--internal"),i.qMorphCancel=void 0,x.qMorphCancel=void 0};const V=()=>{if(n===!0){typeof x.qMorphCancel=="function"&&x.qMorphCancel();return}o.hideFromClone!==!0&&(P.classList.add("q-morph--internal"),P.innerHTML="",P.style.left=0,P.style.right="unset",P.style.top=0,P.style.bottom="unset",P.style.transform="none"),o.keepToClone!==!0&&x.classList.remove("q-morph--internal");const j=x.parentNode,{width:ue,height:I}=ol(j),C=x.cloneNode(o.keepToClone);C.setAttribute("aria-hidden","true"),o.keepToClone!==!0&&(C.style.left=0,C.style.right="unset",C.style.top=0,C.style.bottom="unset",C.style.transform="none",C.style.pointerEvents="none"),C.classList.add("q-morph--internal");const K=x===i&&h===j?P:x.nextElementSibling;j.insertBefore(C,K);const{borderWidth:Q,borderStyle:oe,borderColor:M,borderRadius:D,backgroundColor:le,transform:H,position:de,cssText:xe}=Oi(x,["borderWidth","borderStyle","borderColor","borderRadius","backgroundColor","transform","position","cssText"]),Y=x.classList.toString(),se=x.style.cssText;x.style.cssText=xe,x.style.transform="none",x.style.animation="none",x.style.transition="none",x.className=Y.split(" ").filter(ae=>/^bg-/.test(ae)===!1).join(" ");const me=Li(x,o.resize),we=v.left-me.left,Me=v.top-me.top,ce=v.width/(me.width>0?me.width:10),$e=v.height/(me.height>0?me.height:100),ze=g-q,Oe=_-U,re=ue-W,fe=I-E,G=Math.max(v.widthM,ze),be=Math.max(v.heightM,Oe),Be=Math.max(me.widthM,re),Ee=Math.max(me.heightM,fe),Pe=i===x&&["absolute","fixed"].includes(de)===!1&&["absolute","fixed"].includes(B)===!1;let Ge=de==="fixed",nt=j;for(;Ge!==!0&&nt!==document;)Ge=window.getComputedStyle(nt).position==="fixed",nt=nt.parentNode;if(o.hideFromClone!==!0&&(P.style.display="block",P.style.flex="0 0 auto",P.style.opacity=0,P.style.minWidth="unset",P.style.maxWidth="unset",P.style.minHeight="unset",P.style.maxHeight="unset",P.classList.remove("q-morph--internal")),o.keepToClone!==!0&&(C.style.display="block",C.style.flex="0 0 auto",C.style.opacity=0,C.style.minWidth="unset",C.style.maxWidth="unset",C.style.minHeight="unset",C.style.maxHeight="unset"),C.classList.remove("q-morph--internal"),typeof o.classes=="string"&&(x.className+=" "+o.classes),typeof o.style=="string")x.style.cssText+=" "+o.style;else if(yt(o.style)===!0)for(const ae in o.style)x.style[ae]=o.style[ae];const at=Ri(P),J=Ri(x),ie=Ge===!0?document.documentElement:{scrollLeft:0,scrollTop:0};x.style.position=Ge===!0?"fixed":"absolute",x.style.left=`${me.left-ie.scrollLeft}px`,x.style.right="unset",x.style.top=`${me.top-ie.scrollTop}px`,x.style.margin=0,o.resize===!0&&(x.style.minWidth="unset",x.style.maxWidth="unset",x.style.minHeight="unset",x.style.maxHeight="unset",x.style.overflow="hidden",x.style.overflowX="hidden",x.style.overflowY="hidden"),document.body.appendChild(x),z!==void 0&&(z.style.cssText=L,z.style.transform="none",z.style.animation="none",z.style.transition="none",z.style.position=x.style.position,z.style.left=`${v.left-ie.scrollLeft}px`,z.style.right="unset",z.style.top=`${v.top-ie.scrollTop}px`,z.style.margin=0,z.style.pointerEvents="none",o.resize===!0&&(z.style.minWidth="unset",z.style.maxWidth="unset",z.style.minHeight="unset",z.style.maxHeight="unset",z.style.overflow="hidden",z.style.overflowX="hidden",z.style.overflowY="hidden"),document.body.appendChild(z));const X=ae=>{i===x&&l!==!0?(x.style.cssText=A,x.className=R):(x.style.cssText=se,x.className=Y),C.parentNode===j&&j.insertBefore(x,C),P.remove(),C.remove(),z!==void 0&&z.remove(),t=()=>!1,i.qMorphCancel=void 0,x.qMorphCancel=void 0,typeof o.onEnd=="function"&&o.onEnd(l===!0?"to":"from",ae===!0)};if(o.useCSS!==!0&&typeof x.animate=="function"){const ae=o.resize===!0?{transform:`translate(${we}px, ${Me}px)`,width:`${G}px`,height:`${be}px`}:{transform:`translate(${we}px, ${Me}px) scale(${ce}, ${$e})`},ke=o.resize===!0?{width:`${Be}px`,height:`${Ee}px`}:{},Re=o.resize===!0?{width:`${G}px`,height:`${be}px`}:{},_e=o.resize===!0?{transform:`translate(${-1*we}px, ${-1*Me}px)`,width:`${Be}px`,height:`${Ee}px`}:{transform:`translate(${-1*we}px, ${-1*Me}px) scale(${1/ce}, ${1/$e})`},He=z!==void 0?{opacity:o.tweenToOpacity}:{backgroundColor:b},ot=z!==void 0?{opacity:1}:{backgroundColor:le};c=x.animate([{margin:0,borderWidth:p,borderStyle:k,borderColor:w,borderRadius:y,zIndex:at,transformOrigin:"0 0",...ae,...He},{margin:0,borderWidth:Q,borderStyle:oe,borderColor:M,borderRadius:D,zIndex:J,transformOrigin:"0 0",transform:H,...ke,...ot}],{duration:o.duration,easing:o.easing,fill:o.fill,delay:o.delay}),d=z===void 0?void 0:z.animate([{opacity:o.tweenFromOpacity,margin:0,borderWidth:p,borderStyle:k,borderColor:w,borderRadius:y,zIndex:at,transformOrigin:"0 0",transform:S,...Re},{opacity:0,margin:0,borderWidth:Q,borderStyle:oe,borderColor:M,borderRadius:D,zIndex:J,transformOrigin:"0 0",..._e}],{duration:o.duration,easing:o.easing,fill:o.fill,delay:o.delay}),u=o.hideFromClone===!0||Pe===!0?void 0:P.animate([{margin:`${Oe<0?Oe/2:0}px ${ze<0?ze/2:0}px`,width:`${G+v.marginH}px`,height:`${be+v.marginV}px`},{margin:0,width:0,height:0}],{duration:o.duration,easing:o.easing,fill:o.fill,delay:o.delay}),f=o.keepToClone===!0?void 0:C.animate([Pe===!0?{margin:`${Oe<0?Oe/2:0}px ${ze<0?ze/2:0}px`,width:`${G+v.marginH}px`,height:`${be+v.marginV}px`}:{margin:0,width:0,height:0},{margin:`${fe<0?fe/2:0}px ${re<0?re/2:0}px`,width:`${Be+me.marginH}px`,height:`${Ee+me.marginV}px`}],{duration:o.duration,easing:o.easing,fill:o.fill,delay:o.delay});const ut=Bt=>{u!==void 0&&u.cancel(),d!==void 0&&d.cancel(),f!==void 0&&f.cancel(),c.cancel(),c.removeEventListener("finish",ut),c.removeEventListener("cancel",ut),X(Bt),u=void 0,d=void 0,f=void 0,c=void 0};i.qMorphCancel=()=>{i.qMorphCancel=void 0,n=!0,ut()},x.qMorphCancel=()=>{x.qMorphCancel=void 0,n=!0,ut()},c.addEventListener("finish",ut),c.addEventListener("cancel",ut),t=Bt=>n===!0||c===void 0?!1:Bt===!0?(ut(!0),!0):(l=l!==!0,u!==void 0&&u.reverse(),d!==void 0&&d.reverse(),f!==void 0&&f.reverse(),c.reverse(),!0)}else{const ae=`q-morph-anim-${++Xm}`,ke=document.createElement("style"),Re=o.resize===!0?`
            transform: translate(${we}px, ${Me}px);
            width: ${G}px;
            height: ${be}px;
          `:`transform: translate(${we}px, ${Me}px) scale(${ce}, ${$e});`,_e=o.resize===!0?`
            width: ${Be}px;
            height: ${Ee}px;
          `:"",He=o.resize===!0?`
            width: ${G}px;
            height: ${be}px;
          `:"",ot=o.resize===!0?`
            transform: translate(${-1*we}px, ${-1*Me}px);
            width: ${Be}px;
            height: ${Ee}px;
          `:`transform: translate(${-1*we}px, ${-1*Me}px) scale(${1/ce}, ${1/$e});`,ut=z!==void 0?`opacity: ${o.tweenToOpacity};`:`background-color: ${b};`,Bt=z!==void 0?"opacity: 1;":`background-color: ${le};`,At=z===void 0?"":`
            @keyframes ${ae}-from-tween {
              0% {
                opacity: ${o.tweenFromOpacity};
                margin: 0;
                border-width: ${p};
                border-style: ${k};
                border-color: ${w};
                border-radius: ${y};
                z-index: ${at};
                transform-origin: 0 0;
                transform: ${S};
                ${He}
              }

              100% {
                opacity: 0;
                margin: 0;
                border-width: ${Q};
                border-style: ${oe};
                border-color: ${M};
                border-radius: ${D};
                z-index: ${J};
                transform-origin: 0 0;
                ${ot}
              }
            }
          `,en=o.hideFromClone===!0||Pe===!0?"":`
            @keyframes ${ae}-from {
              0% {
                margin: ${Oe<0?Oe/2:0}px ${ze<0?ze/2:0}px;
                width: ${G+v.marginH}px;
                height: ${be+v.marginV}px;
              }

              100% {
                margin: 0;
                width: 0;
                height: 0;
              }
            }
          `,Zt=Pe===!0?`
            margin: ${Oe<0?Oe/2:0}px ${ze<0?ze/2:0}px;
            width: ${G+v.marginH}px;
            height: ${be+v.marginV}px;
          `:`
            margin: 0;
            width: 0;
            height: 0;
          `,ee=o.keepToClone===!0?"":`
            @keyframes ${ae}-to {
              0% {
                ${Zt}
              }

              100% {
                margin: ${fe<0?fe/2:0}px ${re<0?re/2:0}px;
                width: ${Be+me.marginH}px;
                height: ${Ee+me.marginV}px;
              }
            }
          `;ke.innerHTML=`
          @keyframes ${ae} {
            0% {
              margin: 0;
              border-width: ${p};
              border-style: ${k};
              border-color: ${w};
              border-radius: ${y};
              background-color: ${b};
              z-index: ${at};
              transform-origin: 0 0;
              ${Re}
              ${ut}
            }

            100% {
              margin: 0;
              border-width: ${Q};
              border-style: ${oe};
              border-color: ${M};
              border-radius: ${D};
              background-color: ${le};
              z-index: ${J};
              transform-origin: 0 0;
              transform: ${H};
              ${_e}
              ${Bt}
            }
          }

          ${en}

          ${At}

          ${ee}
        `,document.head.appendChild(ke);let ye="normal";P.style.animation=`${o.duration}ms ${o.easing} ${o.delay}ms ${ye} ${o.fill} ${ae}-from`,z!==void 0&&(z.style.animation=`${o.duration}ms ${o.easing} ${o.delay}ms ${ye} ${o.fill} ${ae}-from-tween`),C.style.animation=`${o.duration}ms ${o.easing} ${o.delay}ms ${ye} ${o.fill} ${ae}-to`,x.style.animation=`${o.duration}ms ${o.easing} ${o.delay}ms ${ye} ${o.fill} ${ae}`;const qe=Le=>{Le===Object(Le)&&Le.animationName!==ae||(x.removeEventListener("animationend",qe),x.removeEventListener("animationcancel",qe),X(),ke.remove())};i.qMorphCancel=()=>{i.qMorphCancel=void 0,n=!0,qe()},x.qMorphCancel=()=>{x.qMorphCancel=void 0,n=!0,qe()},x.addEventListener("animationend",qe),x.addEventListener("animationcancel",qe),t=Le=>n===!0||!x||!P||!C?!1:Le===!0?(qe(),!0):(l=l!==!0,ye=ye==="normal"?"reverse":"normal",P.style.animationDirection=ye,z.style.animationDirection=ye,C.style.animationDirection=ye,x.style.animationDirection=ye,!0)}};o.waitFor>0||o.waitFor==="transitionend"||o.waitFor===Object(o.waitFor)&&typeof o.waitFor.then=="function"?(o.waitFor>0?new Promise(ue=>setTimeout(ue,o.waitFor)):o.waitFor==="transitionend"?new Promise(ue=>{const I=()=>{C!==null&&(clearTimeout(C),C=null),x&&(x.removeEventListener("transitionend",I),x.removeEventListener("transitioncancel",I)),ue()};let C=setTimeout(I,400);x.addEventListener("transitionend",I),x.addEventListener("transitioncancel",I)}):o.waitFor).then(V).catch(()=>{typeof x.qMorphCancel=="function"&&x.qMorphCancel()}):V()};return typeof e.onToggle=="function"&&e.onToggle(),requestAnimationFrame(T),x=>t(x)}var Bl={},ag=["duration","delay","easing","fill","classes","style","duration","resize","useCSS","hideFromClone","keepToClone","tween","tweenFromOpacity","tweenToOpacity","waitFor","onEnd"],lg=["resize","useCSS","hideFromClone","keepToClone","tween"];function la(e,t){e.clsAction!==t&&(e.clsAction=t,e.el.classList[t]("q-morph--invisible"))}function Qs(e){if(e.animating===!0||e.queue.length<2)return;const[t,n]=e.queue;e.animating=!0,t.animating=!0,n.animating=!0,la(t,"remove"),la(n,"remove");const l=ng({from:t.el,to:n.el,onToggle(){la(t,"add"),la(n,"remove")},...n.opts,onEnd(a,o){n.opts.onEnd!==void 0&&n.opts.onEnd(a,o),o!==!0&&(t.animating=!1,n.animating=!1,e.animating=!1,e.cancel=void 0,e.queue.shift(),Qs(e))}});e.cancel=()=>{l(!0),e.cancel=void 0}}function js(e,t){const n=t.opts;lg.forEach(l=>{n[l]=e[l]===!0})}function og(e,t){const n=typeof e=="string"&&e.length!==0?e.split(":"):[];t.name=n[0],t.group=n[1],Object.assign(t.opts,{duration:isNaN(n[2])===!0?300:parseFloat(n[2]),waitFor:n[3]})}function rg(e,t){e.group!==void 0&&(t.group=e.group),e.name!==void 0&&(t.name=e.name);const n=t.opts;ag.forEach(l=>{e[l]!==void 0&&(n[l]=e[l])})}function ig(e,t){if(t.name===e){const n=Bl[t.group];n===void 0?(Bl[t.group]={name:t.group,model:e,queue:[t],animating:!1},la(t,"remove")):n.model!==e&&(n.model=e,n.queue.push(t),n.animating===!1&&n.queue.length===2&&Qs(n));return}t.animating===!1&&la(t,"add")}function Di(e,t){let n;Object(t)===t?(n=""+t.model,rg(t,e),js(t,e)):n=""+t,n!==e.model?(e.model=n,ig(n,e)):e.animating===!1&&e.clsAction!==void 0&&e.el.classList[e.clsAction]("q-morph--invisible")}var M0=Xt({name:"morph",mounted(e,t){const n={el:e,animating:!1,opts:{}};js(t.modifiers,n),og(t.arg,n),Di(n,t.value),e.__qmorph=n},updated(e,t){Di(e.__qmorph,t.value)},beforeUnmount(e){const t=e.__qmorph,n=Bl[t.group];n!==void 0&&n.queue.indexOf(t)!==-1&&(n.queue=n.queue.filter(a=>a!==t),n.queue.length===0&&(n.cancel!==void 0&&n.cancel(),delete Bl[t.group])),t.clsAction==="add"&&e.classList.remove("q-morph--invisible"),delete e.__qmorph}}),ug={childList:!0,subtree:!0,attributes:!0,characterData:!0,attributeOldValue:!0,characterDataOldValue:!0};function Ii(e,t,n){t.handler=n,t.observer!==void 0&&t.observer.disconnect(),t.observer=new MutationObserver(l=>{typeof t.handler=="function"&&(t.handler(l)===!1||t.once===!0)&&Ks(e)}),t.observer.observe(e,t.opts)}function Ks(e){const t=e.__qmutation;t!==void 0&&(t.observer!==void 0&&t.observer.disconnect(),delete e.__qmutation)}var $0=Xt({name:"mutation",mounted(e,{modifiers:{once:t,...n},value:l}){const a={once:t,opts:Object.keys(n).length===0?ug:n};Ii(e,a,l),e.__qmutation=a},updated(e,{oldValue:t,value:n}){const l=e.__qmutation;l!==void 0&&t!==n&&Ii(e,l,n)},beforeUnmount:Ks}),{passive:Pl}=et;function Hi(e,{value:t,oldValue:n}){if(typeof t!="function"){e.scrollTarget.removeEventListener("scroll",e.scroll,Pl);return}e.handler=t,typeof n!="function"&&(e.scrollTarget.addEventListener("scroll",e.scroll,Pl),e.scroll())}var B0=Xt({name:"scroll-fire",mounted(e,t){const n={scrollTarget:Gt(e),scroll:fa(()=>{let l,a;n.scrollTarget===window?(a=e.getBoundingClientRect().bottom,l=window.innerHeight):(a=za(e).top+Fn(e),l=za(n.scrollTarget).top+Fn(n.scrollTarget)),a>0&&a<l&&(n.scrollTarget.removeEventListener("scroll",n.scroll,Pl),n.handler(e))},25)};Hi(n,t),e.__qscrollfire=n},updated(e,t){t.value!==t.oldValue&&Hi(e.__qscrollfire,t)},beforeUnmount(e){const t=e.__qscrollfire;t.scrollTarget.removeEventListener("scroll",t.scroll,Pl),t.scroll.cancel(),delete e.__qscrollfire}});function Ni(e,{value:t,oldValue:n}){if(typeof t!="function"){e.scrollTarget.removeEventListener("scroll",e.scroll,et.passive);return}e.handler=t,typeof n!="function"&&e.scrollTarget.addEventListener("scroll",e.scroll,et.passive)}var P0=Xt({name:"scroll",mounted(e,t){const n={scrollTarget:Gt(e),scroll(){n.handler(rn(n.scrollTarget),ja(n.scrollTarget))}};Ni(n,t),e.__qscroll=n},updated(e,t){e.__qscroll!==void 0&&t.oldValue!==t.value&&Ni(e.__qscroll,t)},beforeUnmount(e){const t=e.__qscroll;t.scrollTarget.removeEventListener("scroll",t.scroll,et.passive),delete e.__qscroll}}),E0=Xt({name:"touch-hold",beforeMount(e,t){const{modifiers:n}=t;if(n.mouse!==!0&&Ae.has.touch!==!0)return;const l={handler:t.value,noop:it,mouseStart(o){typeof l.handler=="function"&&Ia(o)===!0&&(vt(l,"temp",[[document,"mousemove","move","passiveCapture"],[document,"click","end","notPassiveCapture"]]),l.start(o,!0))},touchStart(o){if(o.target!==void 0&&typeof l.handler=="function"){const i=o.target;vt(l,"temp",[[i,"touchmove","move","passiveCapture"],[i,"touchcancel","end","notPassiveCapture"],[i,"touchend","end","notPassiveCapture"]]),l.start(o)}},start(o,i){l.origin=$t(o);const u=Date.now();Ae.is.mobile===!0&&(document.body.classList.add("non-selectable"),Wt(),l.styleCleanup=d=>{l.styleCleanup=void 0;const f=()=>{document.body.classList.remove("non-selectable")};d===!0?(Wt(),setTimeout(f,10)):f()}),l.triggered=!1,l.sensitivity=i===!0?l.mouseSensitivity:l.touchSensitivity,l.timer=setTimeout(()=>{l.timer=void 0,Wt(),l.triggered=!0,l.handler({evt:o,touch:i!==!0,mouse:i===!0,position:l.origin,duration:Date.now()-u})},l.duration)},move(o){const{top:i,left:u}=$t(o);l.timer!==void 0&&(Math.abs(u-l.origin.left)>=l.sensitivity||Math.abs(i-l.origin.top)>=l.sensitivity)&&(clearTimeout(l.timer),l.timer=void 0)},end(o){Mt(l,"temp"),l.styleCleanup!==void 0&&l.styleCleanup(l.triggered),l.triggered===!0?o!==void 0&&Fe(o):l.timer!==void 0&&(clearTimeout(l.timer),l.timer=void 0)}},a=[600,5,7];if(typeof t.arg=="string"&&t.arg.length!==0&&t.arg.split(":").forEach((o,i)=>{const u=parseInt(o,10);u&&(a[i]=u)}),[l.duration,l.touchSensitivity,l.mouseSensitivity]=a,e.__qtouchhold=l,n.mouse===!0){const o=n.mouseCapture===!0||n.mousecapture===!0?"Capture":"";vt(l,"main",[[e,"mousedown","mouseStart",`passive${o}`]])}Ae.has.touch===!0&&vt(l,"main",[[e,"touchstart","touchStart",`passive${n.capture===!0?"Capture":""}`],[e,"touchend","noop","notPassiveCapture"]])},updated(e,t){const n=e.__qtouchhold;n!==void 0&&t.oldValue!==t.value&&(typeof t.value!="function"&&n.end(),n.handler=t.value)},beforeUnmount(e){const t=e.__qtouchhold;t!==void 0&&(Mt(t,"main"),Mt(t,"temp"),t.timer!==void 0&&clearTimeout(t.timer),t.styleCleanup!==void 0&&t.styleCleanup(),delete e.__qtouchhold)}}),Ws={esc:27,tab:9,enter:13,space:32,up:38,left:37,right:39,down:40,delete:[8,46]},sg=new RegExp(`^([\\d+]+|${Object.keys(Ws).join("|")})$`,"i");function cg(e,t){const{top:n,left:l}=$t(e);return Math.abs(l-t.left)>=7||Math.abs(n-t.top)>=7}var L0=Xt({name:"touch-repeat",beforeMount(e,{modifiers:t,value:n,arg:l}){const a=Object.keys(t).reduce((d,f)=>{if(sg.test(f)===!0){const c=isNaN(parseInt(f,10))?Ws[f.toLowerCase()]:parseInt(f,10);c>=0&&d.push(c)}return d},[]);if(t.mouse!==!0&&Ae.has.touch!==!0&&a.length===0)return;const o=typeof l=="string"&&l.length!==0?l.split(":").map(d=>parseInt(d,10)):[0,600,300],i=o.length-1,u={keyboard:a,handler:n,noop:it,mouseStart(d){u.event===void 0&&typeof u.handler=="function"&&Ia(d)===!0&&(vt(u,"temp",[[document,"mousemove","move","passiveCapture"],[document,"click","end","notPassiveCapture"]]),u.start(d,!0))},keyboardStart(d){if(typeof u.handler=="function"&&Vt(d,a)===!0){if((o[0]===0||u.event!==void 0)&&(Fe(d),e.focus(),u.event!==void 0))return;vt(u,"temp",[[document,"keyup","end","notPassiveCapture"],[document,"click","end","notPassiveCapture"]]),u.start(d,!1,!0)}},touchStart(d){if(d.target!==void 0&&typeof u.handler=="function"){const f=d.target;vt(u,"temp",[[f,"touchmove","move","passiveCapture"],[f,"touchcancel","end","notPassiveCapture"],[f,"touchend","end","notPassiveCapture"]]),u.start(d)}},start(d,f,c){c!==!0&&(u.origin=$t(d));function h(v){u.styleCleanup=void 0,document.documentElement.style.cursor="";const g=()=>{document.body.classList.remove("non-selectable")};v===!0?(Wt(),setTimeout(g,10)):g()}Ae.is.mobile===!0&&(document.body.classList.add("non-selectable"),Wt(),u.styleCleanup=h),u.event={touch:f!==!0&&c!==!0,mouse:f===!0,keyboard:c===!0,startTime:Date.now(),repeatCount:0};const m=()=>{if(u.timer=void 0,u.event===void 0)return;u.event.repeatCount===0&&(u.event.evt=d,c===!0?u.event.keyCode=d.keyCode:u.event.position=$t(d),Ae.is.mobile!==!0&&(document.documentElement.style.cursor="pointer",document.body.classList.add("non-selectable"),Wt(),u.styleCleanup=h)),u.event.duration=Date.now()-u.event.startTime,u.event.repeatCount+=1,u.handler(u.event);const v=i<u.event.repeatCount?i:u.event.repeatCount;u.timer=setTimeout(m,o[v])};o[0]===0?m():u.timer=setTimeout(m,o[0])},move(d){u.event!==void 0&&u.timer!==void 0&&cg(d,u.origin)===!0&&(clearTimeout(u.timer),u.timer=void 0)},end(d){u.event!==void 0&&(u.styleCleanup!==void 0&&u.styleCleanup(!0),d!==void 0&&u.event.repeatCount>0&&Fe(d),Mt(u,"temp"),u.timer!==void 0&&(clearTimeout(u.timer),u.timer=void 0),u.event=void 0)}};if(e.__qtouchrepeat=u,t.mouse===!0){const d=t.mouseCapture===!0||t.mousecapture===!0?"Capture":"";vt(u,"main",[[e,"mousedown","mouseStart",`passive${d}`]])}if(Ae.has.touch===!0&&vt(u,"main",[[e,"touchstart","touchStart",`passive${t.capture===!0?"Capture":""}`],[e,"touchend","noop","passiveCapture"]]),a.length!==0){const d=t.keyCapture===!0||t.keycapture===!0?"Capture":"";vt(u,"main",[[e,"keydown","keyboardStart",`notPassive${d}`]])}},updated(e,{oldValue:t,value:n}){const l=e.__qtouchrepeat;l!==void 0&&t!==n&&(typeof n!="function"&&l.end(),l.handler=n)},beforeUnmount(e){const t=e.__qtouchrepeat;t!==void 0&&(t.timer!==void 0&&clearTimeout(t.timer),Mt(t,"main"),Mt(t,"temp"),t.styleCleanup!==void 0&&t.styleCleanup(),delete e.__qtouchrepeat)}});function dg(e,t=document.body){if(typeof e!="string")throw new TypeError("Expected a string as propName");if(!(t instanceof Element))throw new TypeError("Expected a DOM element");return getComputedStyle(t).getPropertyValue(`--q-${e}`).trim()||null}var rl;function fg(){return Ae.is.winphone?"msapplication-navbutton-color":Ae.is.safari?"apple-mobile-web-app-status-bar-style":"theme-color"}function vg(e){const t=document.getElementsByTagName("META");for(const n in t)if(t[n].name===e)return t[n]}function mg(e){rl===void 0&&(rl=fg());let t=vg(rl);const n=t===void 0;n&&(t=document.createElement("meta"),t.setAttribute("name",rl)),t.setAttribute("content",e),n&&document.head.appendChild(t)}var A0={set:Ae.is.mobile===!0&&(Ae.is.nativeMobile===!0||Ae.is.winphone===!0||Ae.is.safari===!0||Ae.is.webkit===!0||Ae.is.vivaldi===!0)?e=>{const t=e||dg("primary");Ae.is.nativeMobile===!0&&window.StatusBar?window.StatusBar.backgroundColorByHexString(t):mg(t)}:it,install({$q:e}){e.addressbarColor=this,e.config.addressbarColor&&this.set(e.config.addressbarColor)}},Sa={};function gg(e){Object.assign(qt,{request:e,exit:e,toggle:e})}function Us(){return document.fullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement||document.msFullscreenElement||null}function Ys(){const e=qt.activeEl=qt.isActive===!1?null:Us();$d(e===null||e===document.documentElement?document.body:e)}function hg(){qt.isActive=qt.isActive===!1,Ys()}function Qi(e,t){try{const n=e[t]();return n===void 0?Promise.resolve():n}catch(n){return Promise.reject(n)}}var qt=pn({isActive:!1,activeEl:null},{isCapable:!1,install({$q:e}){e.fullscreen=this}});Sa.request=["requestFullscreen","msRequestFullscreen","mozRequestFullScreen","webkitRequestFullscreen"].find(e=>document.documentElement[e]!==void 0),qt.isCapable=Sa.request!==void 0,qt.isCapable===!1?gg(()=>Promise.reject("Not capable")):(Object.assign(qt,{request(e){const t=e||document.documentElement,{activeEl:n}=qt;return t===n?Promise.resolve():(n!==null&&t.contains(n)===!0?qt.exit():Promise.resolve()).finally(()=>Qi(t,Sa.request))},exit(){return qt.isActive===!0?Qi(document,Sa.exit):Promise.resolve()},toggle(e){return qt.isActive===!0?qt.exit():qt.request(e)}}),Sa.exit=["exitFullscreen","msExitFullscreen","mozCancelFullScreen","webkitExitFullscreen"].find(e=>document[e]),qt.isActive=Boolean(Us()),qt.isActive===!0&&Ys(),["onfullscreenchange","onmsfullscreenchange","onwebkitfullscreenchange"].forEach(e=>{document[e]=hg}));var z0=qt,Xs=pn({appVisible:!0},{install({$q:e}){St(e,"appVisible",()=>this.appVisible)}});{let e,t;if(typeof document.hidden!="undefined"?(e="hidden",t="visibilitychange"):typeof document.msHidden!="undefined"?(e="msHidden",t="msvisibilitychange"):typeof document.webkitHidden!="undefined"&&(e="webkitHidden",t="webkitvisibilitychange"),t&&typeof document[e]!="undefined"){const n=()=>{Xs.appVisible=!document[e]};document.addEventListener(t,n,!1)}}var O0=Xs,bg=te({name:"BottomSheetComponent",props:{...Ke,title:String,message:String,actions:Array,grid:Boolean,cardClass:[String,Array,Object],cardStyle:[String,Array,Object]},emits:["ok","hide"],setup(e,{emit:t}){const{proxy:n}=ve(),l=We(e,n.$q),a=O(null);function o(){a.value.show()}function i(){a.value.hide()}function u(v){t("ok",v),i()}function d(){t("hide")}function f(){return e.actions.map(v=>{const g=v.avatar||v.img;return v.label===void 0?r(Dn,{class:"col-all",dark:l.value}):r("div",{class:["q-bottom-sheet__item q-hoverable q-focusable cursor-pointer relative-position",v.class],style:v.style,tabindex:0,role:"listitem",onClick(){u(v)},onKeyup(_){_.keyCode===13&&u(v)}},[r("div",{class:"q-focus-helper"}),v.icon?r(je,{name:v.icon,color:v.color}):g?r("img",{class:v.avatar?"q-bottom-sheet__avatar":"",src:g}):r("div",{class:"q-bottom-sheet__empty-icon"}),r("div",v.label)])})}function c(){return e.actions.map(v=>{const g=v.avatar||v.img;return v.label===void 0?r(Dn,{spaced:!0,dark:l.value}):r(Il,{class:["q-bottom-sheet__item",v.classes],style:v.style,tabindex:0,clickable:!0,dark:l.value,onClick(){u(v)}},()=>[r(gn,{avatar:!0},()=>v.icon?r(je,{name:v.icon,color:v.color}):g?r("img",{class:v.avatar?"q-bottom-sheet__avatar":"",src:g}):null),r(gn,()=>v.label)])})}function h(){const v=[];return e.title&&v.push(r(Mn,{class:"q-dialog__title"},()=>e.title)),e.message&&v.push(r(Mn,{class:"q-dialog__message"},()=>e.message)),v.push(e.grid===!0?r("div",{class:"row items-stretch justify-start",role:"list"},f()):r("div",{role:"list"},c())),v}function m(){return[r(Vu,{class:[`q-bottom-sheet q-bottom-sheet--${e.grid===!0?"grid":"list"}`+(l.value===!0?" q-bottom-sheet--dark q-dark":""),e.cardClass],style:e.cardStyle},h)]}return Object.assign(n,{show:o,hide:i}),()=>r(Dl,{ref:a,position:"bottom",onHide:d},m)}});function Gs(e,t){for(const n in t)n!=="spinner"&&Object(t[n])===t[n]?(e[n]=Object(e[n])!==e[n]?{}:{...e[n]},Gs(e[n],t[n])):e[n]=t[n]}function Zs(e,t,n){return l=>{let a,o;const i=t===!0&&l.component!==void 0;if(i===!0){const{component:w,componentProps:y}=l;a=typeof w=="string"?n.component(w):w,o=y||{}}else{const{class:w,style:y,...b}=l;a=e,o=b,w!==void 0&&(b.cardClass=w),y!==void 0&&(b.cardStyle=y)}let u,d=!1;const f=O(null),c=Qa(!1,"dialog"),h=w=>{if(f.value!==null&&f.value[w]!==void 0){f.value[w]();return}const y=u.$.subTree;if(y&&y.component){if(y.component.proxy&&y.component.proxy[w]){y.component.proxy[w]();return}if(y.component.subTree&&y.component.subTree.component&&y.component.subTree.component.proxy&&y.component.subTree.component.proxy[w]){y.component.subTree.component.proxy[w]();return}}console.error("[Quasar] Incorrectly defined Dialog component")},m=[],v=[],g={onOk(w){return m.push(w),g},onCancel(w){return v.push(w),g},onDismiss(w){return m.push(w),v.push(w),g},hide(){return h("hide"),g},update(w){if(u!==null){if(i===!0)Object.assign(o,w);else{const{class:y,style:b,...S}=w;y!==void 0&&(S.cardClass=y),b!==void 0&&(S.cardStyle=b),Gs(o,S)}u.$forceUpdate()}return g}},_=w=>{d=!0,m.forEach(y=>{y(w)})},p=()=>{k.unmount(c),Wo(c),k=null,u=null,d!==!0&&v.forEach(w=>{w()})};let k=Al({name:"QGlobalDialog",setup:()=>()=>r(a,{...o,ref:f,onOk:_,onHide:p,onVnodeMounted(...w){typeof o.onVnodeMounted=="function"&&o.onVnodeMounted(...w),Qe(()=>h("show"))}})},n);return u=k.mount(c),g}}var R0={install({$q:e,parentApp:t}){e.bottomSheet=this.create=Zs(bg,!1,t)}};function Js(e){return encodeURIComponent(e)}function ec(e){return decodeURIComponent(e)}function yg(e){return Js(e===Object(e)?JSON.stringify(e):""+e)}function pg(e){if(e==="")return e;e.indexOf('"')===0&&(e=e.slice(1,-1).replace(/\\"/g,'"').replace(/\\\\/g,"\\")),e=ec(e.replace(/\+/g," "));try{const t=JSON.parse(e);(t===Object(t)||Array.isArray(t)===!0)&&(e=t)}catch{}return e}function tc(e){const t=new Date;return t.setMilliseconds(t.getMilliseconds()+e),t.toUTCString()}function wg(e){let t=0;const n=e.match(/(\d+)d/),l=e.match(/(\d+)h/),a=e.match(/(\d+)m/),o=e.match(/(\d+)s/);return n&&(t+=n[1]*864e5),l&&(t+=l[1]*36e5),a&&(t+=a[1]*6e4),o&&(t+=o[1]*1e3),t===0?e:tc(t)}function nc(e,t,n={},l){let a,o;n.expires!==void 0&&(Object.prototype.toString.call(n.expires)==="[object Date]"?a=n.expires.toUTCString():typeof n.expires=="string"?a=wg(n.expires):(o=parseFloat(n.expires),a=isNaN(o)===!1?tc(o*864e5):n.expires));const i=`${Js(e)}=${yg(t)}`,u=[i,a!==void 0?"; Expires="+a:"",n.path?"; Path="+n.path:"",n.domain?"; Domain="+n.domain:"",n.sameSite?"; SameSite="+n.sameSite:"",n.httpOnly?"; HttpOnly":"",n.secure?"; Secure":"",n.other?"; "+n.other:""].join("");if(l){l.req.qCookies?l.req.qCookies.push(u):l.req.qCookies=[u],l.res.setHeader("Set-Cookie",l.req.qCookies);let d=l.req.headers.cookie||"";if(a!==void 0&&o<0){const f=El(e,l);f!==void 0&&(d=d.replace(`${e}=${f}; `,"").replace(`; ${e}=${f}`,"").replace(`${e}=${f}`,""))}else d=d?`${i}; ${d}`:u;l.req.headers.cookie=d}else document.cookie=u}function El(e,t){const n=t?t.req.headers:document,l=n.cookie?n.cookie.split("; "):[],a=l.length;let o=e?null:{},i=0,u,d,f;for(;i<a;i++)if(u=l[i].split("="),d=ec(u.shift()),f=u.join("="),!e)o[d]=f;else if(e===d){o=pg(f);break}return o}function _g(e,t,n){nc(e,"",{expires:-1,...t},n)}function Sg(e,t){return El(e,t)!==null}function xg(e){return{get:t=>El(t,e),set:(t,n,l)=>nc(t,n,l,e),has:t=>Sg(t,e),remove:(t,n)=>_g(t,n,e),getAll:()=>El(null,e)}}var ac={install({$q:e,ssrContext:t}){e.cookies=this}};Object.assign(ac,xg());var F0=ac,Cg=te({name:"DialogPluginComponent",props:{...Ke,title:String,message:String,prompt:Object,options:Object,progress:[Boolean,Object],html:Boolean,ok:{type:[String,Object,Boolean],default:!0},cancel:[String,Object,Boolean],focus:{type:String,default:"ok",validator:e=>["ok","cancel","none"].includes(e)},stackButtons:Boolean,color:String,cardClass:[String,Array,Object],cardStyle:[String,Array,Object]},emits:["ok","hide"],setup(e,{emit:t}){const{proxy:n}=ve(),{$q:l}=n,a=We(e,l),o=O(null),i=O(e.prompt!==void 0?e.prompt.model:e.options!==void 0?e.options.model:void 0),u=s(()=>"q-dialog-plugin"+(a.value===!0?" q-dialog-plugin--dark q-dark":"")+(e.progress!==!1?" q-dialog-plugin--progress":"")),d=s(()=>e.color||(a.value===!0?"amber":"primary")),f=s(()=>e.progress===!1?null:yt(e.progress)===!0?{component:e.progress.spinner||It,props:{color:e.progress.color||d.value}}:{component:It,props:{color:d.value}}),c=s(()=>e.prompt!==void 0||e.options!==void 0),h=s(()=>{if(c.value!==!0)return{};const{model:q,isValid:U,items:W,...E}=e.prompt!==void 0?e.prompt:e.options;return E}),m=s(()=>yt(e.ok)===!0||e.ok===!0?l.lang.label.ok:e.ok),v=s(()=>yt(e.cancel)===!0||e.cancel===!0?l.lang.label.cancel:e.cancel),g=s(()=>e.prompt!==void 0?e.prompt.isValid!==void 0&&e.prompt.isValid(i.value)!==!0:e.options!==void 0?e.options.isValid!==void 0&&e.options.isValid(i.value)!==!0:!1),_=s(()=>({color:d.value,label:m.value,ripple:!1,disable:g.value,...yt(e.ok)===!0?e.ok:{flat:!0},"data-autofocus":e.focus==="ok"&&c.value!==!0||void 0,onClick:y})),p=s(()=>({color:d.value,label:v.value,ripple:!1,...yt(e.cancel)===!0?e.cancel:{flat:!0},"data-autofocus":e.focus==="cancel"&&c.value!==!0||void 0,onClick:b}));ne(()=>e.prompt&&e.prompt.model,B),ne(()=>e.options&&e.options.model,B);function k(){o.value.show()}function w(){o.value.hide()}function y(){t("ok",an(i.value)),w()}function b(){w()}function S(){t("hide")}function B(q){i.value=q}function L(q){g.value!==!0&&e.prompt.type!=="textarea"&&Vt(q,13)===!0&&y()}function R(q,U){return e.html===!0?r(Mn,{class:q,innerHTML:U}):r(Mn,{class:q},()=>U)}function A(){return[r($s,{color:d.value,dense:!0,autofocus:!0,dark:a.value,...h.value,modelValue:i.value,"onUpdate:modelValue":B,onKeyup:L})]}function P(){return[r(zv,{color:d.value,options:e.options.items,dark:a.value,...h.value,modelValue:i.value,"onUpdate:modelValue":B})]}function z(){const q=[];return e.cancel&&q.push(r(Xe,p.value)),e.ok&&q.push(r(Xe,_.value)),r(Wd,{class:e.stackButtons===!0?"items-end":"",vertical:e.stackButtons,align:"right"},()=>q)}function T(){const q=[];return e.title&&q.push(R("q-dialog__title",e.title)),e.progress!==!1&&q.push(r(Mn,{class:"q-dialog__progress"},()=>r(f.value.component,f.value.props))),e.message&&q.push(R("q-dialog__message",e.message)),e.prompt!==void 0?q.push(r(Mn,{class:"scroll q-dialog-plugin__form"},A)):e.options!==void 0&&q.push(r(Dn,{dark:a.value}),r(Mn,{class:"scroll q-dialog-plugin__form"},P),r(Dn,{dark:a.value})),(e.ok||e.cancel)&&q.push(z()),q}function x(){return[r(Vu,{class:[u.value,e.cardClass],style:e.cardStyle,dark:a.value},T)]}return Object.assign(n,{show:k,hide:w}),()=>r(Dl,{ref:o,onHide:S},x)}}),V0={install({$q:e,parentApp:t}){e.dialog=this.create=Zs(Cg,!0,t)}},Jn,mo,ji=0,qn=null,xt={},Bn={},lc={group:"__default_quasar_group__",delay:0,message:!1,html:!1,spinnerSize:80,spinnerColor:"",messageColor:"",backgroundColor:"",boxClass:"",spinner:It,customClass:""},oc={...lc};function kg(e){if(e&&e.group!==void 0&&Bn[e.group]!==void 0)return Object.assign(Bn[e.group],e);const t=yt(e)===!0&&e.ignoreDefaults===!0?{...lc,...e}:{...oc,...e};return Bn[t.group]=t,t}var Nt=pn({isActive:!1},{show(e){xt=kg(e);const{group:t}=xt;return Nt.isActive=!0,Jn!==void 0?(xt.uid=ji,mo.$forceUpdate()):(xt.uid=++ji,qn!==null&&clearTimeout(qn),qn=setTimeout(()=>{qn=null;const n=Qa("q-loading");Jn=Al({name:"QLoading",setup(){ft(()=>{Mo(!0)});function l(){Nt.isActive!==!0&&Jn!==void 0&&(Mo(!1),Jn.unmount(n),Wo(n),Jn=void 0,mo=void 0)}function a(){if(Nt.isActive!==!0)return null;const o=[r(xt.spinner,{class:"q-loading__spinner",color:xt.spinnerColor,size:xt.spinnerSize})];return xt.message&&o.push(r("div",{class:"q-loading__message"+(xt.messageColor?` text-${xt.messageColor}`:""),[xt.html===!0?"innerHTML":"textContent"]:xt.message})),r("div",{class:"q-loading fullscreen flex flex-center z-max "+xt.customClass.trim(),key:xt.uid},[r("div",{class:"q-loading__backdrop"+(xt.backgroundColor?` bg-${xt.backgroundColor}`:"")}),r("div",{class:"q-loading__box column items-center "+xt.boxClass},o)])}return()=>r(_t,{name:"q-transition--fade",appear:!0,onAfterLeave:l},a)}},Nt.__parentApp),mo=Jn.mount(n)},xt.delay)),n=>{if(n===void 0||Object(n)!==n){Nt.hide(t);return}Nt.show({...n,group:t})}},hide(e){if(Nt.isActive===!0){if(e===void 0)Bn={};else{if(Bn[e]===void 0)return;{delete Bn[e];const t=Object.keys(Bn);if(t.length!==0){const n=t[t.length-1];Nt.show({group:n});return}}}qn!==null&&(clearTimeout(qn),qn=null),Nt.isActive=!1}},setDefaults(e){yt(e)===!0&&Object.assign(oc,e)},install({$q:e,parentApp:t}){e.loading=this,Nt.__parentApp=t,e.config.loading!==void 0&&this.setDefaults(e.config.loading)}}),D0=Nt,il=O(null),Lo=pn({isActive:!1},{start:it,stop:it,increment:it,setDefaults:it,install({$q:e,parentApp:t}){if(e.loadingBar=this,this.__installed===!0){e.config.loadingBar!==void 0&&this.setDefaults(e.config.loadingBar);return}const n=O(e.config.loadingBar!==void 0?{...e.config.loadingBar}:{});function l(){Lo.isActive=!0}function a(){Lo.isActive=!1}const o=Qa("q-loading-bar");Al({name:"LoadingBar",devtools:{hide:!0},setup:()=>()=>r(td,{...n.value,onStart:l,onStop:a,ref:il})},t).mount(o),Object.assign(this,{start(i){il.value.start(i)},stop(){il.value.stop()},increment(){il.value.increment.apply(null,arguments)},setDefaults(i){yt(i)===!0&&Object.assign(n.value,i)}})}}),I0=Lo,fl=null,Ao,ln=[];function qg(e){e.title&&(e.title=e.titleTemplate?e.titleTemplate(e.title):e.title,delete e.titleTemplate),[["meta","content"],["link","href"]].forEach(t=>{const n=e[t[0]],l=t[1];for(const a in n){const o=n[a];o.template&&(Object.keys(o).length===1?delete n[a]:(o[l]=o.template(o[l]||""),delete o.template))}})}function Tg(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!0;for(const n in e)if(e[n]!==t[n])return!0}function Ki(e){return["class","style"].includes(e)===!1}function Wi(e){return["lang","dir"].includes(e)===!1}function Mg(e,t){const n={},l={};return e===void 0?{add:t,remove:l}:(e.title!==t.title&&(n.title=t.title),["meta","link","script","htmlAttr","bodyAttr"].forEach(a=>{const o=e[a],i=t[a];if(l[a]=[],o==null){n[a]=i;return}n[a]={};for(const u in o)i.hasOwnProperty(u)===!1&&l[a].push(u);for(const u in i)o.hasOwnProperty(u)===!1?n[a][u]=i[u]:Tg(o[u],i[u])===!0&&(l[a].push(u),n[a][u]=i[u])}),{add:n,remove:l})}function $g({add:e,remove:t}){e.title&&(document.title=e.title),Object.keys(t).length!==0&&(["meta","link","script"].forEach(n=>{t[n].forEach(l=>{document.head.querySelector(`${n}[data-qmeta="${l}"]`).remove()})}),t.htmlAttr.filter(Wi).forEach(n=>{document.documentElement.removeAttribute(n)}),t.bodyAttr.filter(Ki).forEach(n=>{document.body.removeAttribute(n)})),["meta","link","script"].forEach(n=>{const l=e[n];for(const a in l){const o=document.createElement(n);for(const i in l[a])i!=="innerHTML"&&o.setAttribute(i,l[a][i]);o.setAttribute("data-qmeta",a),n==="script"&&(o.innerHTML=l[a].innerHTML||""),document.head.appendChild(o)}}),Object.keys(e.htmlAttr).filter(Wi).forEach(n=>{document.documentElement.setAttribute(n,e.htmlAttr[n]||"")}),Object.keys(e.bodyAttr).filter(Ki).forEach(n=>{document.body.setAttribute(n,e.bodyAttr[n]||"")})}function Bg(){fl=null;const e={title:"",titleTemplate:null,meta:{},link:{},script:{},htmlAttr:{},bodyAttr:{}};for(let t=0;t<ln.length;t++){const{active:n,val:l}=ln[t];n===!0&&br(!0,e,l)}qg(e),$g(Mg(Ao,e)),Ao=e}function Kt(){fl!==null&&clearTimeout(fl),fl=setTimeout(Bg,50)}var H0={install(e){this.__installed!==!0&&Pt.value===!0&&(Ao=window.__Q_META__,document.getElementById("qmeta-init").remove())}},Pg=0,vl={},ml={},Qt={},rc={},Eg=/^\s*$/,ic=[],Lg=[void 0,null,!0,!1,""],_r=["top-left","top-right","bottom-left","bottom-right","top","bottom","left","right","center"],Ag=["top-left","top-right","bottom-left","bottom-right"],ea={positive:{icon:e=>e.iconSet.type.positive,color:"positive"},negative:{icon:e=>e.iconSet.type.negative,color:"negative"},warning:{icon:e=>e.iconSet.type.warning,color:"warning",textColor:"dark"},info:{icon:e=>e.iconSet.type.info,color:"info"},ongoing:{group:!1,timeout:0,spinner:!0,color:"grey-8"}};function uc(e,t,n){if(!e)return xa("parameter required");let l;const a={textColor:"white"};if(e.ignoreDefaults!==!0&&Object.assign(a,vl),yt(e)===!1&&(a.type&&Object.assign(a,ea[a.type]),e={message:e}),Object.assign(a,ea[e.type||a.type],e),typeof a.icon=="function"&&(a.icon=a.icon(t)),a.spinner?(a.spinner===!0&&(a.spinner=It),a.spinner=Oo(a.spinner)):a.spinner=!1,a.meta={hasMedia:Boolean(a.spinner!==!1||a.icon||a.avatar),hasText:Ui(a.message)||Ui(a.caption)},a.position){if(_r.includes(a.position)===!1)return xa("wrong position",e)}else a.position="bottom";if(Lg.includes(a.timeout)===!0)a.timeout=5e3;else{const d=Number(a.timeout);if(isNaN(d)||d<0)return xa("wrong timeout",e);a.timeout=Number.isFinite(d)?d:0}a.timeout===0?a.progress=!1:a.progress===!0&&(a.meta.progressClass="q-notification__progress"+(a.progressClass?` ${a.progressClass}`:""),a.meta.progressStyle={animationDuration:`${a.timeout+1e3}ms`});const o=(Array.isArray(e.actions)===!0?e.actions:[]).concat(e.ignoreDefaults!==!0&&Array.isArray(vl.actions)===!0?vl.actions:[]).concat(ea[e.type]!==void 0&&Array.isArray(ea[e.type].actions)===!0?ea[e.type].actions:[]),{closeBtn:i}=a;if(i&&o.push({label:typeof i=="string"?i:t.lang.label.close}),a.actions=o.map(({handler:d,noDismiss:f,...c})=>({flat:!0,...c,onClick:typeof d=="function"?()=>{d(),f!==!0&&u()}:()=>{u()}})),a.multiLine===void 0&&(a.multiLine=a.actions.length>1),Object.assign(a.meta,{class:`q-notification row items-stretch q-notification--${a.multiLine===!0?"multi-line":"standard"}`+(a.color!==void 0?` bg-${a.color}`:"")+(a.textColor!==void 0?` text-${a.textColor}`:"")+(a.classes!==void 0?` ${a.classes}`:""),wrapperClass:"q-notification__wrapper col relative-position border-radius-inherit "+(a.multiLine===!0?"column no-wrap justify-center":"row items-center"),contentClass:"q-notification__content row items-center"+(a.multiLine===!0?"":" col"),leftClass:a.meta.hasText===!0?"additional":"single",attrs:{role:"alert",...a.attrs}}),a.group===!1?(a.group=void 0,a.meta.group=void 0):((a.group===void 0||a.group===!0)&&(a.group=[a.message,a.caption,a.multiline].concat(a.actions.map(d=>`${d.label}*${d.icon}`)).join("|")),a.meta.group=a.group+"|"+a.position),a.actions.length===0?a.actions=void 0:a.meta.actionsClass="q-notification__actions row items-center "+(a.multiLine===!0?"justify-end":"col-auto")+(a.meta.hasMedia===!0?" q-notification__actions--with-media":""),n!==void 0){n.notif.meta.timer&&(clearTimeout(n.notif.meta.timer),n.notif.meta.timer=void 0),a.meta.uid=n.notif.meta.uid;const d=Qt[a.position].value.indexOf(n.notif);Qt[a.position].value[d]=a}else{const d=ml[a.meta.group];if(d===void 0){if(a.meta.uid=Pg++,a.meta.badge=1,["left","right","center"].indexOf(a.position)!==-1)Qt[a.position].value.splice(Math.floor(Qt[a.position].value.length/2),0,a);else{const f=a.position.indexOf("top")!==-1?"unshift":"push";Qt[a.position].value[f](a)}a.group!==void 0&&(ml[a.meta.group]=a)}else{if(d.meta.timer&&(clearTimeout(d.meta.timer),d.meta.timer=void 0),a.badgePosition!==void 0){if(Ag.includes(a.badgePosition)===!1)return xa("wrong badgePosition",e)}else a.badgePosition=`top-${a.position.indexOf("left")!==-1?"right":"left"}`;a.meta.uid=d.meta.uid,a.meta.badge=d.meta.badge+1,a.meta.badgeClass=`q-notification__badge q-notification__badge--${a.badgePosition}`+(a.badgeColor!==void 0?` bg-${a.badgeColor}`:"")+(a.badgeTextColor!==void 0?` text-${a.badgeTextColor}`:"")+(a.badgeClass?` ${a.badgeClass}`:"");const f=Qt[a.position].value.indexOf(d);Qt[a.position].value[f]=ml[a.meta.group]=a}}const u=()=>{zg(a),l=void 0};if(a.timeout>0&&(a.meta.timer=setTimeout(()=>{a.meta.timer=void 0,u()},a.timeout+1e3)),a.group!==void 0)return d=>{d!==void 0?xa("trying to update a grouped one which is forbidden",e):u()};if(l={dismiss:u,config:e,notif:a},n!==void 0){Object.assign(n,l);return}return d=>{if(l!==void 0)if(d===void 0)l.dismiss();else{const f=Object.assign({},l.config,d,{group:!1,position:a.position});uc(f,t,l)}}}function zg(e){e.meta.timer&&(clearTimeout(e.meta.timer),e.meta.timer=void 0);const t=Qt[e.position].value.indexOf(e);if(t!==-1){e.group!==void 0&&delete ml[e.meta.group];const n=ic[""+e.meta.uid];if(n){const{width:l,height:a}=getComputedStyle(n);n.style.left=`${n.offsetLeft}px`,n.style.width=l,n.style.height=a}Qt[e.position].value.splice(t,1),typeof e.onDismiss=="function"&&e.onDismiss()}}function Ui(e){return e!=null&&Eg.test(e)!==!0}function xa(e,t){return console.error(`Notify: ${e}`,t),!1}function Og(){return te({name:"QNotifications",devtools:{hide:!0},setup(){return()=>r("div",{class:"q-notifications"},_r.map(e=>r(Cc,{key:e,class:rc[e],tag:"div",name:`q-notification--${e}`},()=>Qt[e].value.map(t=>{const n=t.meta,l=[];if(n.hasMedia===!0&&(t.spinner!==!1?l.push(r(t.spinner,{class:"q-notification__spinner q-notification__spinner--"+n.leftClass,color:t.spinnerColor,size:t.spinnerSize})):t.icon?l.push(r(je,{class:"q-notification__icon q-notification__icon--"+n.leftClass,name:t.icon,color:t.iconColor,size:t.iconSize,role:"img"})):t.avatar&&l.push(r(sd,{class:"q-notification__avatar q-notification__avatar--"+n.leftClass},()=>r("img",{src:t.avatar,"aria-hidden":"true"})))),n.hasText===!0){let o;const i={class:"q-notification__message col"};if(t.html===!0)i.innerHTML=t.caption?`<div>${t.message}</div><div class="q-notification__caption">${t.caption}</div>`:t.message;else{const u=[t.message];o=t.caption?[r("div",u),r("div",{class:"q-notification__caption"},[t.caption])]:u}l.push(r("div",i,o))}const a=[r("div",{class:n.contentClass},l)];return t.progress===!0&&a.push(r("div",{key:`${n.uid}|p|${n.badge}`,class:n.progressClass,style:n.progressStyle})),t.actions!==void 0&&a.push(r("div",{class:n.actionsClass},t.actions.map(o=>r(Xe,o)))),n.badge>1&&a.push(r("div",{key:`${n.uid}|${n.badge}`,class:t.meta.badgeClass,style:t.badgeStyle},[n.badge])),r("div",{ref:o=>{ic[""+n.uid]=o},key:n.uid,class:n.class,...n.attrs},[r("div",{class:n.wrapperClass},a)])}))))}})}var N0={setDefaults(e){yt(e)===!0&&Object.assign(vl,e)},registerType(e,t){yt(t)===!0&&(ea[e]=t)},install({$q:e,parentApp:t}){if(e.notify=this.create=n=>uc(n,e),e.notify.setDefaults=this.setDefaults,e.notify.registerType=this.registerType,e.config.notify!==void 0&&this.setDefaults(e.config.notify),this.__installed!==!0){_r.forEach(l=>{Qt[l]=O([]);const a=["left","center","right"].includes(l)===!0?"center":l.indexOf("top")!==-1?"top":"bottom",o=l.indexOf("left")!==-1?"start":l.indexOf("right")!==-1?"end":"center",i=["left","right"].includes(l)?`items-${l==="left"?"start":"end"} justify-center`:l==="center"?"flex-center":`items-${o}`;rc[l]=`q-notifications__list q-notifications__list--${a} fixed column no-wrap ${i}`});const n=Qa("q-notify");Al(Og(),t).mount(n)}}};function Rg(e){return sa(e)===!0?"__q_date|"+e.getTime():mu(e)===!0?"__q_expr|"+e.source:typeof e=="number"?"__q_numb|"+e:typeof e=="boolean"?"__q_bool|"+(e?"1":"0"):typeof e=="string"?"__q_strn|"+e:typeof e=="function"?"__q_strn|"+e.toString():e===Object(e)?"__q_objt|"+JSON.stringify(e):e}function Fg(e){if(e.length<9)return e;const n=e.substring(0,8),l=e.substring(9);switch(n){case"__q_date":const a=Number(l);return new Date(Number.isNaN(a)===!0?l:a);case"__q_expr":return new RegExp(l);case"__q_numb":return Number(l);case"__q_bool":return Boolean(l==="1");case"__q_strn":return""+l;case"__q_objt":return JSON.parse(l);default:return e}}function sc(){const e=()=>null;return{has:()=>!1,hasItem:()=>!1,getLength:()=>0,getItem:e,getIndex:e,getKey:e,getAll:()=>{},getAllKeys:()=>[],set:it,setItem:it,remove:it,removeItem:it,clear:it,isEmpty:()=>!0}}function cc(e){const t=window[e+"Storage"],n=i=>{const u=t.getItem(i);return u?Fg(u):null},l=i=>t.getItem(i)!==null,a=(i,u)=>{t.setItem(i,Rg(u))},o=i=>{t.removeItem(i)};return{has:l,hasItem:l,getLength:()=>t.length,getItem:n,getIndex:i=>i<t.length?n(t.key(i)):null,getKey:i=>i<t.length?t.key(i):null,getAll:()=>{let i;const u={},d=t.length;for(let f=0;f<d;f++)i=t.key(f),u[i]=n(i);return u},getAllKeys:()=>{const i=[],u=t.length;for(let d=0;d<u;d++)i.push(t.key(d));return i},set:a,setItem:a,remove:o,removeItem:o,clear:()=>{t.clear()},isEmpty:()=>t.length===0}}var dc=Ae.has.webStorage===!1?sc():cc("local"),fc={install({$q:e}){e.localStorage=dc}};Object.assign(fc,dc);var Q0=fc,vc=Ae.has.webStorage===!1?sc():cc("session"),mc={install({$q:e}){e.sessionStorage=vc}};Object.assign(mc,vc);var j0=mc;function gc(){const{emit:e,proxy:t}=ve(),n=O(null);function l(){n.value.show()}function a(){n.value.hide()}function o(u){e("ok",u),a()}function i(){e("hide")}return Object.assign(t,{show:l,hide:a}),{dialogRef:n,onDialogHide:i,onDialogOK:o,onDialogCancel:a}}var hc=["ok","hide"];gc.emits=hc;gc.emitsObject=Ns(hc);function K0(e){{const t={active:!0};if(typeof e=="function"){const n=s(e);t.val=n.value,ne(n,l=>{t.val=l,t.active===!0&&Kt()})}else t.val=e;ln.push(t),Kt(),bn(()=>{t.active=!0,Kt()}),Yt(()=>{t.active=!1,Kt()}),zo(()=>{ln.splice(ln.indexOf(t),1),Kt()})}}function W0(){return Et(ru)}function U0(){let e=null;const t=ve();function n(){e!==null&&(clearInterval(e),e=null)}return Yt(n),Ne(n),{removeInterval:n,registerInterval(l,a){n(),on(t)===!1&&(e=setInterval(l,a))}}}function Vg(e){const t=document.createElement("textarea");t.value=e,t.contentEditable="true",t.style.position="fixed";const n=()=>{};Go(n),document.body.appendChild(t),t.focus(),t.select();const l=document.execCommand("copy");return t.remove(),wl(n),l}function Y0(e){return navigator.clipboard!==void 0?navigator.clipboard.writeText(e):new Promise((t,n)=>{const l=Vg(e);l?t(!0):n(l)})}var X0=e=>{const t={activated(){this.__qMeta.active=!0,Kt()},deactivated(){this.__qMeta.active=!1,Kt()},unmounted(){ln.splice(ln.indexOf(this.__qMeta),1),Kt(),this.__qMeta=void 0}};return typeof e=="function"?Object.assign(t,{computed:{__qMetaOptions(){return e.call(this)||{}}},watch:{__qMetaOptions(n){this.__qMeta.val=n,this.__qMeta.active===!0&&Kt()}},created(){this.__qMeta={active:!0,val:this.__qMetaOptions},ln.push(this.__qMeta),Kt()}}):t.created=function(){this.__qMeta={active:!0,val:e},ln.push(this.__qMeta),Kt()},t},G0=class{constructor(){this.__stack={}}on(e,t,n){return(this.__stack[e]||(this.__stack[e]=[])).push({fn:t,ctx:n}),this}once(e,t,n){const l=(...a)=>{this.off(e,l),t.apply(n,a)};return l.__callback=t,this.on(e,l,n)}emit(e){const t=this.__stack[e];if(t!==void 0){const n=[].slice.call(arguments,1);t.forEach(l=>{l.fn.apply(l.ctx,n)})}return this}off(e,t){const n=this.__stack[e];if(n===void 0)return this;if(t===void 0)return delete this.__stack[e],this;const l=n.filter(a=>a.fn!==t&&a.fn.__callback!==t);return l.length!==0?this.__stack[e]=l:delete this.__stack[e],this}};function Yi(e){setTimeout(()=>{window.URL.revokeObjectURL(e.href)},1e4),e.remove()}function Z0(e,t,n={}){const{mimeType:l,byteOrderMark:a,encoding:o}=typeof n=="string"?{mimeType:n}:n,i=o!==void 0?new TextEncoder(o).encode([t]):t,u=a!==void 0?[a,i]:[i],d=new Blob(u,{type:l||"application/octet-stream"}),f=document.createElement("a");f.href=window.URL.createObjectURL(d),f.setAttribute("download",e),typeof f.download=="undefined"&&f.setAttribute("target","_blank"),f.classList.add("hidden"),f.style.position="fixed",document.body.appendChild(f);try{return f.click(),Yi(f),!0}catch(c){return Yi(f),c}}function Dg(e){const t=Object.assign({noopener:!0},e),n=[];for(const l in t){const a=t[l];a===!0?n.push(l):(Rn(a)||typeof a=="string"&&a!=="")&&n.push(l+"="+a)}return n.join(",")}function Xi(e,t,n){let l=window.open;if(Ea.is.cordova===!0){if(cordova!==void 0&&cordova.InAppBrowser!==void 0&&cordova.InAppBrowser.open!==void 0)l=cordova.InAppBrowser.open;else if(navigator!==void 0&&navigator.app!==void 0)return navigator.app.loadUrl(e,{openExternal:!0})}const a=l(e,"_blank",Dg(n));if(a)return Ea.is.desktop&&a.focus(),a;t&&t()}var J0=(e,t,n)=>{if(Ea.is.ios===!0&&window.SafariViewController!==void 0){window.SafariViewController.isAvailable(l=>{l?window.SafariViewController.show({url:e},it,t):Xi(e,t,n)});return}return Xi(e,t,n)};function Ig(e){const t=Array.isArray(e);if(t===!0){const a=e.length;return{isList:t,totalJobs:a,resultAggregator:Array(a).fill(null)}}const n=Object.keys(e),l={};return n.forEach(a=>{l[a]=null}),{isList:t,totalJobs:n.length,resultAggregator:l,resultKeys:n}}function eb(e,{threadsNumber:t=1,abortOnFail:n=!0}={}){let l=-1,a=!1;const{isList:o,totalJobs:i,resultAggregator:u,resultKeys:d}=Ig(e),f=()=>new Promise((h,m)=>{function v(){const g=++l;if(a===!0||g>=i){h();return}const _=o===!0?g:d[g];e[_](u).then(p=>{if(a===!0){h();return}u[_]={key:_,status:"fulfilled",value:p},setTimeout(v)}).catch(p=>{if(a===!0){h();return}const k={key:_,status:"rejected",reason:p};if(u[_]=k,n===!0){a=!0,m({...k,resultAggregator:u});return}setTimeout(v)})}v()}),c=Array(t).fill(f());return Promise.all(c).then(()=>u)}var tb={version:"2.16.8",install:Yc,lang:Ll,iconSet:ou};export{A0 as AddressbarColor,z0 as AppFullscreen,O0 as AppVisibility,R0 as BottomSheet,T0 as ClosePopup,F0 as Cookies,Oc as Dark,V0 as Dialog,G0 as EventBus,ou as IconSet,kv as Intersection,Ll as Lang,D0 as Loading,I0 as LoadingBar,Q0 as LocalStorage,H0 as Meta,M0 as Morph,$0 as Mutation,N0 as Notify,Ea as Platform,td as QAjaxBar,sd as QAvatar,Kg as QBadge,Wg as QBanner,Ug as QBar,Yg as QBreadcrumbs,Xg as QBreadcrumbsEl,Xe as QBtn,Kd as QBtnDropdown,Tu as QBtnGroup,Jg as QBtnToggle,Vu as QCard,Wd as QCardActions,Mn as QCardSection,eh as QCarousel,nh as QCarouselControl,th as QCarouselSlide,ah as QChatMessage,$a as QCheckbox,ju as QChip,Uu as QCircularProgress,rh as QColor,uh as QDate,Dl as QDialog,sh as QDrawer,ch as QEditor,dh as QExpansionItem,fh as QFab,vh as QFabAction,hv as QField,mh as QFile,gh as QFooter,hh as QForm,bh as QFormChildMixin,yh as QHeader,je as QIcon,ph as QImg,wh as QInfiniteScroll,_h as QInnerLoading,$s as QInput,Sh as QIntersection,Il as QItem,$o as QItemLabel,gn as QItemSection,xh as QKnob,Ch as QLayout,Rv as QLinearProgress,qv as QList,Bv as QMarkupTable,Fl as QMenu,kh as QNoSsr,zv as QOptionGroup,qh as QPage,Th as QPageContainer,Mh as QPageScroller,$h as QPageSticky,Bh as QPagination,Ph as QParallax,Eh as QPopupEdit,Lh as QPopupProxy,Ah as QPullToRefresh,Ev as QRadio,zh as QRange,Oh as QRating,Vn as QResizeObserver,Rh as QResponsive,b0 as QRouteTab,Fh as QScrollArea,Ps as QScrollObserver,Hv as QSelect,Dn as QSeparator,Vh as QSkeleton,Dh as QSlideItem,yr as QSlideTransition,Yn as QSlider,Ih as QSpace,It as QSpinner,Hh as QSpinnerAudio,Nh as QSpinnerBall,Qh as QSpinnerBars,jh as QSpinnerBox,Kh as QSpinnerClock,Wh as QSpinnerComment,Uh as QSpinnerCube,Yh as QSpinnerDots,Xh as QSpinnerFacebook,Gh as QSpinnerGears,Zh as QSpinnerGrid,Jh as QSpinnerHearts,e0 as QSpinnerHourglass,t0 as QSpinnerInfinity,n0 as QSpinnerIos,a0 as QSpinnerOrbit,l0 as QSpinnerOval,o0 as QSpinnerPie,r0 as QSpinnerPuff,i0 as QSpinnerRadio,u0 as QSpinnerRings,s0 as QSpinnerTail,c0 as QSplitter,d0 as QStep,f0 as QStepper,v0 as QStepperNavigation,ya as QTab,Jl as QTabPanel,df as QTabPanels,m0 as QTable,Yr as QTabs,h0 as QTd,hm as QTh,y0 as QTime,p0 as QTimeline,w0 as QTimelineEntry,Lv as QToggle,_0 as QToolbar,S0 as QToolbarTitle,Jf as QTooltip,g0 as QTr,x0 as QTree,C0 as QUploader,k0 as QUploaderAddTrigger,q0 as QVideo,pm as QVirtualScroll,tb as Quasar,Ol as Ripple,zc as Screen,P0 as Scroll,B0 as ScrollFire,j0 as SessionStorage,E0 as TouchHold,Ft as TouchPan,L0 as TouchRepeat,Gd as TouchSwipe,Pa as clone,oh as colors,Y0 as copyToClipboard,X0 as createMetaMixin,Qm as createUploaderComponent,ih as date,fa as debounce,Gg as dom,Ng as event,Z0 as exportFile,br as extend,jg as format,so as frameDebounce,dg as getCssVar,Qg as is,ng as morph,it as noop,J0 as openURL,lh as patterns,eb as runSequentialPromises,Zg as scroll,Rc as setCssVar,Cu as throttle,Fa as uid,gc as useDialogPluginComponent,fv as useFormChild,Zu as useHydration,Vl as useId,U0 as useInterval,K0 as useMeta,W0 as useQuasar,Wa as useRenderCache,ps as useSplitAttrs,ia as useTick,hn as useTimeout};
