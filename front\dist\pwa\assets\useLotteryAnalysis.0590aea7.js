var y=Object.defineProperty;var m=(s,e,a)=>e in s?y(s,e,{enumerable:!0,configurable:!0,writable:!0,value:a}):s[e]=a;var o=(s,e,a)=>(m(s,typeof e!="symbol"?e+"":e,a),a);import{h as t,C as w,bc as C,bd as v,r as k,c as z}from"./index.8af2c4ae.js";const A=[t("circle",{cx:"15",cy:"15",r:"15"},[t("animate",{attributeName:"r",from:"15",to:"15",begin:"0s",dur:"0.8s",values:"15;9;15",calcMode:"linear",repeatCount:"indefinite"}),t("animate",{attributeName:"fill-opacity",from:"1",to:"1",begin:"0s",dur:"0.8s",values:"1;.5;1",calcMode:"linear",repeatCount:"indefinite"})]),t("circle",{cx:"60",cy:"15",r:"9","fill-opacity":".3"},[t("animate",{attributeName:"r",from:"9",to:"9",begin:"0s",dur:"0.8s",values:"9;15;9",calcMode:"linear",repeatCount:"indefinite"}),t("animate",{attributeName:"fill-opacity",from:".5",to:".5",begin:"0s",dur:"0.8s",values:".5;1;.5",calcMode:"linear",repeatCount:"indefinite"})]),t("circle",{cx:"105",cy:"15",r:"15"},[t("animate",{attributeName:"r",from:"15",to:"15",begin:"0s",dur:"0.8s",values:"15;9;15",calcMode:"linear",repeatCount:"indefinite"}),t("animate",{attributeName:"fill-opacity",from:"1",to:"1",begin:"0s",dur:"0.8s",values:"1;.5;1",calcMode:"linear",repeatCount:"indefinite"})])];var P=w({name:"QSpinnerDots",props:C,setup(s){const{cSize:e,classes:a}=v(s);return()=>t("svg",{class:a.value,fill:"currentColor",width:e.value,height:e.value,viewBox:"0 0 120 30",xmlns:"http://www.w3.org/2000/svg"},A)}});class S{constructor(e){o(this,"worker",null);o(this,"isAnalyzing",!1);o(this,"warningCallback",null);o(this,"progressCallback",null);this.drawResults=e}async analyzeBatch(e){if(this.isAnalyzing)throw new Error("Analysis is already in progress");return this.isAnalyzing=!0,new Promise((a,i)=>{try{this.worker=new Worker("/assets/analyzer.worker.e5120479.js",{type:"module"}),this.worker.onmessage=c=>{const{type:g,data:n,occurrences:p,matchData:f}=c.data;switch(g){case"progress":this.progressCallback&&this.progressCallback(n);break;case"warning":this.warningCallback&&this.warningCallback(n),console.warn("Worker \u8B66\u544A:",n);break;case"log":console.log(n);break;case"debug":console.log("\u8A08\u7B97\u7D71\u8A08\u4FE1\u606F:",n);break;case"complete":this.progressCallback&&this.progressCallback(n),this.cleanupWorker(),this.isAnalyzing=!1,a({data:n,occurrences:p,matchData:f});break;case"error":this.cleanupWorker(),this.isAnalyzing=!1,i(new Error(n.message));break}},this.worker.postMessage({type:"init",data:{results:JSON.stringify(this.drawResults),config:JSON.stringify(e)}})}catch(c){this.cleanupWorker(),this.isAnalyzing=!1,i(c)}})}setProgressCallback(e){e&&(this.progressCallback=e)}setWarningCallback(e){this.warningCallback=e}cleanupWorker(){this.worker&&(this.worker.terminate(),this.worker=null)}stopWorker(){this.cleanupWorker(),this.isAnalyzing=!1}}const R=s=>{const e=k(s||[]),a=k({firstGroupSize:1,secondGroupSize:1,targetGroupSize:1,maxRange:20,lookAheadCount:1}),i=z(()=>new S(e.value));return{drawResults:e,analysisConfig:a,analyzeWithProgress:async(r,l)=>(i.value.setProgressCallback(r),l&&i.value.setWarningCallback(l),i.value.analyzeBatch(a.value)),stopAnalyzer:()=>{i.value.stopWorker()},init:(r,l)=>{a.value={...a.value,...r},e.value=l},setConfig:r=>{a.value={...a.value,...r}},setResults:r=>{e.value=r},getTailSet:(r,l)=>{const h=new Set,b=[...r.draw_number_size];!l&&r.special_number&&b.push(r.special_number);for(const u of b)h.add(u%10);return Array.from(h).sort((u,d)=>u===0?1:d===0?-1:u-d)}}};export{P as Q,R as u};
