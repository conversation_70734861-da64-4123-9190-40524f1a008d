<template>
  <q-page class="justify-center q-pa-md">
    <div class="text-center q-mb-lg">
      <h4 class="text-h4 text-weight-bold q-my-md">最新開獎結果</h4>
    </div>

    <!-- 載入中狀態 -->
    <div v-if="loading" class="text-center q-py-xl">
      <q-spinner-dots size="50px" color="primary" />
      <div class="text-h6 q-mt-md">載入中...</div>
    </div>

    <!-- 開獎結果卡片 -->
    <div v-else class="row q-col-gutter-md">
      <div
        v-for="(result, drawType) in latestResults"
        :key="drawType"
        class="col-12"
      >
        <q-card
          class="lotto-result-card cursor-pointer"
          @click="goToLottoDetail(drawType)"
        >
          <q-card-section class="q-pa-lg">
            <!-- 彩種名稱 -->
            <div class="text-center q-mb-md">
              <div class="lotto-type-name text-h5 text-weight-bold">
                {{ getLottoTypeName(drawType) }}
              </div>
            </div>

            <!-- 期號和日期 -->
            <div class="text-center q-mb-md">
              <div class="period-info">
                <div class="text-h6 text-weight-bold text-primary">
                  第 {{ result.period }} 期
                </div>
                <div class="text-subtitle2 text-grey-7">
                  {{ result.draw_date }}
                </div>
              </div>
            </div>

            <!-- 開獎號碼 -->
            <div class="numbers-section">
              <div class="row justify-center q-gutter-xs">
                <div
                  v-for="number in result.draw_number_size"
                  :key="number"
                  class="col-auto"
                >
                  <div class="ball">
                    {{ paddingZero(number) }}
                  </div>
                </div>
                <div
                  v-if="result.special_number"
                  class="col-auto"
                >
                  <div class="ball special-number">
                    {{ paddingZero(result.special_number) }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 查看更多按鈕 -->
            <div class="text-center q-mt-md">
              <q-btn
                label="查看歷史記錄"
                color="primary"
                outline
                rounded
                class="text-weight-bold"
                @click.stop="goToLottoDetail(drawType)"
              />
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- 無資料狀態 -->
    <div v-if="!loading && Object.keys(latestResults).length === 0" class="text-center q-py-xl">
      <q-icon name="info" size="60px" color="grey-5" />
      <div class="text-h6 q-mt-md text-grey-7">暫無開獎資料</div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { LOTTO_API } from '@/api';
import { LottoItem } from '@/api/modules/lotto';
import { paddingZero } from '@/utils';

defineOptions({
  name: 'LottoResultsPage',
});

const router = useRouter();

const loading = ref(true);
const latestResults = ref<Record<string, LottoItem>>({});

// 獲取彩種名稱
const getLottoTypeName = (drawType: string): string => {
  switch (drawType) {
    case 'super_lotto638':
      return '威力彩';
    case 'lotto649':
      return '大樂透';
    case 'daily539':
      return '今彩539';
    case 'lotto_hk':
      return '六合彩';
    default:
      return '';
  }
};

// 獲取最新開獎結果
const fetchLatestResults = async () => {
  try {
    loading.value = true;
    const { data } = await LOTTO_API.getLatestResults();
    latestResults.value = data;
  } catch (error) {
    console.error('獲取最新開獎結果失敗:', error);
  } finally {
    loading.value = false;
  }
};

// 跳轉到彩種詳細頁面
const goToLottoDetail = (drawType: string) => {
  router.push(`/lotto-detail/${drawType}`);
};

onMounted(() => {
  fetchLatestResults();
});
</script>

<style lang="scss" scoped>
.lotto-result-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 2px solid #e9ecef;
  border-radius: 16px;
  transition: all 0.3s ease;
  min-height: 280px;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #007bff;
  }

  &::before {
    content: '';
    width: 6px;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    border-top-left-radius: 14px;
    border-bottom-left-radius: 14px;
    background: linear-gradient(180deg, #da8359 0%, #ff6b35 100%);
  }
}

.lotto-type-name {
  color: #2c3e50;
  font-size: 1.4rem;
}

.period-info {
  .text-h6 {
    font-size: 1.3rem;
  }

  .text-subtitle2 {
    font-size: 1rem;
  }
}

.numbers-section {
  .ball {
    width: 2.8rem;
    height: 2.8rem;
    font-size: 1.2rem;
    margin: 0.2rem;
  }
}

// 響應式設計
@media (max-width: 1199px) {
  .lotto-type-name {
    font-size: 1.3rem;
  }

  .period-info {
    .text-h6 {
      font-size: 1.2rem;
    }
  }

  .numbers-section {
    .ball {
      width: 2.6rem;
      height: 2.6rem;
      font-size: 1.1rem;
    }
  }
}

@media (max-width: 768px) {
  .lotto-type-name {
    font-size: 1.2rem;
  }

  .period-info {
    .text-h6 {
      font-size: 1.1rem;
    }

    .text-subtitle2 {
      font-size: 0.9rem;
    }
  }

  .numbers-section {
    .ball {
      width: 2.4rem;
      height: 2.4rem;
      font-size: 1rem;
    }
  }

  .lotto-result-card {
    min-height: 260px;
  }
}

@media (max-width: 480px) {
  .numbers-section {
    .ball {
      width: 2.2rem;
      height: 2.2rem;
      font-size: 0.9rem;
      margin: 0.1rem;
    }
  }

  .lotto-result-card {
    min-height: 240px;
  }
}
</style>
